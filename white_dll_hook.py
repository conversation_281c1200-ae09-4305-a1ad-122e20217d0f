#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
white.dll Hook脚本 - 绕过24小时限制
使用pymem库进行运行时Hook
"""

import pymem
import pymem.process
import time
import struct

def hook_white_dll():
    """Hook white.dll的验证函数"""
    try:
        # 连接到游戏进程
        pm = pymem.Pymem("Prmain.exe")
        print("✅ 已连接到游戏进程")
        
        # 获取white.dll模块
        white_dll = None
        for module in pm.list_modules():
            if "white.dll" in module.name.lower():
                white_dll = module
                break
        
        if not white_dll:
            print("❌ 未找到white.dll模块")
            return False
        
        print(f"✅ 找到white.dll: 基址=0x{white_dll.lpBaseOfDll:08X}")
        
        # Hook策略1: 查找并修改24小时常量
        base_addr = white_dll.lpBaseOfDll
        
        # 读取DLL内存
        dll_data = pm.read_bytes(base_addr, white_dll.SizeOfImage)
        
        # 查找24的DWORD表示
        pattern_24 = struct.pack('<L', 24)
        offset = dll_data.find(pattern_24)
        
        if offset != -1:
            addr = base_addr + offset
            print(f"🎯 找到24小时常量在: 0x{addr:08X}")
            
            # 修改为1
            pm.write_int(addr, 1)
            print("✅ 已将24小时修改为1小时")
            
            return True
        else:
            print("❓ 未找到24小时常量")
            return False
            
    except Exception as e:
        print(f"❌ Hook失败: {e}")
        return False

def main():
    print("=" * 50)
    print("🎮 white.dll Hook工具")
    print("=" * 50)
    
    print("⚠️ 请确保游戏正在运行...")
    input("按回车键开始Hook...")
    
    if hook_white_dll():
        print("\n✅ Hook成功! 24小时限制已绕过")
        print("💡 现在可以测试物品转让功能")
    else:
        print("\n❌ Hook失败，请检查游戏是否运行")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
