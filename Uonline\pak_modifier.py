#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PAK文件修改器 - 修改真正的游戏数据
"""

import os
import re
import shutil
from datetime import datetime

def backup_file(filepath):
    """备份文件"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{filepath}.backup_{timestamp}"
    shutil.copy2(filepath, backup_path)
    print(f"备份创建: {backup_path}")
    return backup_path

def modify_pak_file(pak_path):
    """修改PAK文件中的HP数据"""
    if not os.path.exists(pak_path):
        print(f"文件不存在: {pak_path}")
        return False
        
    # 创建备份
    backup_path = backup_file(pak_path)
    
    try:
        # 读取文件
        with open(pak_path, 'rb') as f:
            data = f.read()
            
        print(f"原始文件大小: {len(data)} 字节")
        
        # 查找并修改HP+数据
        modifications = 0
        new_data = bytearray(data)
        
        # 查找HP+模式并增强
        i = 0
        while i < len(new_data) - 10:
            if new_data[i:i+3] == b'HP+':
                # 提取数字
                j = i + 3
                num_bytes = bytearray()
                while j < len(new_data) and chr(new_data[j]).isdigit():
                    num_bytes.append(new_data[j])
                    j += 1
                    
                if num_bytes:
                    try:
                        original_value = int(num_bytes.decode())
                        # 增强HP值 (乘以10)
                        new_value = min(original_value * 10, 999999)
                        new_value_str = str(new_value).encode()
                        
                        # 替换数值
                        if len(new_value_str) <= len(num_bytes):
                            # 如果新值更短，用0填充
                            padded_value = new_value_str.ljust(len(num_bytes), b'0')
                            new_data[i+3:i+3+len(num_bytes)] = padded_value
                            modifications += 1
                            print(f"修改 HP+{original_value} -> HP+{new_value}")
                            
                    except ValueError:
                        pass
                        
            i += 1
            
        # 查找24小时限制并移除
        hour_patterns = [
            b'24\x00\x00\x00',  # 24小时的二进制表示
            b'\x18\x00\x00\x00',  # 24的小端序
            b'24',  # 直接的24
        ]
        
        for pattern in hour_patterns:
            count = new_data.count(pattern)
            if count > 0:
                # 替换为1小时
                if pattern == b'24\x00\x00\x00':
                    new_data = new_data.replace(pattern, b'01\x00\x00\x00')
                elif pattern == b'\x18\x00\x00\x00':
                    new_data = new_data.replace(pattern, b'\x01\x00\x00\x00')
                else:
                    new_data = new_data.replace(pattern, b'01')
                print(f"移除24小时限制: {count}处")
                modifications += count
                
        # 写入修改后的文件
        with open(pak_path, 'wb') as f:
            f.write(new_data)
            
        print(f"完成修改: {modifications}处变更")
        print(f"新文件大小: {len(new_data)} 字节")
        
        return True
        
    except Exception as e:
        print(f"修改失败: {e}")
        # 恢复备份
        if os.path.exists(backup_path):
            shutil.copy2(backup_path, pak_path)
            print("已恢复原始文件")
        return False

def main():
    """主函数"""
    print("PAK文件修改器 - 修改真正的游戏数据")
    print("=" * 50)
    
    # 要修改的PAK文件
    pak_files = [
        'item0001.pak',
        'item0002.pak', 
        'item0003.pak',
        'etc.pak'
    ]
    
    success_count = 0
    
    for pak_file in pak_files:
        print(f"\n处理: {pak_file}")
        if modify_pak_file(pak_file):
            success_count += 1
        else:
            print(f"跳过: {pak_file}")
            
    print(f"\n修改完成: {success_count}/{len(pak_files)} 个文件成功修改")
    print("\n重要提示:")
    print("1. 已创建备份文件，如有问题可以恢复")
    print("2. 重启游戏以加载修改后的数据")
    print("3. 如果游戏崩溃，请恢复备份文件")

if __name__ == "__main__":
    main()
