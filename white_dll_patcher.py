#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
white.dll 24小时限制补丁工具
基于分析结果，修改24小时相关常量
"""

import os
import shutil
import struct
from pathlib import Path

def backup_file(file_path):
    """备份原始文件"""
    backup_path = file_path + ".backup"
    if not os.path.exists(backup_path):
        shutil.copy2(file_path, backup_path)
        print(f"✅ 已备份原始文件: {backup_path}")
        return True
    else:
        print(f"ℹ️ 备份文件已存在: {backup_path}")
        return True

def patch_24_hour_constants(file_path, dry_run=True):
    """修改24小时相关常量"""
    print(f"🔧 {'模拟' if dry_run else '实际'}修改24小时常量...")
    
    try:
        with open(file_path, 'rb') as f:
            data = bytearray(f.read())
        
        modifications = []
        
        # 要修改的常量
        patches = [
            {
                'name': '24小时 (DWORD LE)',
                'original': struct.pack('<L', 24),  # 24 as little-endian DWORD
                'replacement': struct.pack('<L', 1),  # 改为1
                'description': '将24小时改为1小时'
            },
            {
                'name': '24小时 (WORD LE)', 
                'original': struct.pack('<H', 24),  # 24 as little-endian WORD
                'replacement': struct.pack('<H', 1),  # 改为1
                'description': '将24小时改为1小时'
            },
            {
                'name': '86400秒 (DWORD LE)',
                'original': struct.pack('<L', 86400),  # 86400 seconds
                'replacement': struct.pack('<L', 60),    # 改为60秒
                'description': '将24小时(86400秒)改为1分钟(60秒)'
            },
            {
                'name': '1440分钟 (DWORD LE)',
                'original': struct.pack('<L', 1440),   # 1440 minutes
                'replacement': struct.pack('<L', 1),     # 改为1分钟
                'description': '将24小时(1440分钟)改为1分钟'
            }
        ]
        
        total_changes = 0
        
        for patch in patches:
            original = patch['original']
            replacement = patch['replacement']
            count = 0
            
            # 查找并替换所有匹配项
            pos = 0
            while True:
                pos = data.find(original, pos)
                if pos == -1:
                    break
                
                if not dry_run:
                    data[pos:pos+len(original)] = replacement
                
                modifications.append({
                    'offset': f'0x{pos:08X}',
                    'patch_name': patch['name'],
                    'description': patch['description']
                })
                
                count += 1
                total_changes += 1
                pos += len(original)
            
            if count > 0:
                print(f"  🎯 {patch['name']}: 找到 {count} 处")
        
        if not dry_run and total_changes > 0:
            # 写入修改后的文件
            with open(file_path, 'wb') as f:
                f.write(data)
            print(f"✅ 已应用 {total_changes} 处修改")
        elif dry_run:
            print(f"ℹ️ 模拟模式: 将修改 {total_changes} 处")
        
        return modifications, total_changes
        
    except Exception as e:
        print(f"❌ 修改失败: {e}")
        return [], 0

def create_dll_hook_script(dll_path):
    """创建DLL Hook脚本"""
    print(f"🔧 创建DLL Hook脚本...")
    
    hook_script = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
white.dll Hook脚本 - 绕过24小时限制
使用pymem库进行运行时Hook
"""

import pymem
import pymem.process
import time
import struct

def hook_white_dll():
    """Hook white.dll的验证函数"""
    try:
        # 连接到游戏进程
        pm = pymem.Pymem("Prmain.exe")
        print("✅ 已连接到游戏进程")
        
        # 获取white.dll模块
        white_dll = None
        for module in pm.list_modules():
            if "white.dll" in module.name.lower():
                white_dll = module
                break
        
        if not white_dll:
            print("❌ 未找到white.dll模块")
            return False
        
        print(f"✅ 找到white.dll: 基址=0x{{white_dll.lpBaseOfDll:08X}}")
        
        # Hook策略1: 查找并修改24小时常量
        base_addr = white_dll.lpBaseOfDll
        
        # 读取DLL内存
        dll_data = pm.read_bytes(base_addr, white_dll.SizeOfImage)
        
        # 查找24的DWORD表示
        pattern_24 = struct.pack('<L', 24)
        offset = dll_data.find(pattern_24)
        
        if offset != -1:
            addr = base_addr + offset
            print(f"🎯 找到24小时常量在: 0x{{addr:08X}}")
            
            # 修改为1
            pm.write_int(addr, 1)
            print("✅ 已将24小时修改为1小时")
            
            return True
        else:
            print("❓ 未找到24小时常量")
            return False
            
    except Exception as e:
        print(f"❌ Hook失败: {{e}}")
        return False

def main():
    print("=" * 50)
    print("🎮 white.dll Hook工具")
    print("=" * 50)
    
    print("⚠️ 请确保游戏正在运行...")
    input("按回车键开始Hook...")
    
    if hook_white_dll():
        print("\\n✅ Hook成功! 24小时限制已绕过")
        print("💡 现在可以测试物品转让功能")
    else:
        print("\\n❌ Hook失败，请检查游戏是否运行")
    
    input("\\n按回车键退出...")

if __name__ == "__main__":
    main()
'''
    
    hook_file = "white_dll_hook.py"
    with open(hook_file, 'w', encoding='utf-8') as f:
        f.write(hook_script)
    
    print(f"✅ 已创建Hook脚本: {hook_file}")
    return hook_file

def main():
    white_dll_path = "Uonline/white.dll"
    
    print("=" * 70)
    print("🔧 white.dll 24小时限制补丁工具")
    print("=" * 70)
    
    if not os.path.exists(white_dll_path):
        print(f"❌ 文件不存在: {white_dll_path}")
        return
    
    print(f"📁 目标文件: {white_dll_path}")
    print(f"📊 文件大小: {os.path.getsize(white_dll_path):,} 字节")
    
    # 备份原始文件
    if not backup_file(white_dll_path):
        print("❌ 备份失败，停止操作")
        return
    
    print("\\n" + "-" * 50)
    
    # 模拟修改 (dry run)
    print("🔍 分析需要修改的位置...")
    modifications, change_count = patch_24_hour_constants(white_dll_path, dry_run=True)
    
    if change_count == 0:
        print("❓ 未找到需要修改的24小时常量")
        return
    
    print(f"\\n📋 将要修改的位置 ({change_count} 处):")
    for mod in modifications[:10]:  # 只显示前10个
        print(f"  - {mod['offset']}: {mod['patch_name']} - {mod['description']}")
    
    if len(modifications) > 10:
        print(f"  ... 还有 {len(modifications) - 10} 处修改")
    
    print("\\n" + "-" * 50)
    
    # 询问用户是否继续
    choice = input("🤔 是否应用这些修改? (y/n): ").lower().strip()
    
    if choice == 'y':
        print("\\n🔧 应用修改...")
        modifications, change_count = patch_24_hour_constants(white_dll_path, dry_run=False)
        
        if change_count > 0:
            print(f"\\n✅ 修改完成! 共修改了 {change_count} 处")
            print("\\n💡 测试步骤:")
            print("  1. 启动游戏")
            print("  2. 测试物品转让功能")
            print("  3. 检查是否还有24小时限制")
            print("\\n⚠️ 如果修改无效，可以:")
            print("  1. 恢复备份文件")
            print("  2. 尝试Hook方法")
            print("  3. 分析网络协议")
        else:
            print("❌ 修改失败")
    else:
        print("❌ 用户取消修改")
    
    print("\\n" + "-" * 50)
    
    # 创建Hook脚本作为备选方案
    print("🔧 创建Hook脚本作为备选方案...")
    hook_file = create_dll_hook_script(white_dll_path)
    
    print("\\n" + "=" * 70)
    print("📊 操作总结")
    print("=" * 70)
    
    print(f"\\n✅ 完成的操作:")
    print(f"  - 备份原始文件: white.dll.backup")
    if choice == 'y':
        print(f"  - 修改DLL文件: {change_count} 处")
    print(f"  - 创建Hook脚本: {hook_file}")
    
    print(f"\\n🚀 下一步:")
    print(f"  1. 测试修改后的游戏")
    print(f"  2. 如果无效，运行Hook脚本")
    print(f"  3. 如果仍无效，分析网络协议")
    
    print(f"\\n⚠️ 恢复方法:")
    print(f"  如果游戏出现问题，删除white.dll并将white.dll.backup重命名为white.dll")

if __name__ == "__main__":
    main()
    input("\\n按回车键退出...")
