#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Uonline 实际修改器 - 真正能用的内存修改工具
需要安装: pip install pymem psutil
"""

import pymem
import pymem.process
import psutil
import struct
import time
import ctypes
from ctypes import wintypes

class UonlineTrainer:
    def __init__(self):
        self.pm = None
        self.process_name = "Prmain.exe"
        self.base_address = None
        
    def find_process(self):
        """查找游戏进程"""
        try:
            for proc in psutil.process_iter(['pid', 'name']):
                if proc.info['name'].lower() == self.process_name.lower():
                    print(f"✅ 找到游戏进程: PID {proc.info['pid']}")
                    return proc.info['pid']
            return None
        except Exception as e:
            print(f"❌ 查找进程失败: {e}")
            return None
    
    def attach_process(self):
        """附加到游戏进程"""
        pid = self.find_process()
        if not pid:
            print("❌ 游戏未运行，请先启动游戏")
            return False
        
        try:
            self.pm = pymem.Pymem(pid)
            self.base_address = self.pm.base_address
            print(f"✅ 成功附加到进程，基址: 0x{self.base_address:08X}")
            return True
        except Exception as e:
            print(f"❌ 附加进程失败: {e}")
            return False
    
    def scan_memory_pattern(self, pattern, start_addr=None, end_addr=None):
        """扫描内存模式"""
        if not self.pm:
            return []
        
        if start_addr is None:
            start_addr = self.base_address
        if end_addr is None:
            end_addr = self.base_address + 0x1000000  # 16MB范围
        
        results = []
        try:
            current_addr = start_addr
            while current_addr < end_addr:
                try:
                    # 读取4KB块
                    chunk_size = 4096
                    data = self.pm.read_bytes(current_addr, chunk_size)
                    
                    # 在块中查找模式
                    offset = 0
                    while True:
                        pos = data.find(pattern, offset)
                        if pos == -1:
                            break
                        
                        addr = current_addr + pos
                        results.append(addr)
                        offset = pos + 1
                        
                        if len(results) >= 100:  # 限制结果数量
                            return results
                    
                    current_addr += chunk_size
                    
                except:
                    current_addr += 4096
                    continue
                    
        except Exception as e:
            print(f"扫描错误: {e}")
        
        return results
    
    def find_server_ip_in_memory(self):
        """在内存中查找服务器IP"""
        print("🔍 在内存中查找服务器IP **************...")
        
        # IP的字节表示
        ip_bytes = struct.pack('BBBB', 121, 40, 205, 138)
        ip_bytes_rev = struct.pack('BBBB', 138, 205, 40, 121)
        
        # 字符串形式
        ip_string = b"**************"
        
        patterns = [
            (ip_bytes, "二进制IP (大端)"),
            (ip_bytes_rev, "二进制IP (小端)"),
            (ip_string, "字符串IP")
        ]
        
        for pattern, desc in patterns:
            results = self.scan_memory_pattern(pattern)
            if results:
                print(f"  {desc}: 找到 {len(results)} 个位置")
                for i, addr in enumerate(results[:5]):  # 只显示前5个
                    print(f"    0x{addr:08X}")
                    try:
                        # 读取周围数据
                        context = self.pm.read_bytes(addr - 8, 24)
                        print(f"      上下文: {context.hex()}")
                    except:
                        pass
    
    def find_port_4001_in_memory(self):
        """在内存中查找端口4001"""
        print("\n🔌 在内存中查找端口4001...")
        
        port_big = struct.pack('>H', 4001)    # 0x0FA1
        port_little = struct.pack('<H', 4001) # 0xA10F
        port_string = b"4001"
        
        patterns = [
            (port_big, "端口 (大端)"),
            (port_little, "端口 (小端)"),
            (port_string, "端口字符串")
        ]
        
        for pattern, desc in patterns:
            results = self.scan_memory_pattern(pattern)
            if results:
                print(f"  {desc}: 找到 {len(results)} 个位置")
                for addr in results[:3]:
                    print(f"    0x{addr:08X}")
    
    def scan_for_player_data(self):
        """扫描可能的玩家数据"""
        print("\n👤 扫描玩家数据模式...")
        
        # 常见的游戏数值模式
        common_values = [
            (1000, "可能的金币/经验"),
            (100, "可能的HP/MP"),
            (50, "可能的等级"),
            (9999, "可能的最大值"),
        ]
        
        for value, desc in common_values:
            pattern = struct.pack('<I', value)  # 32位小端整数
            results = self.scan_memory_pattern(pattern)
            if results:
                print(f"  {desc} ({value}): 找到 {len(results)} 个位置")
                for addr in results[:3]:
                    print(f"    0x{addr:08X}")
    
    def monitor_memory_changes(self, address, size=4):
        """监控内存地址的变化"""
        print(f"\n👁️ 监控地址 0x{address:08X} 的变化...")
        
        try:
            last_value = None
            for i in range(10):  # 监控10次
                try:
                    if size == 4:
                        current_value = self.pm.read_int(address)
                    elif size == 2:
                        current_value = self.pm.read_short(address)
                    else:
                        current_value = self.pm.read_bytes(address, size)
                    
                    if last_value is not None and current_value != last_value:
                        print(f"  变化: {last_value} -> {current_value}")
                    
                    last_value = current_value
                    time.sleep(1)
                    
                except Exception as e:
                    print(f"  读取失败: {e}")
                    break
                    
        except KeyboardInterrupt:
            print("  监控停止")
    
    def modify_memory_value(self, address, new_value, value_type='int'):
        """修改内存值"""
        try:
            if value_type == 'int':
                old_value = self.pm.read_int(address)
                self.pm.write_int(address, new_value)
                print(f"✅ 修改成功: 0x{address:08X} {old_value} -> {new_value}")
            elif value_type == 'float':
                old_value = self.pm.read_float(address)
                self.pm.write_float(address, new_value)
                print(f"✅ 修改成功: 0x{address:08X} {old_value} -> {new_value}")
            elif value_type == 'bytes':
                old_value = self.pm.read_bytes(address, len(new_value))
                self.pm.write_bytes(address, new_value, len(new_value))
                print(f"✅ 修改成功: 0x{address:08X}")
                
        except Exception as e:
            print(f"❌ 修改失败: {e}")
    
    def run_trainer(self):
        """运行修改器主程序"""
        print("=" * 60)
        print("🎯 Uonline 实际修改器")
        print("=" * 60)
        
        if not self.attach_process():
            return
        
        while True:
            print("\n选择功能:")
            print("1. 查找服务器IP")
            print("2. 查找端口4001")
            print("3. 扫描玩家数据")
            print("4. 监控内存地址")
            print("5. 修改内存值")
            print("0. 退出")
            
            try:
                choice = input("\n请选择: ").strip()
                
                if choice == '1':
                    self.find_server_ip_in_memory()
                elif choice == '2':
                    self.find_port_4001_in_memory()
                elif choice == '3':
                    self.scan_for_player_data()
                elif choice == '4':
                    addr_str = input("输入要监控的地址 (十六进制): 0x")
                    try:
                        addr = int(addr_str, 16)
                        self.monitor_memory_changes(addr)
                    except ValueError:
                        print("❌ 地址格式错误")
                elif choice == '5':
                    addr_str = input("输入要修改的地址 (十六进制): 0x")
                    value_str = input("输入新值: ")
                    try:
                        addr = int(addr_str, 16)
                        value = int(value_str)
                        self.modify_memory_value(addr, value)
                    except ValueError:
                        print("❌ 输入格式错误")
                elif choice == '0':
                    break
                else:
                    print("❌ 无效选择")
                    
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"❌ 错误: {e}")
        
        print("\n👋 修改器退出")

if __name__ == "__main__":
    trainer = UonlineTrainer()
    trainer.run_trainer()
