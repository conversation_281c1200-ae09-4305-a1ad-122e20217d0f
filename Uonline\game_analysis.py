#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天之游侠 (Uonline) 游戏逆向分析工具
基于发现的内存结构和API函数进行深度分析
"""

import pymem
import struct
import time
import json
from typing import Dict, List, Optional, Tuple

class UonlineGameAnalyzer:
    """天之游侠游戏分析器"""
    
    def __init__(self):
        self.process_name = "prmain.exe"
        self.pm = None
        self.base_address = None
        
        # 已知的内存偏移地址（从逆向分析中获得）
        self.offsets = {
            'player_base': 0x253C44,  # 玩家信息基址偏移
            'player_x': 0x8,         # X坐标偏移
            'player_y': 0xC,         # Y坐标偏移  
            'player_z': 0x10,        # Z坐标偏移
            'player_hp': 0x14,       # 血量偏移
            'player_hp_max': 0x18,   # 最大血量偏移
            'player_cp': 0x1C,       # 魔法值偏移
            'player_speed': 0x20,    # 移动速度偏移
            'player_status': 0x24,   # 状态偏移
            'player_race': 0x28,     # 种族偏移
            'player_xp': 0x2C,       # 经验值偏移
        }
        
        # 游戏机制参数（从分析中获得）
        self.game_mechanics = {
            'player_attack_range': 20.47,  # 玩家攻击距离
            'monster_attack_range': 5.09,  # 怪物攻击距离
            'safe_distance': 6.0,          # AI安全距离
            'optimal_distance': 18.0,      # AI最优攻击距离
            'aggro_range': 45.0,           # 怪物仇恨距离
            'chase_range': 45.0,           # 怪物追击距离
        }
        
        # 技能范围修改地址（从CE表中获得）
        self.cheat_addresses = {
            'skill_range': 0x6496EC,       # 技能全屏地址
            'attack_range': 0x649708,      # 攻击全屏地址
            'bullet_count': 0x6496F4,      # 子弹数量地址
        }

    def connect_to_game(self) -> bool:
        """连接到游戏进程"""
        try:
            self.pm = pymem.Pymem(self.process_name)
            self.base_address = self.pm.base_address
            print(f"成功连接到游戏进程: {self.process_name}")
            print(f"基址: {hex(self.base_address)}")
            return True
        except Exception as e:
            print(f"连接游戏失败: {e}")
            return False

    def get_player_info(self) -> Optional[Dict]:
        """获取玩家信息"""
        if not self.pm:
            return None
            
        try:
            # 读取玩家信息基址
            pointer_base = self.pm.read_longlong(self.base_address + self.offsets['player_base'])
            
            player_info = {
                'base_address': hex(pointer_base),
                'x': self.pm.read_float(pointer_base + self.offsets['player_x']),
                'y': self.pm.read_float(pointer_base + self.offsets['player_y']),
                'z': self.pm.read_float(pointer_base + self.offsets['player_z']),
                'hp': self.pm.read_int(pointer_base + self.offsets['player_hp']),
                'hp_max': self.pm.read_int(pointer_base + self.offsets['player_hp_max']),
                'cp': self.pm.read_int(pointer_base + self.offsets['player_cp']),
                'speed': self.pm.read_int(pointer_base + self.offsets['player_speed']),
                'status': self.pm.read_int(pointer_base + self.offsets['player_status']),
                'race': self.pm.read_int(pointer_base + self.offsets['player_race']),
                'xp': self.pm.read_int(pointer_base + self.offsets['player_xp']),
            }
            
            return player_info
            
        except Exception as e:
            print(f"读取玩家信息失败: {e}")
            return None

    def analyze_monster_memory(self, monster_obj_ptr: int) -> Dict:
        """分析怪物内存结构"""
        if not self.pm:
            return {}
            
        try:
            monster_data = {}
            
            # 读取怪物的各种偏移数据（基于逆向分析发现的偏移）
            offsets_to_check = [4, 24, 108, 124, 128, 144, 152, 156, 160, 200]
            
            for offset in offsets_to_check:
                try:
                    value = self.pm.read_int(monster_obj_ptr + offset)
                    monster_data[f'offset_{offset}'] = value
                except:
                    monster_data[f'offset_{offset}'] = None
            
            # 根据分析推测各偏移的含义
            monster_analysis = {
                'level': monster_data.get('offset_4', 0),           # 可能是等级
                'attack_power': monster_data.get('offset_24', 0),   # 可能是攻击力
                'ai_state': monster_data.get('offset_108', 0),      # 可能是AI状态
                'chase_range': monster_data.get('offset_124', 0),   # 可能是追击距离
                'aggro_range': monster_data.get('offset_128', 0),   # 可能是仇恨距离
                'ai_mode': monster_data.get('offset_144', 0),       # 可能是AI模式
                'behavior': monster_data.get('offset_152', 0),      # 可能是行为模式
                'unknown_param': monster_data.get('offset_156', 0), # 未知参数
                'damage_factor': monster_data.get('offset_160', 0), # 可能是伤害系数
                'duplicate_range': monster_data.get('offset_200', 0), # 重复的范围值
            }
            
            return monster_analysis
            
        except Exception as e:
            print(f"分析怪物内存失败: {e}")
            return {}

    def calculate_optimal_position(self, player_pos: Tuple[float, float], 
                                 monster_pos: Tuple[float, float]) -> Tuple[float, float]:
        """计算最优攻击位置"""
        import math
        
        # 计算玩家到怪物的向量
        dx = monster_pos[0] - player_pos[0]
        dy = monster_pos[1] - player_pos[1]
        distance = math.sqrt(dx*dx + dy*dy)
        
        if distance == 0:
            return player_pos
        
        # 单位向量
        unit_x = dx / distance
        unit_y = dy / distance
        
        # 最优距离位置（在安全距离和攻击距离之间）
        optimal_distance = (self.game_mechanics['safe_distance'] + 
                          self.game_mechanics['optimal_distance']) / 2
        
        # 计算最优位置
        optimal_x = monster_pos[0] - unit_x * optimal_distance
        optimal_y = monster_pos[1] - unit_y * optimal_distance
        
        return (optimal_x, optimal_y)

    def enable_cheats(self) -> bool:
        """启用游戏修改（基于CE表）"""
        if not self.pm:
            return False
            
        try:
            # 技能全屏
            self.pm.write_int(self.cheat_addresses['skill_range'], 32384)
            print("已启用技能全屏")
            
            # 攻击全屏  
            self.pm.write_int(self.cheat_addresses['attack_range'], 32384)
            print("已启用攻击全屏")
            
            # 子弹数量修改
            self.pm.write_int(self.cheat_addresses['bullet_count'], 1)
            print("已修改子弹数量")
            
            return True
            
        except Exception as e:
            print(f"启用修改失败: {e}")
            return False

    def teleport_player(self, x: float, z: float) -> bool:
        """瞬移玩家到指定坐标"""
        if not self.pm:
            return False
            
        try:
            pointer_base = self.pm.read_longlong(self.base_address + self.offsets['player_base'])
            
            # 写入新坐标
            self.pm.write_float(pointer_base + self.offsets['player_x'], x)
            self.pm.write_float(pointer_base + self.offsets['player_z'], z)
            
            print(f"瞬移到坐标: X={x:.2f}, Z={z:.2f}")
            return True
            
        except Exception as e:
            print(f"瞬移失败: {e}")
            return False

    def monitor_game_state(self, duration: int = 60):
        """监控游戏状态"""
        print(f"开始监控游戏状态，持续{duration}秒...")
        
        start_time = time.time()
        while time.time() - start_time < duration:
            player_info = self.get_player_info()
            if player_info:
                print(f"玩家状态: HP={player_info['hp']}/{player_info['hp_max']} "
                      f"位置=({player_info['x']:.2f}, {player_info['y']:.2f}, {player_info['z']:.2f}) "
                      f"速度={player_info['speed']} 状态={player_info['status']}")
            
            time.sleep(1)

    def export_analysis_report(self, filename: str = "game_analysis_report.json"):
        """导出分析报告"""
        report = {
            'game_info': {
                'name': '天之游侠 (Uonline)',
                'process': self.process_name,
                'base_address': hex(self.base_address) if self.base_address else None,
            },
            'memory_offsets': self.offsets,
            'game_mechanics': self.game_mechanics,
            'cheat_addresses': {k: hex(v) for k, v in self.cheat_addresses.items()},
            'analysis_timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
        }
        
        # 如果连接到游戏，添加当前玩家信息
        if self.pm:
            player_info = self.get_player_info()
            if player_info:
                report['current_player_state'] = player_info
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"分析报告已导出到: {filename}")

def main():
    """主函数"""
    analyzer = UonlineGameAnalyzer()
    
    print("=== 天之游侠游戏逆向分析工具 ===")
    print("1. 连接游戏")
    print("2. 获取玩家信息")
    print("3. 启用修改")
    print("4. 监控游戏状态")
    print("5. 导出分析报告")
    print("0. 退出")
    
    while True:
        choice = input("\n请选择操作: ").strip()
        
        if choice == '1':
            analyzer.connect_to_game()
        elif choice == '2':
            info = analyzer.get_player_info()
            if info:
                print("玩家信息:")
                for key, value in info.items():
                    print(f"  {key}: {value}")
        elif choice == '3':
            analyzer.enable_cheats()
        elif choice == '4':
            duration = int(input("监控时长(秒): ") or "60")
            analyzer.monitor_game_state(duration)
        elif choice == '5':
            analyzer.export_analysis_report()
        elif choice == '0':
            break
        else:
            print("无效选择")

if __name__ == "__main__":
    main()
