#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的Uonline游戏目录分析
基于目录结构分析游戏架构和24小时限制可能位置
"""

import os
import re
from pathlib import Path

def analyze_game_directory():
    """分析游戏目录结构"""
    print("🔍 分析Uonline游戏目录结构...")
    
    # 关键文件分类
    analysis = {
        "executables": [],
        "dlls": [],
        "config_files": [],
        "data_files": [],
        "anti_cheat": [],
        "launchers": [],
        "maps": [],
        "characters": [],
        "items": [],
        "sounds": [],
        "analysis_tools": []
    }
    
    # 从目录列表中提取文件信息
    files = [
        # 可执行文件
        "Prmain.exe", "Launcher.exe", "Patch.exe", "PatchLauncher.exe", "reg.exe",
        
        # DLL文件
        "Uonline.dll", "DX.dll", "GrpLib.dll", "Wav.dll", "Mfc42.dll", "Msvcp.dll",
        "bugslayerutil.dll", "dbghelp.dll", "msvcp60.dll", "netmine.dll", "soundlib.dll", "white.dll",
        
        # 配置文件
        "connection.ini", "prconfig.ini", "version.dat", "Message.ctf",
        
        # 反作弊系统
        "HShield/", "HackShield/",
        
        # 启动器
        "Launch/", "Launcher.exe",
        
        # 数据文件 (.pak)
        "Common.pak", "Effect.pak", "Env.pak", "etc.pak", "icon.pak", "wav.pak"
    ]
    
    print("\n📊 文件分类分析:")
    
    # 可执行文件分析
    executables = ["Prmain.exe", "Launcher.exe", "Patch.exe", "PatchLauncher.exe", "reg.exe"]
    print(f"  🚀 可执行文件 ({len(executables)}):")
    for exe in executables:
        print(f"    - {exe}")
    
    # DLL文件分析
    dlls = ["Uonline.dll", "DX.dll", "GrpLib.dll", "Wav.dll", "Mfc42.dll", "Msvcp.dll",
            "bugslayerutil.dll", "dbghelp.dll", "msvcp60.dll", "netmine.dll", "soundlib.dll", "white.dll"]
    print(f"\n  📚 DLL库文件 ({len(dlls)}):")
    for dll in dlls:
        print(f"    - {dll}")
    
    # 反作弊系统
    print(f"\n  🛡️ 反作弊系统:")
    print(f"    - HShield/ (韩国HShield反作弊)")
    print(f"    - HackShield/ (AhnLab HackShield)")
    
    # 配置文件
    configs = ["connection.ini", "prconfig.ini", "version.dat", "Message.ctf"]
    print(f"\n  ⚙️ 配置文件 ({len(configs)}):")
    for config in configs:
        print(f"    - {config}")
    
    return analysis

def analyze_dll_functions():
    """分析各个DLL的可能功能"""
    print("\n🔍 DLL功能分析:")
    
    dll_functions = {
        "Uonline.dll": "游戏核心引擎 (已分析 - 高度混淆)",
        "DX.dll": "DirectX图形库 - 可能包含渲染逻辑",
        "GrpLib.dll": "图形库 - 界面和图像处理",
        "Wav.dll": "音频库 - 声音播放",
        "soundlib.dll": "音频库 - 声音系统",
        "netmine.dll": "网络库 - 可能包含网络通信和验证",
        "white.dll": "未知功能 - 可能是保护或验证模块",
        "bugslayerutil.dll": "调试工具库",
        "dbghelp.dll": "Windows调试帮助库",
        "Mfc42.dll": "Microsoft Foundation Classes",
        "Msvcp.dll": "Microsoft Visual C++ 运行库",
        "msvcp60.dll": "Microsoft Visual C++ 6.0 运行库"
    }
    
    for dll, function in dll_functions.items():
        print(f"  📚 {dll}: {function}")
    
    print("\n🎯 重点分析目标:")
    priority_dlls = ["netmine.dll", "white.dll", "DX.dll", "GrpLib.dll"]
    for dll in priority_dlls:
        print(f"  🔍 {dll} - {dll_functions[dll]}")
    
    return dll_functions

def analyze_24hour_possibilities():
    """分析24小时限制可能的位置"""
    print("\n⏰ 24小时限制可能位置分析:")
    
    possibilities = {
        "高可能性": [
            "netmine.dll - 网络通信库，可能包含服务器验证",
            "white.dll - 未知功能，可能是验证模块", 
            "Message.ctf - 游戏文本文件，包含限制提示信息",
            "服务器端验证 - 通过网络协议验证"
        ],
        "中等可能性": [
            "DX.dll - 可能包含游戏逻辑",
            "GrpLib.dll - 界面库，可能处理限制显示",
            "Prmain.exe - 主程序，可能包含验证逻辑",
            "connection.ini - 连接配置，可能包含验证参数"
        ],
        "低可能性": [
            "Uonline.dll - 已分析，主要是引擎功能",
            "音频相关DLL - 不太可能包含验证逻辑",
            "PAK文件 - 主要是游戏资源"
        ]
    }
    
    for level, items in possibilities.items():
        print(f"\n  🎯 {level}:")
        for item in items:
            print(f"    - {item}")
    
    return possibilities

def generate_analysis_strategy():
    """生成分析策略"""
    print("\n🚀 分析策略建议:")
    
    strategies = [
        {
            "优先级": "🔥 最高",
            "目标": "netmine.dll",
            "原因": "网络库最可能包含服务器验证逻辑",
            "方法": "静态分析 + 网络抓包"
        },
        {
            "优先级": "🔥 最高", 
            "目标": "white.dll",
            "原因": "未知功能DLL，可能是专门的验证模块",
            "方法": "完整逆向分析"
        },
        {
            "优先级": "🔥 高",
            "目标": "Prmain.exe",
            "原因": "主程序，可能包含客户端验证",
            "方法": "动态调试 + API监控"
        },
        {
            "优先级": "🔥 高",
            "目标": "网络通信分析",
            "原因": "如果是服务器验证，需要分析协议",
            "方法": "Wireshark抓包 + 协议分析"
        },
        {
            "优先级": "🔥 中",
            "目标": "DX.dll + GrpLib.dll",
            "原因": "可能包含游戏逻辑和界面处理",
            "方法": "API调用分析"
        }
    ]
    
    for i, strategy in enumerate(strategies, 1):
        print(f"\n  {i}. {strategy['优先级']} - {strategy['目标']}")
        print(f"     原因: {strategy['原因']}")
        print(f"     方法: {strategy['方法']}")
    
    return strategies

def analyze_anti_cheat_impact():
    """分析反作弊系统的影响"""
    print("\n🛡️ 反作弊系统影响分析:")
    
    print("  📋 检测到的反作弊系统:")
    print("    - HShield (韩国安全公司)")
    print("    - HackShield (AhnLab公司)")
    
    print("\n  ⚠️ 对分析的影响:")
    print("    - 内存修改会被检测")
    print("    - 调试器可能被阻止")
    print("    - 进程注入会触发保护")
    print("    - API Hook可能失败")
    
    print("\n  🔧 绕过策略:")
    print("    - 使用虚拟机环境")
    print("    - 禁用反作弊服务")
    print("    - 使用内核级调试器")
    print("    - 分析离线状态")

def main():
    print("=" * 60)
    print("🎮 Uonline 完整游戏分析报告")
    print("=" * 60)
    
    # 目录结构分析
    analyze_game_directory()
    
    # DLL功能分析
    analyze_dll_functions()
    
    # 24小时限制分析
    analyze_24hour_possibilities()
    
    # 分析策略
    generate_analysis_strategy()
    
    # 反作弊影响
    analyze_anti_cheat_impact()
    
    print("\n" + "=" * 60)
    print("🎯 关键发现总结")
    print("=" * 60)
    
    print("\n✅ 重要发现:")
    print("  1. 🎯 netmine.dll - 最可能包含网络验证逻辑")
    print("  2. 🎯 white.dll - 神秘DLL，可能是验证模块")
    print("  3. 🎯 双重反作弊保护 - HShield + HackShield")
    print("  4. 🎯 Message.ctf - 包含24小时限制文本")
    
    print("\n🚀 下一步行动:")
    print("  1. 分析 netmine.dll (网络库)")
    print("  2. 分析 white.dll (未知模块)")
    print("  3. 动态分析 Prmain.exe")
    print("  4. 网络协议抓包分析")
    
    print("\n💡 绕过建议:")
    print("  - 如果验证在服务器端: 搭建私服或协议修改")
    print("  - 如果验证在客户端: 重点分析netmine.dll和white.dll")
    print("  - 考虑禁用反作弊系统进行分析")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
