-- 纯攻击脚本 - 使用正确的Hook函数
_Hook技能发包代码()  -- 这是正确的函数名

local 攻击距离 = 15.0
local 检测范围 = 30.0
local 攻击计数器 = 0
local 上次攻击时间 = 0
local 攻击冷却 = 0  -- 100毫秒冷却

function 获取最近怪物()
    local role = _取人物信息()
    local monsters = _取周围怪物信息()
    
    if not monsters or not role or not role.x then
        return nil
    end
    
    local 最近怪物 = nil
    local 最近距离 = 999999
    
    for _, monster in ipairs(monsters) do
        local distance = _取坐标距离(role.x, role.y, monster.x, monster.y)
        if distance < 检测范围 and distance < 最近距离 then
            最近距离 = distance
            最近怪物 = monster
        end
    end
    
    return 最近怪物, 最近距离
end

function 纯攻击循环()
    _Log("开始攻击循环 - 使用正确的Hook函数")
    
    while true do
        if _Exit() == false then 
            break
        end
        
        local role = _取人物信息()
        if role and role.x then
            local monster, distance = 获取最近怪物()
            
            if monster and distance <= 攻击距离 then
                local current_time = os.clock()
                
                -- 检查攻击冷却
                if current_time - 上次攻击时间 >= 攻击冷却 then
                    _普攻(monster.Id)
                    上次攻击时间 = current_time
                    _Log(string.format("普攻: %s, 距离: %.2f", monster.Name, distance))
                end
            end
        end
        
        _Sleep(50)
    end
end

纯攻击循环()