#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天之游侠实用逆向工具 - 提取真实可用数据
"""

import os
import struct
import json
import re
from pathlib import Path

class UonlineReverser:
    def __init__(self):
        self.game_dir = Path(".")
        self.results = {}
        
    def extract_server_info(self):
        """提取真实服务器信息"""
        print("🔍 提取服务器信息...")
        
        # 从connection.ini提取
        conn_file = self.game_dir / "connection.ini"
        if conn_file.exists():
            with open(conn_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                
            # 提取IP和端口
            ip_match = re.search(r'GAME_IP=([0-9.]+)', content)
            port_match = re.search(r'GAME_PORT=(\d+)', content)
            url_match = re.search(r'URL_ACCOUNT=([^\r\n]+)', content)
            
            server_info = {
                'game_ip': ip_match.group(1) if ip_match else None,
                'game_port': int(port_match.group(1)) if port_match else None,
                'account_url': url_match.group(1) if url_match else None
            }
            
            print(f"✅ 游戏服务器: {server_info['game_ip']}:{server_info['game_port']}")
            print(f"✅ 注册地址: {server_info['account_url']}")
            
            self.results['server_info'] = server_info
            
        # 从severlist.xml提取更多服务器
        server_file = self.game_dir / "Launch/severlist.xml"
        if server_file.exists():
            with open(server_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                
            # 提取所有服务器IP
            ips = re.findall(r'<ServerIP>\s*([0-9.]+)\s*</ServerIP>', content)
            ports = re.findall(r'<ServerPort>\s*(\d+)\s*</ServerPort>', content)
            names = re.findall(r'<ServerName>\s*([^<]+)\s*</ServerName>', content)
            
            servers = []
            for i in range(len(ips)):
                servers.append({
                    'name': names[i] if i < len(names) else f"服务器{i+1}",
                    'ip': ips[i],
                    'port': int(ports[i]) if i < len(ports) else 4001
                })
            
            print(f"✅ 发现 {len(servers)} 个服务器")
            self.results['all_servers'] = servers
    
    def extract_memory_addresses(self):
        """从可执行文件中提取内存地址"""
        print("🔍 分析可执行文件...")
        
        exe_file = self.game_dir / "Prmain.exe"
        if not exe_file.exists():
            print("❌ 找不到Prmain.exe")
            return
            
        with open(exe_file, 'rb') as f:
            data = f.read()
            
        # 查找可能的内存地址模式
        addresses = []
        
        # 查找典型的内存地址模式 (0x00400000 - 0x00800000)
        for i in range(0, len(data) - 4, 4):
            addr = struct.unpack('<I', data[i:i+4])[0]
            if 0x00400000 <= addr <= 0x00800000:
                addresses.append(hex(addr))
        
        # 去重并取前20个
        unique_addrs = list(set(addresses))[:20]
        print(f"✅ 发现 {len(unique_addrs)} 个可能的内存地址")
        
        self.results['memory_addresses'] = unique_addrs
    
    def extract_game_strings(self):
        """提取游戏中的有用字符串"""
        print("🔍 提取游戏字符串...")
        
        # 从Message.ctf提取
        msg_file = self.game_dir / "Message.ctf"
        if msg_file.exists():
            with open(msg_file, 'rb') as f:
                data = f.read()
            
            # 提取中文字符串
            chinese_strings = []
            current_string = ""
            
            try:
                # 尝试GBK解码
                text = data.decode('gbk', errors='ignore')
                # 查找包含中文的字符串
                chinese_matches = re.findall(r'[\u4e00-\u9fff][^\x00-\x1f]*', text)
                chinese_strings.extend(chinese_matches[:50])  # 取前50个
                
            except:
                pass
            
            print(f"✅ 提取到 {len(chinese_strings)} 个中文字符串")
            self.results['game_strings'] = chinese_strings[:20]  # 只保存前20个
    
    def extract_cheat_addresses(self):
        """提取作弊地址"""
        print("🔍 查找作弊相关地址...")
        
        # 从CE表文件提取
        ct_files = list(self.game_dir.glob("*.CT"))
        if ct_files:
            cheat_data = []
            for ct_file in ct_files:
                try:
                    with open(ct_file, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                    
                    # 提取地址和描述
                    addresses = re.findall(r'<Address>([^<]+)</Address>', content)
                    descriptions = re.findall(r'<Description>"([^"]+)"</Description>', content)
                    
                    for i, addr in enumerate(addresses):
                        desc = descriptions[i] if i < len(descriptions) else "未知功能"
                        cheat_data.append({
                            'address': addr,
                            'description': desc,
                            'file': ct_file.name
                        })
                        
                except:
                    continue
            
            print(f"✅ 发现 {len(cheat_data)} 个作弊地址")
            self.results['cheat_addresses'] = cheat_data
    
    def extract_pak_info(self):
        """分析PAK文件结构"""
        print("🔍 分析PAK文件...")
        
        pak_files = list(self.game_dir.glob("*.pak"))
        pak_info = []
        
        for pak_file in pak_files[:10]:  # 只分析前10个
            try:
                with open(pak_file, 'rb') as f:
                    header = f.read(32)
                    
                if len(header) >= 16:
                    # 尝试解析头部
                    magic = header[:4].hex()
                    possible_count = struct.unpack('<I', header[8:12])[0]
                    possible_offset = struct.unpack('<I', header[12:16])[0]
                    
                    pak_info.append({
                        'name': pak_file.name,
                        'size': pak_file.stat().st_size,
                        'magic': magic,
                        'possible_file_count': possible_count if possible_count < 10000 else 0,
                        'possible_data_offset': possible_offset if possible_offset < pak_file.stat().st_size else 0
                    })
                    
            except:
                continue
        
        print(f"✅ 分析了 {len(pak_info)} 个PAK文件")
        self.results['pak_analysis'] = pak_info
    
    def extract_network_info(self):
        """提取网络相关信息"""
        print("🔍 提取网络信息...")
        
        # 查找所有可能包含URL或IP的文件
        network_info = []
        
        for file_path in self.game_dir.rglob("*"):
            if file_path.is_file() and file_path.suffix.lower() in ['.ini', '.xml', '.txt', '.cfg']:
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                    
                    # 查找IP地址
                    ips = re.findall(r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b', content)
                    # 查找URL
                    urls = re.findall(r'https?://[^\s<>"]+', content)
                    
                    if ips or urls:
                        network_info.append({
                            'file': str(file_path.relative_to(self.game_dir)),
                            'ips': list(set(ips)),
                            'urls': list(set(urls))
                        })
                        
                except:
                    continue
        
        print(f"✅ 从 {len(network_info)} 个文件中提取网络信息")
        self.results['network_info'] = network_info
    
    def generate_useful_output(self):
        """生成实用的输出"""
        print("\n" + "="*60)
        print("🎯 天之游侠逆向分析结果")
        print("="*60)
        
        # 服务器信息
        if 'server_info' in self.results:
            info = self.results['server_info']
            print(f"\n📡 主服务器:")
            print(f"   IP: {info['game_ip']}")
            print(f"   端口: {info['game_port']}")
            print(f"   注册: {info['account_url']}")
        
        # 所有服务器
        if 'all_servers' in self.results:
            print(f"\n🌐 所有服务器列表:")
            for server in self.results['all_servers'][:5]:
                print(f"   {server['name']}: {server['ip']}:{server['port']}")
        
        # 作弊地址
        if 'cheat_addresses' in self.results:
            print(f"\n🔧 可用修改地址:")
            for cheat in self.results['cheat_addresses'][:5]:
                print(f"   {cheat['address']} - {cheat['description']}")
        
        # 网络信息
        if 'network_info' in self.results:
            print(f"\n🌍 网络连接信息:")
            for net in self.results['network_info'][:3]:
                if net['ips']:
                    print(f"   文件: {net['file']}")
                    print(f"   IP: {', '.join(net['ips'])}")
                if net['urls']:
                    print(f"   URL: {', '.join(net['urls'])}")
        
        # 保存到文件
        output_file = "uonline_reverse_results.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 详细结果已保存到: {output_file}")
        print("\n✅ 逆向分析完成！")
    
    def run_full_analysis(self):
        """运行完整分析"""
        print("🚀 开始天之游侠逆向分析...")
        
        self.extract_server_info()
        self.extract_cheat_addresses()
        self.extract_network_info()
        self.extract_memory_addresses()
        self.extract_game_strings()
        self.extract_pak_info()
        
        self.generate_useful_output()

if __name__ == "__main__":
    reverser = UonlineReverser()
    reverser.run_full_analysis()
