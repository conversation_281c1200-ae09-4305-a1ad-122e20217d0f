import pymem
import time
from pymem import Pymem

def teleport(pm, x_addr, z_addr, direction):
    """瞬移函数"""
    try:
        current_x = pm.read_float(x_addr)
        current_z = pm.read_float(z_addr)
        
        print(f"\n当前坐标: X={current_x:.2f}, Z={current_z:.2f}")
        
        distance = 8.0  # 瞬移距离
        new_x = current_x
        new_z = current_z
        
        if direction == 'forward':
            new_z = current_z + distance
        elif direction == 'backward':
            new_z = current_z - distance
        elif direction == 'left':
            new_x = current_x - distance
        elif direction == 'right':
            new_x = current_x + distance
            
        # 写入新坐标
        if new_x != current_x:
            pm.write_float(x_addr, new_x)
            time.sleep(0.1)  # 等待一下
            verify_x = pm.read_float(x_addr)
            print(f"X轴移动: {current_x:.2f} -> {verify_x:.2f}")
            
        if new_z != current_z:
            pm.write_float(z_addr, new_z)
            time.sleep(0.1)  # 等待一下
            verify_z = pm.read_float(z_addr)
            print(f"Z轴移动: {current_z:.2f} -> {verify_z:.2f}")
            
    except Exception as e:
        print(f"瞬移错误: {e}")

def main():
    try:
        pm = Pymem("prmain.exe")
        print("成功连接到游戏进程")
        
        base = pm.base_address
        pointer_base = pm.read_longlong(base + 0x253C44)
        
        x_addr = pointer_base + 0x8
        z_addr = pointer_base + 0x10
        
        print(f"\n基址: {hex(base)}")
        print(f"指针基址: {hex(pointer_base)}")
        print(f"X地址: {hex(x_addr)}")
        print(f"Z地址: {hex(z_addr)}")
        
        print("\n瞬移程序启动...")
        
        while True:
            try:
                with open("C:\\Users\\<USER>\\Desktop\\打三个\\Script\\command.txt", "r") as f:
                    cmd = f.read().strip()
                
                if cmd:
                    print(f"\n收到命令: {cmd}")
                    if cmd == "tp":
                        teleport(pm, x_addr, z_addr, 'forward')
                    elif cmd == "back":
                        teleport(pm, x_addr, z_addr, 'backward')
                    elif cmd == "left":
                        teleport(pm, x_addr, z_addr, 'left')
                    elif cmd == "right":
                        teleport(pm, x_addr, z_addr, 'right')
                    
                    # 清空命令
                    with open("C:\\Users\\<USER>\\Desktop\\打三个\\Script\\command.txt", "w") as f:
                        f.write("")
                        
            except Exception as e:
                print(f"错误: {e}")
                
            time.sleep(0.05)

    except Exception as e:
        print(f"错误: {e}")
    finally:
        input("\n按回车键退出...")

if __name__ == "__main__":
    main()