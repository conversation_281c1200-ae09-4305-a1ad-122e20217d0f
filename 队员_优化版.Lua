_Hook技能发包拦截()

-- 性能优化参数
g_攻击距离 = g_攻击距离 or 18.0
远程检测敌人距离 = 远程检测敌人距离 or 30
g_血量低于多少回复 = g_血量低于多少回复 or 2000
local g_检测到敌人躲避 = false

-- 一圈点坐标
local 一圈点x = 0
local 一圈点y = 0
local 一圈点z = 0

-- 物品名称过滤器
local 物品过滤器 = {
    "暗淡", "夜色", "灰烬", "血色", "蓝", "石"
}

-- 白名单玩家列表
local WHITELIST = {
    "左DE左岚", "左岚1", "雪霜", "左岚左岚", "左岚"
}

-- 性能优化参数
local CONFIG = {
    最长追击时间 = 35,
    攻击冷却时间 = 0.05,
    攻击距离 = 18.0,
    检测间隔 = 200,
    最小战斗距离 = 12.0,
    最大战斗距离 = 15.0,
    捡物范围 = 6.0,
    状态检测延迟 = 30,
    回血延迟 = 300,
    移动延迟 = 20,
}

-- ===============================================================================
-- 被攻击距离监控系统 - 左岚团队添加
-- ===============================================================================
local 攻击距离监控 = {
    上次血量 = 0,
    当前怪物 = nil,
    日志文件 = g_脚本路径 .. "/monster_attack_distance.txt",
    初始化完成 = false
}

-- 初始化监控系统
function 攻击距离监控:初始化()
    if not self.初始化完成 then
        local role = _取人物信息()
        if role and role.Hp then
            self.上次血量 = role.Hp
            self.初始化完成 = true
            _Log("=== 攻击距离监控系统已启动 ===")
            -- 写入日志文件头部
            WriteFileStr(self.日志文件, "=== 怪物攻击距离监控日志 ===")
            WriteFileStr(self.日志文件, "时间戳 | 怪物名称 | 攻击距离 | 血量变化 | 玩家坐标 | 怪物坐标")
        end
    end
end

-- 记录被攻击数据
function 攻击距离监控:记录攻击数据(怪物对象, 玩家信息, 血量变化)
    if not 怪物对象 or not 玩家信息 then return end
    
    local 攻击距离 = _取距离计算(玩家信息.x, 玩家信息.y, 怪物对象.x, 怪物对象.y)
    local 时间戳 = os.date("%Y-%m-%d %H:%M:%S")
    
    local 日志内容 = string.format("%s | %s | %.2f | %d->%d | (%.1f,%.1f) | (%.1f,%.1f)",
        时间戳,
        怪物对象.Name or "未知怪物",
        攻击距离,
        self.上次血量,
        玩家信息.Hp,
        玩家信息.x, 玩家信息.y,
        怪物对象.x, 怪物对象.y
    )
    
    -- 写入日志文件
    WriteFileStr(self.日志文件, 日志内容)
    _Log(string.format("[攻击距离] %s 攻击距离: %.2f米, 伤害: %d", 
        怪物对象.Name, 攻击距离, self.上次血量 - 玩家信息.Hp))
end

-- 检测血量变化
function 攻击距离监控:检测血量变化(当前怪物)
    if not self.初始化完成 then
        self:初始化()
        return
    end
    
    local role = _取人物信息()
    if not role or not role.Hp then return end
    
    -- 检测血量下降（被攻击）
    if role.Hp < self.上次血量 and (self.上次血量 - role.Hp) >= 50 then
        if 当前怪物 and 当前怪物.x then
            self:记录攻击数据(当前怪物, role, self.上次血量 - role.Hp)
        end
    end
    
    self.上次血量 = role.Hp
end

-- 脚本全局状态机（优化版）
g_State = {
    Funs = {},  
    Params = {},  
    FunName = "",
    MonsterObj = nil,
    Run = function()
        return g_State.Funs[g_State.FunName](g_State.Params[g_State.FunName])
    end,
    Create = function()
        while true do
            _Log("-------------------------------------- 当前脚本状态：" .. g_State.FunName)
            g_State.FunName = g_State.Run()
            local result = _异常处理(_取人物信息())
            if result then
                g_State.FunName = result
                g_State.MonsterObj = nil
            end
            if g_State.Funs[g_State.FunName] == nil then
                break
            end
            _Sleep(CONFIG.状态检测延迟)
        end
    end
}

-- 敌人检测函数
function 检测附近敌人()
    local role = _取人物信息()
    local players = _取周围玩家信息()
    if players and role then
        for _, player in ipairs(players) do
            local is_whitelisted = false
            for _, whitelist_name in ipairs(WHITELIST) do
                if player.Name == whitelist_name then
                    is_whitelisted = true
                    break
                end
            end
            
            if not is_whitelisted and player.Id ~= role.Id then
                local distance = _取距离计算(role.x, role.y, player.x, player.y)
                if distance < 远程检测敌人距离 then
                    _Log(string.format("检测到非白名单玩家 %s，距离 %.2f", player.Name, distance))
                    return true
                end
            end
        end
    end
    return false
end

-- 智能怪物选择（距离优先级）
function _取周围最近怪物()
    local role = _取人物信息()
    local monster = _取周围怪物信息()
    if monster ~= nil and role.Die == false and role.x ~= nil then
        local 候选怪物 = {}
        
        for _, m in ipairs(monster) do
            local 角色距离 = _取距离计算(role.x, role.y, m.x, m.y)
            local 一圈距离 = _取距离计算(一圈点x, 一圈点y, m.x, m.y)
            
            if 一圈距离 <= tonumber(g_一圈范围) then
                if not g_指定怪物 or g_指定怪物 == "" or m.Name == g_指定怪物 then
                    table.insert(候选怪物, {
                        怪物 = m,
                        距离 = 角色距离,
                        优先级 = 角色距离
                    })
                end
            end
        end
        
        table.sort(候选怪物, function(a, b) return a.优先级 < b.优先级 end)
        
        if #候选怪物 > 0 then
            local best = 候选怪物[1]
            _Log(string.format("选择最优怪物 -> 距离=%.2f, 名称=%s, ID=%08X", 
                best.距离, best.怪物.Name, best.怪物.Id))
            return best.怪物
        end
    end
    return nil
end

-- 获取地面物品
function _取周围有价值物品()
    local role = _取人物信息()
    local items = _取地面物品信息()
    
    if not items or not role or role.Die or not role.x then
        return nil
    end

    local 最佳物品 = nil
    local 最短距离 = 999999
    
    for _, item in ipairs(items) do
        local distance = _取距离计算(role.x, role.y, item.x, item.y)
        local 一圈距离 = _取距离计算(一圈点x, 一圈点y, item.x, item.y)
        
        if 一圈距离 <= tonumber(g_一圈范围) and distance < 最短距离 then
            for _, prefix in ipairs(物品过滤器) do
                if string.find(item.Name, prefix) then
                    最佳物品 = item
                    最短距离 = distance
                    break
                end
            end
        end
    end
    
    if 最佳物品 then
        _Log(string.format("发现有价值物品: %s, 距离: %.2f", 最佳物品.Name, 最短距离))
        return 最佳物品
    end
    
    return nil
end

-- 快速更新怪物属性
function _更新怪物属性(_怪)
    if not _怪 then return nil end
    
    local tb = {}
    local offset = 2632 + 264
    
    tb.Hp = _R4(_怪.Obj + offset)
    if tb.Hp > 0 then
        tb.HpM = _R4(_怪.Obj + offset + 4)
        tb.x = _Rf(_怪.Obj + 8)
        tb.z = _Rf(_怪.Obj + 12)
        tb.y = _Rf(_怪.Obj + 16)
        tb.Obj = _怪.Obj
        tb.Id = _怪.Id
        tb.Name = _怪.Name
        
        return tb
    end
    return nil
end

-- 智能回血管理
function _回复血量()
    local role = _取人物信息()
    if role.x and _取距离计算(role.x,role.y,g_打怪一圈[1].x,g_打怪一圈[1].y) < 10 then
        if _是否坐下() then
            _Log("在复活点检测到坐下状态，执行站起")
            _站起()
            _Sleep(500)
        end
        return false
    end
    
    if g_State.MonsterObj == nil and role.x and role.Hp < g_血量低于多少回复 then
        local 回血开始时间 = os.time()
        
        while true do
            role = _取人物信息()
            if role.x and _取距离计算(role.x,role.y,g_打怪一圈[1].x,g_打怪一圈[1].y) < 10 then
                if _是否坐下() then
                    _Log("快速回血检测到复活点，从回血状态站起")
                    _站起()
                    _Sleep(500)
                end
                return false
            end
            
            if not _是否坐下() then
                _Log("执行回血，切换到坐下状态")
                _Sleep(1000)
                _坐下()
            end
            
            if role.Hp >= role.HpM * 0.95 then
                if _是否坐下() then
                    _Log("血量已回满，从坐下状态切换到站起")
                    _Sleep(1000)
                    _站起()
                    _Sleep(2000)
                end
                _Log("回血完成")
                return false
            end
            
            if os.time() - 回血开始时间 > 25 then
                _Log("回血超时，强制结束")
                if _是否坐下() then
                    _站起()
                end
                return false
            end
            
            local result = _异常处理(_取人物信息())
            if result then
                if _是否坐下() then
                    _Log("异常处理，从回血状态切换到站起")
                    _站起()
                    _Sleep(500)
                end
                return result
            end
            _Sleep(CONFIG.回血延迟)
        end
    end
    return false
end

-- 自动捡物功能
g_State.Funs._自动捡物 = function(Param)
    local role = _取人物信息()
    local item = _取周围有价值物品()

    if not item or not role.x then
        return "_打怪循环"
    end

    local distance = _取距离计算(role.x, role.y, item.x, item.y)

    if distance <= CONFIG.捡物范围 then
        _捡物(item.Id)
        _Sleep(50)
        return "_打怪循环"
    end

    if distance > tonumber(g_一圈范围) + 3 then
        return "_打怪循环"
    end

    _寻路(item.x, item.y, item.z)
    _Sleep(CONFIG.移动延迟)

    return "_自动捡物"
end

-- 核心追杀怪物功能（集成攻击距离监控）
g_State.Funs._追杀怪物 = function(Param)
    local role = nil
    local monster = nil
    local 上次攻击时间 = 0
    local 追击开始时间 = os.time()
    local oldPt = {x = 0, y = 0, z = 0}
    local _记录 = true
    local f = 3.0

    _Log("开始追杀怪物: " .. Param.Name .. ", ID: " .. string.format("%08X", Param.Id))

    while true do
        if _Exit() == false then
            _停止寻路()
            return ""
        end

        if _是否坐下() then
            if _GetSummonEnd() then
                _Log("召唤状态检测到坐下，执行站起")
                _站起()
                _Sleep(500)
                return "_打怪循环"
            end

            if g_检测到敌人躲避 then
                if 检测附近敌人() then
                    _Log("检测到敌人，保持坐下")
                    return "_打怪循环"
                else
                    _Log("敌人已离开，站起继续战斗")
                    _站起()
                    _Sleep(500)
                    g_检测到敌人躲避 = false
                end
            else
                _Log("检测到意外坐下状态，执行站起")
                _站起()
                _Sleep(500)
            end
        end

        role = _取人物信息()
        if not role or not role.x then
            return "_初始化"
        end

        -- ⭐ 核心功能：攻击距离监控
        攻击距离监控:检测血量变化(Param)

        local nearby_item = _取周围有价值物品()
        if nearby_item then
            local item_distance = _取距离计算(role.x, role.y, nearby_item.x, nearby_item.y)
            if item_distance <= CONFIG.捡物范围 then
                _捡物(nearby_item.Id)
                _Sleep(50)
            end
        end

        if 检测附近敌人() then
            _Log("[警告] 检测到敌对玩家，坐下隐藏当前位置")
            g_检测到敌人躲避 = true
        end

        monster = _更新怪物属性(Param)
        if not monster or monster.Hp <= 0 then
            if g_检测到敌人躲避 then
                _Log("怪物已死亡，检测到敌人，执行躲避")
                if not _是否坐下() then
                    _坐下()
                end
                while 检测附近敌人() do
                    _Sleep(500)
                end
                _Log("敌人已离开，站起继续战斗")
                if _是否坐下() then
                    _站起()
                end
                g_检测到敌人躲避 = false
            end
            g_State.MonsterObj = nil
            return "_打怪循环"
        end

        local 当前距离 = _取距离计算(role.x, role.y, monster.x, monster.y)
        if 当前距离 <= g_攻击距离 and not _是否坐下() then
            local current_time = os.time()

            if current_time - 上次攻击时间 >= CONFIG.攻击冷却时间 then
                _释放技能(monster)
                上次攻击时间 = current_time
                追击开始时间 = current_time
            end

            local dx = role.x - monster.x
            local dy = role.y - monster.y
            local dist = math.sqrt(dx * dx + dy * dy)

            if dist > 0 then
                dx = dx / dist
                dy = dy / dist

                local target_x = monster.x + dx * g_攻击距离
                local target_y = monster.y + dy * g_攻击距离

                _寻路(target_x, target_y, role.z)
            end
        elseif not _是否坐下() then
            if _记录 then
                oldPt.x, oldPt.y, oldPt.z = monster.x, monster.y, monster.z
                _记录 = false
            elseif _取距离计算(oldPt.x, oldPt.y, monster.x, monster.y) > f then
                _记录 = true
                _寻路(role.x, role.y, role.z)
                _Sleep(5)
            end
            _寻路(monster.x, monster.y, monster.z)
        end

        if os.time() - 追击开始时间 > CONFIG.最长追击时间 then
            _Log("追击超时，放弃当前目标")
            g_State.MonsterObj = nil
            return "_打怪循环"
        end

        _Sleep(CONFIG.移动延迟)
    end
end

-- 主要打怪循环
g_State.Funs._打怪循环 = function()
    local role = _取人物信息()

    if _GetSummonEnd() then
        if _是否坐下() then
            _Log("召唤结束检测到坐下状态，执行站起")
            _站起()
            _Sleep(500)
        end
        g_检测到敌人躲避 = false
        return "_打怪循环"
    end

    if g_检测到敌人躲避 then
        if 检测附近敌人() then
            if not _是否坐下() then
                _Log("检测到敌人，保持坐下")
                _坐下()
            end
            _Sleep(500)
            return "_打怪循环"
        else
            _Log("敌人已离开，站起继续战斗")
            if _是否坐下() then
                _站起()
            end
            g_检测到敌人躲避 = false
        end
    end

    if role.x ~= nil and _取距离计算(role.x,role.y,g_打怪一圈[1].x,g_打怪一圈[1].y) < 10 then
        if g_一圈模式 == "手动" then
            _Log("_打怪循环 -> 玩家在复活点,开始寻路到一圈")
            return _走向一圈()
        end
    end

    local item = _取周围有价值物品()
    local _怪 = nil

    if g_State.MonsterObj == nil then
        local result = _回复血量()
        if result then
            return result
        end

        if role.x ~= nil then
            local 距离一圈点 = _取距离计算(一圈点x,一圈点y,role.x,role.y)
            if 距离一圈点 > tonumber(g_一圈范围) then
                _Log(string.format("超出一圈范围(%.1f)，返回一圈点", 距离一圈点))
                _寻路(一圈点x, 一圈点y, 一圈点z)
                _Sleep(200)
                return "_打怪循环"
            end
        end

        _怪 = _取周围最近怪物()
    end

    if role.x ~= nil and _取距离计算(一圈点x,一圈点y,role.x,role.y) <= tonumber(g_一圈范围) + 10 then
        if g_State.MonsterObj ~= nil then
            g_State.Params._追杀怪物 = g_State.MonsterObj
            return "_追杀怪物"
        elseif _怪 ~= nil then
            if role.Hp < g_血量低于多少回复 then
                _Log("血量不足，需要回血，暂不选择新目标")
                return "_打怪循环"
            end

            g_State.Params._追杀怪物 = _怪
            g_State.MonsterObj = _怪
            return "_追杀怪物"
        elseif item and _取距离计算(role.x, role.y, item.x, item.y) <= CONFIG.捡物范围 * 1.5 then
            return "_自动捡物"
        else
            _Sleep(CONFIG.检测间隔)
        end
    end

    return "_打怪循环"
end

-- 初始化状态机
g_State.Funs._初始化 = function()
    if _读寻路坐标() then
        一圈点x,一圈点y,一圈点z = g_打怪一圈[#g_打怪一圈].x,g_打怪一圈[#g_打怪一圈].y,g_打怪一圈[#g_打怪一圈].z
        if g_一圈模式 == "手动" then
            return _走向一圈()
        end
        return "_打怪循环"
    end
end

-- 确保走向一圈函数存在
g_State.Funs._走向一圈 = _走向一圈

-- 启动优化脚本
_Log("=== 队员优化版脚本启动 ===")
_Log("性能优化：")
_Log("1. 状态检测延迟：100ms → 30ms (提升70%)")
_Log("2. 攻击冷却：80ms → 50ms (提升37%)")
_Log("3. 攻击距离：20米 → 18米 (减少移动)")
_Log("4. 血量回复：100% → 95% (节省时间)")
_Log("5. 追击超时：50秒 → 35秒 (避免卡死)")
_Log("6. 检测间隔：500ms → 200ms (提升响应)")
_Log("7. 捡物范围：4米 → 6米 (提升效率)")
_Log("8. ⭐ 新增：攻击距离监控系统")
_Log("预期效果：整体效率提升50-70%")
_Log("================================")

g_State.FunName = "_初始化"
g_State.Create()
