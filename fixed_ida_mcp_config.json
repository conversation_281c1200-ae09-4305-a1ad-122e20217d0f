{"mcpServers": {"github.com/mrexodia/ida-pro-mcp": {"isActive": true, "command": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python.exe", "args": ["C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\ida_pro_mcp\\server.py"], "timeout": 1800, "disabled": false, "autoApprove": ["check_connection", "get_metadata", "get_function_by_name", "get_function_by_address", "get_current_address", "get_current_function", "convert_number", "list_functions", "list_strings", "search_strings", "decompile_function", "disassemble_function", "get_xrefs_to", "get_entry_points", "set_comment", "rename_local_variable", "rename_global_variable", "set_global_variable_type", "rename_function", "set_function_prototype", "declare_c_type", "set_local_variable_type"], "alwaysAllow": ["check_connection", "get_metadata", "get_function_by_name", "get_function_by_address", "get_current_address", "get_current_function", "convert_number", "list_functions", "list_strings", "search_strings", "decompile_function", "disassemble_function", "get_xrefs_to", "get_entry_points", "set_comment", "rename_local_variable", "rename_global_variable", "set_global_variable_type", "rename_function", "set_function_prototype", "declare_c_type", "set_local_variable_type"], "name": "ida-pro-mcp"}}}