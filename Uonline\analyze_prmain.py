#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析Prmain.exe - 提取字符串、地址、API调用
"""

import struct
import re
import os

def extract_strings_from_exe():
    """从Prmain.exe提取可读字符串"""
    print("🔍 分析 Prmain.exe...")
    
    try:
        with open('Prmain.exe', 'rb') as f:
            data = f.read()
        
        print(f"文件大小: {len(data):,} 字节")
        
        # 提取ASCII字符串 (长度>=4)
        ascii_strings = []
        current_string = ""
        
        for i, byte in enumerate(data):
            if 32 <= byte <= 126:  # 可打印ASCII
                current_string += chr(byte)
            else:
                if len(current_string) >= 4:
                    ascii_strings.append((i - len(current_string), current_string))
                current_string = ""
        
        # 最后一个字符串
        if len(current_string) >= 4:
            ascii_strings.append((len(data) - len(current_string), current_string))
        
        print(f"找到 {len(ascii_strings)} 个ASCII字符串")
        
        # 分类字符串
        api_calls = []
        file_paths = []
        urls = []
        error_msgs = []
        game_strings = []
        
        for offset, string in ascii_strings:
            s = string.lower()
            if any(api in s for api in ['createfile', 'readfile', 'writefile', 'socket', 'connect', 'send', 'recv']):
                api_calls.append((offset, string))
            elif '\\' in string or '/' in string:
                file_paths.append((offset, string))
            elif 'http' in s or 'www' in s:
                urls.append((offset, string))
            elif any(word in s for word in ['error', 'fail', 'exception', 'invalid']):
                error_msgs.append((offset, string))
            elif len(string) > 10 and not string.isdigit():
                game_strings.append((offset, string))
        
        # 输出结果
        print(f"\n📞 API调用相关 ({len(api_calls)}个):")
        for offset, string in api_calls[:10]:
            print(f"  0x{offset:08X}: {string}")
        
        print(f"\n📁 文件路径 ({len(file_paths)}个):")
        for offset, string in file_paths[:10]:
            print(f"  0x{offset:08X}: {string}")
        
        print(f"\n🌐 URL地址 ({len(urls)}个):")
        for offset, string in urls[:10]:
            print(f"  0x{offset:08X}: {string}")
        
        print(f"\n❌ 错误信息 ({len(error_msgs)}个):")
        for offset, string in error_msgs[:10]:
            print(f"  0x{offset:08X}: {string}")
        
        print(f"\n🎮 游戏字符串 ({len(game_strings)}个):")
        for offset, string in game_strings[:15]:
            print(f"  0x{offset:08X}: {string}")
        
        return ascii_strings
        
    except Exception as e:
        print(f"分析失败: {e}")
        return []

def find_memory_addresses():
    """查找可能的内存地址"""
    print(f"\n🧠 查找内存地址模式...")
    
    try:
        with open('Prmain.exe', 'rb') as f:
            data = f.read()
        
        addresses = []
        
        # 查找典型的内存地址 (0x00400000 - 0x00800000)
        for i in range(0, len(data) - 4, 4):
            try:
                addr = struct.unpack('<I', data[i:i+4])[0]
                if 0x00400000 <= addr <= 0x00800000:
                    addresses.append((i, addr))
            except:
                continue
        
        # 去重并排序
        unique_addrs = {}
        for offset, addr in addresses:
            if addr not in unique_addrs:
                unique_addrs[addr] = offset
        
        sorted_addrs = sorted(unique_addrs.items())
        
        print(f"找到 {len(sorted_addrs)} 个可能的内存地址:")
        for addr, offset in sorted_addrs[:20]:
            print(f"  文件偏移 0x{offset:08X}: 内存地址 0x{addr:08X}")
        
        return sorted_addrs
        
    except Exception as e:
        print(f"地址查找失败: {e}")
        return []

def analyze_pe_header():
    """分析PE文件头"""
    print(f"\n📋 PE文件头分析...")
    
    try:
        with open('Prmain.exe', 'rb') as f:
            # 读取DOS头
            dos_header = f.read(64)
            if dos_header[:2] != b'MZ':
                print("❌ 不是有效的PE文件")
                return
            
            # 获取PE头偏移
            pe_offset = struct.unpack('<I', dos_header[60:64])[0]
            f.seek(pe_offset)
            
            # 读取PE签名
            pe_sig = f.read(4)
            if pe_sig != b'PE\x00\x00':
                print("❌ PE签名无效")
                return
            
            # 读取COFF头
            coff_header = f.read(20)
            machine, sections, timestamp, _, _, opt_size, characteristics = struct.unpack('<HHIIIHH', coff_header)
            
            print(f"✅ 有效的PE文件")
            print(f"目标架构: 0x{machine:04X}")
            print(f"节数量: {sections}")
            print(f"编译时间: {timestamp}")
            print(f"特征: 0x{characteristics:04X}")
            
            # 读取可选头
            if opt_size > 0:
                opt_header = f.read(min(opt_size, 96))
                if len(opt_header) >= 28:
                    magic, major, minor, code_size, init_size, uninit_size, entry_point, code_base = struct.unpack('<HHBBIIIII', opt_header[:28])
                    print(f"入口点: 0x{entry_point:08X}")
                    print(f"代码基址: 0x{code_base:08X}")
                    print(f"代码大小: {code_size:,} 字节")
        
    except Exception as e:
        print(f"PE分析失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 Prmain.exe 深度分析")
    print("=" * 60)
    
    if not os.path.exists('Prmain.exe'):
        print("❌ 找不到 Prmain.exe 文件")
        return
    
    # 分析PE头
    analyze_pe_header()
    
    # 提取字符串
    strings = extract_strings_from_exe()
    
    # 查找内存地址
    addresses = find_memory_addresses()
    
    print(f"\n" + "=" * 60)
    print("✅ Prmain.exe 分析完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
