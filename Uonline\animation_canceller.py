#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动画取消器
基于分析结果，将技能动画时长改为1ms，实现瞬发效果
"""

import os
import struct
import shutil
from datetime import datetime

class AnimationCanceller:
    def __init__(self):
        self.modifications = 0
        self.target_files = [
            'Effect.pak',      # 特效动画 (37MB, 最重要)
            'Common.pak',      # 通用动画 (22MB)
            'i_m_d.pak',       # 男性角色动画
            'i_w_d.pak',       # 女性角色动画
            'c_pc.pak'         # 玩家角色动画
        ]
        
    def backup_file(self, filename):
        """备份文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = f"{filename}.backup_anim_{timestamp}"
        shutil.copy2(filename, backup_path)
        print(f"✅ 备份创建: {backup_path}")
        return backup_path
        
    def cancel_animation_durations(self, filename, data):
        """取消动画时长 - 将所有动画时长改为1ms"""
        print(f"\n🎬 处理动画时长: {filename}")
        
        # 基于分析结果的动画时长列表 (毫秒)
        animation_durations = [
            100, 200, 300, 400, 500, 600, 700, 800, 900, 1000,
            1100, 1200, 1300, 1400, 1500, 1600, 1700, 1800, 1900, 2000,
            2100, 2200, 2300, 2400, 2500, 2600, 2700, 2800, 2900, 3000,
            3100, 3200, 3300, 3400, 3500, 3600, 3700, 3800, 3900, 4000,
            4100, 4200, 4300, 4400, 4500, 4600, 4700, 4800, 4900, 5000
        ]
        
        total_changes = 0
        
        for duration_ms in animation_durations:
            # 转换为32位小端序字节
            duration_bytes = struct.pack('<I', duration_ms)
            instant_bytes = struct.pack('<I', 1)  # 1毫秒 = 瞬间
            
            # 查找并替换
            count = 0
            i = 0
            while i < len(data) - 4:
                if data[i:i+4] == duration_bytes:
                    data[i:i+4] = instant_bytes
                    count += 1
                    total_changes += 1
                i += 1
            
            if count > 0:
                print(f"  ⚡ {duration_ms}ms -> 1ms: 修改了 {count} 处")
        
        print(f"  📊 总共修改了 {total_changes} 个动画时长")
        return total_changes
        
    def cancel_frame_rates(self, filename, data):
        """提升动画帧率 - 让动画播放更快"""
        print(f"\n🎞️ 处理动画帧率: {filename}")
        
        # 基于分析结果的帧率
        frame_rates = [15, 24, 30, 60, 120]
        target_fps = 1000  # 超高帧率 = 超快播放
        
        total_changes = 0
        
        for fps in frame_rates:
            fps_bytes = struct.pack('<I', fps)
            fast_fps_bytes = struct.pack('<I', target_fps)
            
            count = 0
            i = 0
            while i < len(data) - 4:
                if data[i:i+4] == fps_bytes:
                    data[i:i+4] = fast_fps_bytes
                    count += 1
                    total_changes += 1
                i += 1
            
            if count > 0:
                print(f"  🚀 {fps}fps -> {target_fps}fps: 修改了 {count} 处")
        
        print(f"  📊 总共修改了 {total_changes} 个帧率设置")
        return total_changes
        
    def redirect_action_ids(self, filename, data):
        """重定向动作ID - 将所有动作指向待机动作"""
        print(f"\n🎭 处理动作ID: {filename}")
        
        # 基于分析结果，动作ID 0 出现最多，可能是待机动作
        idle_action_id = 0
        idle_bytes = struct.pack('<I', idle_action_id)
        
        # 要重定向的动作ID (攻击、技能等)
        action_ids_to_redirect = [1, 2, 3, 4, 5, 6, 9, 10, 18, 21, 29, 33, 34, 37, 43, 48, 49]
        
        total_changes = 0
        
        for action_id in action_ids_to_redirect:
            action_bytes = struct.pack('<I', action_id)
            
            count = 0
            i = 0
            while i < len(data) - 4:
                if data[i:i+4] == action_bytes:
                    # 检查上下文，确保这是动作ID而不是其他数据
                    if self.is_likely_action_id_context(data, i):
                        data[i:i+4] = idle_bytes
                        count += 1
                        total_changes += 1
                i += 1
            
            if count > 0:
                print(f"  🔄 动作ID {action_id} -> 0(待机): 修改了 {count} 处")
        
        print(f"  📊 总共重定向了 {total_changes} 个动作ID")
        return total_changes
        
    def is_likely_action_id_context(self, data, pos):
        """判断是否是动作ID的上下文"""
        # 简单的上下文检查
        if pos < 8 or pos > len(data) - 8:
            return True
            
        # 检查前后的数据是否在合理范围内
        try:
            prev_val = struct.unpack('<I', data[pos-4:pos])[0]
            next_val = struct.unpack('<I', data[pos+4:pos+8])[0]
            
            # 如果前后的值都很大，这个可能不是动作ID
            if prev_val > 100000 and next_val > 100000:
                return False
                
            return True
        except:
            return True
            
    def process_animation_file(self, filename):
        """处理单个动画文件"""
        if not os.path.exists(filename):
            print(f"❌ 文件不存在: {filename}")
            return False
            
        print(f"\n{'='*60}")
        print(f"🎬 处理动画文件: {filename}")
        print(f"{'='*60}")
        
        try:
            # 读取文件
            with open(filename, 'rb') as f:
                data = bytearray(f.read())
            
            print(f"📁 文件大小: {len(data):,} 字节")
            
            # 备份文件
            backup_path = self.backup_file(filename)
            
            # 执行各种动画取消操作
            duration_changes = self.cancel_animation_durations(filename, data)
            framerate_changes = self.cancel_frame_rates(filename, data)
            action_changes = self.redirect_action_ids(filename, data)
            
            total_changes = duration_changes + framerate_changes + action_changes
            
            if total_changes > 0:
                # 保存修改后的文件
                with open(filename, 'wb') as f:
                    f.write(data)
                
                print(f"\n✅ {filename} 修改完成!")
                print(f"   📊 总修改数: {total_changes}")
                print(f"   💾 新文件大小: {len(data):,} 字节")
                
                self.modifications += total_changes
                return True
            else:
                print(f"\n⚠️ {filename} 未找到可修改的动画数据")
                # 删除不必要的备份
                if os.path.exists(backup_path):
                    os.remove(backup_path)
                return False
                
        except Exception as e:
            print(f"❌ 处理失败: {e}")
            return False
            
    def verify_modifications(self, filename):
        """验证修改效果"""
        try:
            with open(filename, 'rb') as f:
                data = f.read()
            
            # 检查是否有1ms的动画时长
            instant_bytes = struct.pack('<I', 1)
            instant_count = data.count(instant_bytes)
            
            # 检查是否有1000fps的帧率
            fast_fps_bytes = struct.pack('<I', 1000)
            fast_fps_count = data.count(fast_fps_bytes)
            
            print(f"  🔍 验证结果:")
            print(f"    1ms动画时长: {instant_count} 个")
            print(f"    1000fps帧率: {fast_fps_count} 个")
            
            return instant_count > 0 or fast_fps_count > 0
            
        except Exception as e:
            print(f"  ❌ 验证失败: {e}")
            return False
            
    def run(self):
        """运行动画取消器"""
        print("🎬 天之游侠动画取消器")
        print("基于HOOK经验和深度分析，实现技能瞬发效果")
        print("=" * 60)
        
        success_count = 0
        
        for filename in self.target_files:
            if self.process_animation_file(filename):
                if self.verify_modifications(filename):
                    success_count += 1
                    print(f"✅ {filename} 验证通过")
                else:
                    print(f"⚠️ {filename} 验证未通过")
            else:
                print(f"❌ {filename} 处理失败")
        
        print(f"\n{'='*60}")
        print(f"🎯 动画取消完成!")
        print(f"📊 成功处理: {success_count}/{len(self.target_files)} 个文件")
        print(f"🔧 总修改数: {self.modifications}")
        print(f"{'='*60}")
        
        if success_count > 0:
            print(f"\n🎉 预期效果:")
            print(f"1. ⚡ 技能释放瞬间完成 - 无动作延迟")
            print(f"2. 🚀 连招更加流畅 - 不用等动画播放")
            print(f"3. ⚔️ 战斗节奏飞快 - 像开了加速器")
            print(f"4. 🎮 操作手感提升 - 按键即时响应")
            print(f"\n💡 重要提示:")
            print(f"- 伤害依然正常计算 (服务器端)")
            print(f"- 只是取消了动作表演 (客户端)")
            print(f"- 如有问题可恢复备份文件")
            print(f"- 重启游戏以加载修改")
        else:
            print(f"\n❌ 没有文件被成功修改")
            print(f"可能需要更深入的分析")

def main():
    """主函数"""
    canceller = AnimationCanceller()
    canceller.run()

if __name__ == "__main__":
    main()
