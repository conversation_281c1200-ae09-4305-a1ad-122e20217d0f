# 天之游侠 (Uonline) 游戏机制深度分析报告

## 1. 游戏基本架构

### 1.1 核心进程结构
- **主进程**: `prmain.exe` (1,744,384 字节)
- **核心库**: `PMainGameMFC.dll` - 主要游戏逻辑
- **游戏库**: `Uonline.dll` (168,960 字节) - 核心功能
- **图形库**: `GrpLib.dll` - 图形渲染
- **音频库**: `Wav.dll` - 音频处理

### 1.2 反作弊系统
- **双重保护**: HackShield + HShield
- **检测模块**: 
  - `EGRNAP.dll` - 内存保护
  - `EHSvc.dll` - 服务监控
  - `v3pro32s.dll` - 进程保护
  - `v3hunt.dll` - 外挂检测

## 2. 内存结构分析

### 2.1 玩家数据结构
```
基址偏移: prmain.exe + 0x253C44
玩家信息结构:
+0x00: 对象指针
+0x08: X坐标 (float)
+0x0C: Y坐标 (float) 
+0x10: Z坐标 (float)
+0x14: 当前血量 (int)
+0x18: 最大血量 (int)
+0x1C: 当前魔法值 (int)
+0x20: 移动速度 (int)
+0x24: 角色状态 (int)
+0x28: 种族信息 (int)
+0x2C: 经验值 (int)
```

### 2.2 怪物数据结构
```
怪物对象偏移分析:
+0x04: 怪物等级 (int)
+0x18: 攻击力 (int) 
+0x6C: AI状态 (int)
+0x7C: 追击距离 (int) - 通常为45
+0x80: 仇恨距离 (int) - 通常为45
+0x90: AI模式 (int)
+0x98: 行为模式 (int)
+0x9C: 未知参数 (int)
+0xA0: 伤害系数 (int)
+0xC8: 重复范围值 (int)
```

## 3. 战斗系统机制

### 3.1 攻击距离系统
- **玩家攻击距离**: 20.47米
- **怪物攻击距离**: 5.09米
- **AI安全距离**: 6-7米 (比怪物攻击距离稍远)
- **AI最优距离**: 18-19米 (在玩家攻击范围内)
- **完美控制区间**: 6-19米 (绝对安全区间)

### 3.2 仇恨系统
- **仇恨范围**: 45米 (从内存分析得出)
- **追击范围**: 45米 (与仇恨范围相同)
- **脱战距离**: 超过追击范围后怪物返回

### 3.3 技能系统
- **技能范围修改地址**: `0x6496EC`
- **攻击范围修改地址**: `0x649708`
- **子弹数量修改地址**: `0x6496F4`
- **全屏技能值**: 32384

## 4. NPC系统分析

### 4.1 功能性NPC
- **双倍经验NPC**:
  - "布莱恩·库克<开始双倍>" - 开启双倍经验
  - "博·奥特洛<结束双倍>" - 结束双倍经验
- **功能NPC**:
  - "马辛·戈塔<打卡教官>" - 打卡功能
  - "接引使者" - 传送功能
  - "神父" - 复活/治疗功能

### 4.2 NPC特征
- 血量为0 (HP = 0, HpM = 0)
- 固定位置，不移动
- 可交互对象

## 5. 物品系统

### 5.1 地面物品检测
- 物品拾取范围可配置
- 支持白名单过滤
- 实时距离计算

### 5.2 装备系统
从.pak文件分析得出:
- 大量装备类型 (eqp0001.pak - eqp1001.pak)
- 不同职业装备 (I_开头和T_开头文件)
- 装备等级系统

## 6. 地图系统

### 6.1 地图结构
主要地图文件:
- `map-agiter_village.pak` - 阿吉特村庄
- `map-oldberry_village.pak` - 老浆果村庄  
- `map-silverfence_village.pak` - 银栅村庄
- `map-riverman_village.pak` - 河人村庄
- 各种野外地图和副本

### 6.2 坐标系统
- 3D坐标系统 (X, Y, Z)
- Y轴为高度
- 支持瞬移修改坐标

## 7. 网络通信

### 7.1 服务器信息
- **主服务器**: 121.40.205.138:4001
- **补丁服务器**: http://patch.wud2.com/wdpatch/
- **注册页面**: http://wud88member.t2uu.com/reg.aspx

### 7.2 通信协议
- TCP连接
- 自定义协议格式
- 实时数据同步

## 8. AI系统分析

### 8.1 怪物AI状态
- **巡逻状态**: 正常游荡
- **追击状态**: 发现目标后追击
- **攻击状态**: 进入攻击范围后攻击
- **返回状态**: 超出追击范围后返回

### 8.2 AI行为模式
- 基于距离的状态切换
- 可预测的行为模式
- 固定的攻击和追击范围

## 9. 修改和外挂检测

### 9.1 内存保护
- 多层反调试保护
- 实时内存完整性检查
- 进程注入检测

### 9.2 已知修改点
- 技能范围修改 (相对安全)
- 攻击范围修改 (相对安全)
- 坐标修改 (高风险)
- 血量修改 (高风险)

## 10. 逆向工程建议

### 10.1 安全的分析方法
1. 使用内存读取而非修改
2. 避免修改关键游戏数据
3. 使用合法的API调用
4. 定期备份游戏文件

### 10.2 进一步研究方向
1. 网络协议逆向
2. 技能系统深度分析
3. 装备属性计算公式
4. 经验值计算机制
5. PvP伤害计算公式

## 11. 工具和脚本

### 11.1 推荐工具
- **Cheat Engine** - 内存分析
- **x64dbg** - 动态调试
- **IDA Pro** - 静态分析
- **Wireshark** - 网络分析

### 11.2 自动化脚本
- 玩家信息监控脚本
- 怪物数据收集器
- 自动战斗AI
- 物品拾取脚本

## 12. 风险评估

### 12.1 检测风险等级
- **低风险**: 内存读取、数据分析
- **中风险**: 技能范围修改、攻击范围修改
- **高风险**: 坐标修改、属性修改、速度修改
- **极高风险**: 网络数据包修改、服务器通信拦截

### 12.2 安全建议
1. 仅用于学习和研究目的
2. 不要在正式服务器使用修改
3. 定期更新反检测技术
4. 保持低调，避免大规模传播

---

**免责声明**: 本分析报告仅用于技术研究和学习目的，不鼓励任何形式的游戏作弊行为。
