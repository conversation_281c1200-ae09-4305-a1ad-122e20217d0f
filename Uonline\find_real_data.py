#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
寻找真正的游戏数据源
"""

import os
import struct

def check_file_for_hp_data(filepath):
    """检查文件是否包含HP数据"""
    try:
        with open(filepath, 'rb') as f:
            data = f.read()
            
        # 查找HP+数字模式
        hp_count = data.count(b'HP+')
        
        # 查找数值模式 (可能的HP值)
        numeric_values = []
        for i in range(0, len(data) - 4, 1):
            try:
                # 尝试小端序
                value = struct.unpack('<I', data[i:i+4])[0]
                if 100 <= value <= 100000:  # 合理的HP范围
                    numeric_values.append(value)
            except:
                continue
                
        return hp_count, len(set(numeric_values))
        
    except Exception as e:
        return 0, 0

def main():
    """主函数"""
    print("寻找真正的游戏数据源...")
    
    # 检查关键文件
    key_files = [
        'Message.ctf',
        'item0001.pak',
        'item0002.pak', 
        'item0003.pak',
        'eqp0001.pak',
        'eqp0002.pak',
        'eqp0003.pak',
        'eqp0004.pak',
        'etc.pak',
        'Common.pak',
        'Uonline.dll',
        'white.dll'
    ]
    
    results = []
    
    for filename in key_files:
        if os.path.exists(filename):
            hp_count, numeric_count = check_file_for_hp_data(filename)
            size = os.path.getsize(filename)
            results.append((filename, hp_count, numeric_count, size))
            print(f"{filename}: HP+出现{hp_count}次, 数值{numeric_count}个, 大小{size}字节")
        else:
            print(f"{filename}: 文件不存在")
            
    # 按HP数据排序
    results.sort(key=lambda x: x[1] + x[2], reverse=True)
    
    print("\n最可能包含游戏数据的文件:")
    for filename, hp_count, numeric_count, size in results[:5]:
        if hp_count > 0 or numeric_count > 100:
            print(f"  {filename} - HP+:{hp_count}, 数值:{numeric_count}")

if __name__ == "__main__":
    main()
