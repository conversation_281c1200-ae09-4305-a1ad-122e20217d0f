#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Uonline.dll 反混淆工具
专业级代码反混淆和控制流恢复
"""

import asyncio
import json
import re
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional

# MCP连接
try:
    from mcp import ClientSession, StdioServerParameters
    from mcp.client.stdio import stdio_client
    MCP_AVAILABLE = True
except ImportError:
    MCP_AVAILABLE = False

class UonlineDeobfuscator:
    def __init__(self):
        self.session = None
        self.functions = {}
        self.strings = {}
        self.control_flow_graph = {}
        self.real_functions = {}
        self.junk_instructions = set()
        
    async def connect_ida(self):
        """连接IDA Pro"""
        if not MCP_AVAILABLE:
            print("❌ MCP不可用")
            return False
            
        try:
            server_params = StdioServerParameters(
                command="C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python.exe",
                args=["C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\ida_pro_mcp\\server.py"]
            )
            
            self.client = stdio_client(server_params)
            self.read, self.write = await self.client.__aenter__()
            self.session = await ClientSession(self.read, self.write).__aenter__()
            
            print("✅ 成功连接IDA Pro")
            return True
            
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    async def analyze_obfuscation_patterns(self):
        """分析混淆模式"""
        print("\n🔍 分析混淆模式...")
        
        # 获取所有函数
        result = await self.session.call_tool("list_functions", arguments={"offset": 0, "count": 0})
        functions_data = json.loads(result.content[0].text)
        
        patterns = {
            "junk_instructions": 0,
            "control_flow_obfuscation": 0,
            "indirect_calls": 0,
            "fake_functions": 0,
            "real_functions": 0
        }
        
        for func in functions_data["data"]:
            addr = func["address"]
            name = func["name"]
            size = int(func["size"], 16)
            
            # 反汇编函数
            try:
                disasm_result = await self.session.call_tool(
                    "disassemble_function", 
                    arguments={"start_address": addr}
                )
                disasm_text = disasm_result.content[0].text
                
                # 分析混淆模式
                analysis = self.analyze_function_obfuscation(disasm_text, size)
                
                for pattern, count in analysis.items():
                    patterns[pattern] += count
                    
                # 保存函数信息
                self.functions[addr] = {
                    "name": name,
                    "size": size,
                    "disasm": disasm_text,
                    "analysis": analysis,
                    "is_real": analysis["real_functions"] > 0
                }
                
            except Exception as e:
                print(f"⚠️ 分析函数 {addr} 失败: {e}")
                continue
        
        print(f"📊 混淆分析结果:")
        for pattern, count in patterns.items():
            print(f"  - {pattern}: {count}")
            
        return patterns
    
    def analyze_function_obfuscation(self, disasm_text: str, size: int) -> Dict[str, int]:
        """分析单个函数的混淆"""
        analysis = {
            "junk_instructions": 0,
            "control_flow_obfuscation": 0,
            "indirect_calls": 0,
            "fake_functions": 0,
            "real_functions": 0
        }
        
        lines = disasm_text.split('\n')
        
        # 垃圾指令模式
        junk_patterns = [
            r'pushf',
            r'popf',
            r'nop',
            r'xor\s+\w+,\s*\w+\s*;\s*same register',
            r'add\s+\w+,\s*0',
            r'sub\s+\w+,\s*0',
            r'or\s+\w+,\s*0',
            r'and\s+\w+,\s*0xFFFFFFFF',
            r'bswap\s+\w+.*bswap\s+\w+',  # 连续字节交换
            r'neg\s+\w+.*neg\s+\w+',      # 连续取反
            r'not\s+\w+.*not\s+\w+',      # 连续取反
        ]
        
        # 控制流混淆模式
        control_flow_patterns = [
            r'jmp\s+loc_',
            r'call\s+loc_.*jmp\s+loc_',
            r'pushf.*jmp',
            r'bt\s+\w+,\s*\w+.*j\w+',
            r'bts\s+\w+,\s*\w+',
            r'btc\s+\w+,\s*\w+',
            r'btr\s+\w+,\s*\w+',
        ]
        
        # 间接调用模式
        indirect_patterns = [
            r'call\s+\[',
            r'jmp\s+\[',
            r'call\s+e\w+',
            r'jmp\s+e\w+',
        ]
        
        for line in lines:
            line = line.strip()
            if not line or ':' not in line:
                continue
                
            instruction = line.split(':', 1)[1].strip() if ':' in line else line
            
            # 检查垃圾指令
            for pattern in junk_patterns:
                if re.search(pattern, instruction, re.IGNORECASE):
                    analysis["junk_instructions"] += 1
                    break
            
            # 检查控制流混淆
            for pattern in control_flow_patterns:
                if re.search(pattern, instruction, re.IGNORECASE):
                    analysis["control_flow_obfuscation"] += 1
                    break
            
            # 检查间接调用
            for pattern in indirect_patterns:
                if re.search(pattern, instruction, re.IGNORECASE):
                    analysis["indirect_calls"] += 1
                    break
        
        # 判断是否为真实函数
        instruction_count = len([l for l in lines if ':' in l])
        junk_ratio = analysis["junk_instructions"] / max(instruction_count, 1)
        
        if junk_ratio < 0.3 and size > 10:  # 垃圾指令比例低且有一定大小
            analysis["real_functions"] = 1
        else:
            analysis["fake_functions"] = 1
            
        return analysis
    
    async def identify_real_functions(self):
        """识别真实函数"""
        print("\n🎯 识别真实函数...")
        
        real_functions = []
        
        for addr, func_info in self.functions.items():
            if func_info["is_real"]:
                real_functions.append({
                    "address": addr,
                    "name": func_info["name"],
                    "size": func_info["size"],
                    "confidence": self.calculate_confidence(func_info["analysis"])
                })
        
        # 按置信度排序
        real_functions.sort(key=lambda x: x["confidence"], reverse=True)
        
        print(f"✅ 发现 {len(real_functions)} 个可能的真实函数:")
        for i, func in enumerate(real_functions[:10]):  # 显示前10个
            print(f"  {i+1}. {func['address']} - {func['name']} (置信度: {func['confidence']:.2f})")
        
        return real_functions
    
    def calculate_confidence(self, analysis: Dict[str, int]) -> float:
        """计算函数真实性置信度"""
        total_instructions = sum(analysis.values())
        if total_instructions == 0:
            return 0.0
        
        junk_ratio = analysis["junk_instructions"] / total_instructions
        control_flow_ratio = analysis["control_flow_obfuscation"] / total_instructions
        
        # 垃圾指令越少，置信度越高
        confidence = 1.0 - (junk_ratio * 0.7 + control_flow_ratio * 0.3)
        return max(0.0, min(1.0, confidence))
    
    async def deobfuscate_function(self, address: str):
        """反混淆单个函数"""
        print(f"\n🔧 反混淆函数 {address}...")
        
        try:
            # 获取反汇编代码
            disasm_result = await self.session.call_tool(
                "disassemble_function", 
                arguments={"start_address": address}
            )
            disasm_text = disasm_result.content[0].text
            
            # 尝试反编译
            try:
                decompile_result = await self.session.call_tool(
                    "decompile_function", 
                    arguments={"address": address}
                )
                decompile_text = decompile_result.content[0].text
                print(f"✅ 反编译成功")
                return self.clean_decompiled_code(decompile_text)
                
            except Exception as e:
                print(f"⚠️ 反编译失败，使用反汇编: {e}")
                return self.clean_assembly_code(disasm_text)
                
        except Exception as e:
            print(f"❌ 反混淆失败: {e}")
            return None
    
    def clean_decompiled_code(self, code: str) -> str:
        """清理反编译代码"""
        lines = code.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line = line.strip()
            
            # 跳过注释行和空行
            if not line or line.startswith('/*') or line.startswith('//'):
                continue
            
            # 移除行号注释
            if re.match(r'/\* line: \d+', line):
                continue
            
            # 清理无意义的变量名
            line = re.sub(r'\bv\d+\b', 'var', line)
            line = re.sub(r'\ba\d+\b', 'arg', line)
            
            cleaned_lines.append(line)
        
        return '\n'.join(cleaned_lines)
    
    def clean_assembly_code(self, code: str) -> str:
        """清理汇编代码"""
        lines = code.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 跳过垃圾指令
            if any(pattern in line.lower() for pattern in ['pushf', 'popf', 'nop']):
                continue
            
            # 跳过无意义的位操作
            if re.search(r'bswap.*bswap|neg.*neg|not.*not', line, re.IGNORECASE):
                continue
            
            cleaned_lines.append(line)
        
        return '\n'.join(cleaned_lines)
    
    async def find_time_related_functions(self):
        """寻找时间相关函数"""
        print("\n⏰ 寻找时间相关函数...")
        
        time_keywords = [
            "time", "clock", "tick", "hour", "minute", "second",
            "24", "timer", "delay", "wait", "sleep", "timeout"
        ]
        
        time_functions = []
        
        for addr, func_info in self.functions.items():
            if not func_info["is_real"]:
                continue
            
            disasm = func_info["disasm"].lower()
            
            # 检查是否包含时间相关关键词
            for keyword in time_keywords:
                if keyword in disasm:
                    time_functions.append({
                        "address": addr,
                        "name": func_info["name"],
                        "keyword": keyword,
                        "confidence": func_info.get("confidence", 0)
                    })
                    break
        
        print(f"✅ 发现 {len(time_functions)} 个可能的时间相关函数:")
        for func in time_functions:
            print(f"  - {func['address']} - {func['name']} (关键词: {func['keyword']})")
        
        return time_functions
    
    async def generate_deobfuscation_report(self):
        """生成反混淆报告"""
        print("\n📋 生成反混淆报告...")
        
        # 分析混淆模式
        patterns = await self.analyze_obfuscation_patterns()
        
        # 识别真实函数
        real_functions = await self.identify_real_functions()
        
        # 寻找时间相关函数
        time_functions = await self.find_time_related_functions()
        
        # 生成报告
        report = {
            "dll_info": {
                "name": "Uonline.dll",
                "base": "0x10000000",
                "size": "0x58000"
            },
            "obfuscation_analysis": patterns,
            "real_functions": real_functions[:20],  # 前20个最可能的真实函数
            "time_functions": time_functions,
            "recommendations": self.generate_recommendations(patterns, real_functions, time_functions)
        }
        
        return report
    
    def generate_recommendations(self, patterns, real_functions, time_functions):
        """生成反混淆建议"""
        recommendations = []
        
        if patterns["junk_instructions"] > 1000:
            recommendations.append("检测到大量垃圾指令，建议使用自动化工具清理")
        
        if patterns["control_flow_obfuscation"] > 500:
            recommendations.append("检测到严重的控制流混淆，需要控制流图重建")
        
        if len(real_functions) < 10:
            recommendations.append("真实函数识别困难，可能使用了高级混淆技术")
        
        if len(time_functions) == 0:
            recommendations.append("未发现明显的时间相关函数，24小时限制可能在其他模块中")
        else:
            recommendations.append(f"发现 {len(time_functions)} 个时间相关函数，建议重点分析")
        
        return recommendations

async def main():
    print("=" * 60)
    print("🛠️  Uonline.dll 专业反混淆工具")
    print("=" * 60)
    
    deobfuscator = UonlineDeobfuscator()
    
    # 连接IDA Pro
    if not await deobfuscator.connect_ida():
        return
    
    try:
        # 生成完整的反混淆报告
        report = await deobfuscator.generate_deobfuscation_report()
        
        # 保存报告
        with open("deobfuscation_report.json", "w", encoding="utf-8") as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print("\n" + "=" * 60)
        print("📊 反混淆分析完成！")
        print("=" * 60)
        
        print(f"\n🎯 真实函数: {len(report['real_functions'])}")
        print(f"⏰ 时间函数: {len(report['time_functions'])}")
        print(f"🗑️ 垃圾指令: {report['obfuscation_analysis']['junk_instructions']}")
        print(f"🔀 控制流混淆: {report['obfuscation_analysis']['control_flow_obfuscation']}")
        
        print("\n💡 建议:")
        for rec in report['recommendations']:
            print(f"  - {rec}")
        
        print(f"\n📄 详细报告已保存到: deobfuscation_report.json")
        
        # 如果发现时间相关函数，进行深度分析
        if report['time_functions']:
            print("\n🔍 对时间相关函数进行深度分析...")
            for func in report['time_functions'][:3]:  # 分析前3个
                cleaned_code = await deobfuscator.deobfuscate_function(func['address'])
                if cleaned_code:
                    print(f"\n📝 函数 {func['address']} 清理后的代码:")
                    print("=" * 40)
                    print(cleaned_code[:500] + "..." if len(cleaned_code) > 500 else cleaned_code)
                    print("=" * 40)
        
    except Exception as e:
        print(f"❌ 分析过程出错: {e}")
    
    finally:
        # 清理连接
        if hasattr(deobfuscator, 'session') and deobfuscator.session:
            await deobfuscator.session.__aexit__(None, None, None)
        if hasattr(deobfuscator, 'client') and deobfuscator.client:
            await deobfuscator.client.__aexit__(None, None, None)

if __name__ == "__main__":
    try:
        asyncio.run(main())
        input("\n按回车键退出...")
    except Exception as e:
        print(f"❌ 程序出错: {e}")
        input("按回车键退出...")
