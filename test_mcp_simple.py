#!/usr/bin/env python3
"""
简单测试MCP是否可用
"""

try:
    from mcp.server import Server
    from mcp.server.stdio import stdio_server
    from mcp.types import Tool, TextContent
    print("✅ MCP模块导入成功")
    
    # 创建简单服务器
    app = Server("test-server")
    
    @app.list_tools()
    async def list_tools():
        return [
            Tool(
                name="test_tool",
                description="测试工具",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "message": {"type": "string", "description": "测试消息"}
                    },
                    "required": ["message"]
                }
            )
        ]
    
    @app.call_tool()
    async def call_tool(name: str, arguments: dict):
        if name == "test_tool":
            return [TextContent(type="text", text=f"收到消息: {arguments['message']}")]
        else:
            raise ValueError(f"Unknown tool: {name}")
    
    print("✅ MCP服务器创建成功")
    print("🚀 启动MCP服务器...")
    
    import asyncio
    
    async def main():
        async with stdio_server() as (read_stream, write_stream):
            await app.run(read_stream, write_stream, app.create_initialization_options())
    
    if __name__ == "__main__":
        asyncio.run(main())

except ImportError as e:
    print(f"❌ MCP模块导入失败: {e}")
    print("📋 请安装MCP:")
    print("   pip install mcp")
    print("   或者 pip install anthropic-mcp")

except Exception as e:
    print(f"❌ MCP服务器创建失败: {e}")
