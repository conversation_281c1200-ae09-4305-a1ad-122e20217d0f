#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试MCP二进制分析工具
"""

import asyncio
import json
import subprocess
import sys
from pathlib import Path

# 尝试导入MCP
try:
    from mcp import ClientSession, StdioServerParameters
    from mcp.client.stdio import stdio_client
    MCP_AVAILABLE = True
except ImportError:
    MCP_AVAILABLE = False

async def test_binary_analyzer():
    """测试二进制分析器MCP工具"""
    if not MCP_AVAILABLE:
        print("❌ MCP不可用")
        return
    
    try:
        # 连接到我们的二进制分析器
        server_params = StdioServerParameters(
            command="python",
            args=["binary_analyzer_mcp.py"]
        )
        
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                print("✅ 成功连接到二进制分析器MCP")
                
                # 获取可用工具
                tools_result = await session.list_tools()
                print(f"📋 可用工具: {[tool.name for tool in tools_result.tools]}")
                
                # 测试分析Prmain.exe
                target_file = Path("Uonline/Prmain.exe")
                if target_file.exists():
                    print(f"\n🎯 分析 {target_file}...")
                    
                    # PE结构分析
                    print("\n📋 PE结构分析:")
                    pe_result = await session.call_tool(
                        "analyze_pe_structure",
                        arguments={"file_path": str(target_file.absolute())}
                    )
                    print(pe_result.content[0].text)
                    
                    # API调用分析
                    print("\n📋 API调用分析:")
                    api_result = await session.call_tool(
                        "find_api_calls",
                        arguments={
                            "file_path": str(target_file.absolute()),
                            "api_names": ["timeGetTime", "GetSystemTime", "GetLocalTime", "send", "recv"]
                        }
                    )
                    print(api_result.content[0].text)
                    
                    # 时间常量分析
                    print("\n📋 时间常量分析:")
                    time_result = await session.call_tool(
                        "find_time_constants",
                        arguments={"file_path": str(target_file.absolute())}
                    )
                    print(time_result.content[0].text)
                    
                    # 字符串提取
                    print("\n📋 字符串提取:")
                    string_result = await session.call_tool(
                        "extract_strings",
                        arguments={
                            "file_path": str(target_file.absolute()),
                            "min_length": 4
                        }
                    )
                    print(string_result.content[0].text)
                
                else:
                    print(f"❌ 文件不存在: {target_file}")
                
    except Exception as e:
        print(f"❌ MCP测试失败: {e}")

def run_direct_analysis():
    """直接运行分析（不使用MCP）"""
    print("🔧 运行直接分析...")
    
    target_file = Path("Uonline/Prmain.exe")
    if not target_file.exists():
        print(f"❌ 文件不存在: {target_file}")
        return
    
    try:
        with open(target_file, 'rb') as f:
            data = f.read()
        
        print(f"📁 文件大小: {len(data):,} 字节")
        
        # 检查PE头
        if data[:2] == b'MZ':
            print("✅ 有效的PE文件")
            
            # 查找timeGetTime
            timegettime_count = data.count(b'timeGetTime')
            print(f"🕐 timeGetTime出现次数: {timegettime_count}")
            
            # 查找24小时相关
            hour24_count = data.count(b'24')
            print(f"🔢 '24'出现次数: {hour24_count}")
            
            # 查找网络API
            send_count = data.count(b'send')
            recv_count = data.count(b'recv')
            print(f"🌐 网络API: send({send_count}), recv({recv_count})")
            
        else:
            print("❌ 不是有效的PE文件")
            
    except Exception as e:
        print(f"❌ 直接分析失败: {e}")

async def main():
    print("=" * 60)
    print("🧪 MCP二进制分析工具测试")
    print("=" * 60)
    print()
    
    if MCP_AVAILABLE:
        print("✅ MCP可用，测试MCP工具...")
        await test_binary_analyzer()
    else:
        print("❌ MCP不可用，运行直接分析...")
        run_direct_analysis()
    
    print("\n" + "="*60)
    print("🎯 测试完成")
    print("="*60)

if __name__ == "__main__":
    try:
        if MCP_AVAILABLE:
            asyncio.run(main())
        else:
            asyncio.run(main())
        
        input("\n按回车键退出...")
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        input("按回车键退出...")
