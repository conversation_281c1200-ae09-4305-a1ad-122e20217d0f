#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析所有关键DLL文件，寻找24小时限制逻辑
"""

import os
import re
from pathlib import Path

def analyze_dll_for_time_patterns(dll_path):
    """分析单个DLL的时间模式"""
    if not os.path.exists(dll_path):
        return None
    
    print(f"\n🔍 分析 {dll_path}...")
    
    try:
        with open(dll_path, 'rb') as f:
            data = f.read()
        
        results = {
            'file_size': len(data),
            'time_apis': [],
            'patterns_24': 0,
            'patterns_86400': 0,
            'ascii_24': 0,
            'suspicious_strings': []
        }
        
        # 查找时间相关API
        time_apis = [
            b'GetSystemTime', b'GetLocalTime', b'GetTickCount', b'timeGetTime',
            b'QueryPerformanceCounter', b'GetSystemTimeAsFileTime'
        ]
        
        for api in time_apis:
            if api in data:
                results['time_apis'].append(api.decode('ascii'))
        
        # 查找24的各种表示
        results['patterns_24'] = data.count(b'\x18')  # 24 as byte
        results['ascii_24'] = data.count(b'24')       # ASCII "24"
        results['patterns_86400'] = data.count(b'\x80\x51\x01\x00')  # 86400 seconds
        
        # 查找可疑字符串
        try:
            text_data = data.decode('ascii', errors='ignore')
            keywords = ['hour', 'time', 'limit', 'restrict', 'expire', 'transfer', 'trade', 'check']
            for keyword in keywords:
                if keyword in text_data.lower():
                    results['suspicious_strings'].append(keyword)
        except:
            pass
        
        # 显示结果
        print(f"  📊 文件大小: {results['file_size']:,} 字节")
        
        if results['time_apis']:
            print(f"  ⏰ 时间API: {', '.join(results['time_apis'])}")
        
        if results['patterns_24'] > 0:
            print(f"  🎯 数字24模式: {results['patterns_24']} 次")
        
        if results['ascii_24'] > 0:
            print(f"  🎯 ASCII '24': {results['ascii_24']} 次")
        
        if results['patterns_86400'] > 0:
            print(f"  🎯 86400秒模式: {results['patterns_86400']} 次")
        
        if results['suspicious_strings']:
            print(f"  🔍 可疑关键词: {', '.join(set(results['suspicious_strings']))}")
        
        return results
        
    except Exception as e:
        print(f"  ❌ 分析失败: {e}")
        return None

def main():
    print("=" * 70)
    print("🔍 Uonline 所有DLL时间验证分析")
    print("=" * 70)
    
    # 要分析的DLL列表
    dlls_to_analyze = [
        "Uonline/white.dll",
        "Uonline/DX.dll", 
        "Uonline/GrpLib.dll",
        "Uonline/Wav.dll",
        "Uonline/soundlib.dll",
        "Uonline/bugslayerutil.dll",
        "Uonline/dbghelp.dll"
    ]
    
    results = {}
    
    for dll_path in dlls_to_analyze:
        result = analyze_dll_for_time_patterns(dll_path)
        if result:
            results[dll_path] = result
    
    print("\n" + "=" * 70)
    print("📊 分析总结")
    print("=" * 70)
    
    # 按可疑程度排序
    suspicious_dlls = []
    
    for dll_path, result in results.items():
        score = 0
        reasons = []
        
        # 计算可疑分数
        if result['time_apis']:
            score += len(result['time_apis']) * 10
            reasons.append(f"{len(result['time_apis'])}个时间API")
        
        if result['patterns_24'] > 10:
            score += 5
            reasons.append(f"{result['patterns_24']}个24模式")
        
        if result['ascii_24'] > 0:
            score += 3
            reasons.append(f"{result['ascii_24']}个ASCII'24'")
        
        if result['patterns_86400'] > 0:
            score += 15
            reasons.append(f"{result['patterns_86400']}个86400秒模式")
        
        if result['suspicious_strings']:
            score += len(set(result['suspicious_strings'])) * 2
            reasons.append(f"{len(set(result['suspicious_strings']))}个可疑关键词")
        
        if score > 0:
            suspicious_dlls.append((dll_path, score, reasons))
    
    # 按分数排序
    suspicious_dlls.sort(key=lambda x: x[1], reverse=True)
    
    print(f"\n🎯 可疑DLL排名 (按可能包含24小时限制逻辑排序):")
    
    if suspicious_dlls:
        for i, (dll_path, score, reasons) in enumerate(suspicious_dlls, 1):
            dll_name = os.path.basename(dll_path)
            print(f"\n  {i}. 🔥 {dll_name} (分数: {score})")
            print(f"     原因: {', '.join(reasons)}")
            
            # 给出分析建议
            if score >= 20:
                print(f"     💡 建议: 🔥 高优先级 - 立即进行深度逆向分析")
            elif score >= 10:
                print(f"     💡 建议: ⚠️ 中优先级 - 值得进一步分析")
            else:
                print(f"     💡 建议: ℹ️ 低优先级 - 可能包含相关逻辑")
    else:
        print("  ❓ 未发现明显的时间验证特征")
    
    print(f"\n🚀 下一步行动建议:")
    
    if suspicious_dlls:
        top_dll = suspicious_dlls[0]
        dll_name = os.path.basename(top_dll[0])
        print(f"  1. 🎯 重点分析 {dll_name}")
        print(f"  2. 🔧 使用IDA Pro进行静态分析")
        print(f"  3. 🔍 使用调试器进行动态分析")
        print(f"  4. 🌐 分析网络通信协议")
        
        if 'white.dll' in top_dll[0]:
            print(f"  5. 🔥 white.dll是最可疑的文件 - 可能是专门的验证模块")
    else:
        print(f"  1. 🔍 分析主程序 Prmain.exe")
        print(f"  2. 🌐 重点分析网络协议和服务器验证")
        print(f"  3. 📦 检查PAK文件中是否包含验证逻辑")
    
    print(f"\n⚠️ 重要提醒:")
    print(f"  - 24小时限制可能在服务器端验证")
    print(f"  - 需要结合网络抓包分析")
    print(f"  - 考虑搭建私服进行测试")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
