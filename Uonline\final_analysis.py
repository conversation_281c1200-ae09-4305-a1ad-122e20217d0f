#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终分析 - 提取Uonline的真实可用数据
"""

import os
import struct

def analyze_file_size():
    """分析文件大小"""
    print("📊 文件大小统计:")
    
    files = ['Prmain.exe', 'Uonline.dll', 'netmine.dll', 'GrpLib.dll', 'Message.ctf']
    
    for filename in files:
        if os.path.exists(filename):
            size = os.path.getsize(filename)
            print(f"  {filename}: {size:,} 字节")

def extract_ip_addresses():
    """从二进制文件中提取IP地址"""
    print("\n🌐 提取IP地址:")
    
    files = ['Prmain.exe', 'Uonline.dll', 'netmine.dll']
    
    for filename in files:
        if os.path.exists(filename):
            try:
                with open(filename, 'rb') as f:
                    data = f.read()
                
                # 查找IP地址模式 (4字节)
                ips = []
                for i in range(len(data) - 3):
                    try:
                        # 读取4字节作为IP
                        ip_bytes = data[i:i+4]
                        ip = f"{ip_bytes[0]}.{ip_bytes[1]}.{ip_bytes[2]}.{ip_bytes[3]}"
                        
                        # 检查是否是有效IP (避免0.0.0.0和***************)
                        if (1 <= ip_bytes[0] <= 223 and 
                            0 <= ip_bytes[1] <= 255 and 
                            0 <= ip_bytes[2] <= 255 and 
                            1 <= ip_bytes[3] <= 254):
                            ips.append((i, ip))
                    except:
                        continue
                
                # 去重
                unique_ips = list(set([ip for _, ip in ips]))
                
                if unique_ips:
                    print(f"  {filename}:")
                    for ip in unique_ips[:10]:
                        print(f"    {ip}")
                        
            except Exception as e:
                print(f"  {filename}: 分析失败 - {e}")

def extract_ports():
    """提取端口号"""
    print("\n🔌 提取端口号:")
    
    files = ['Prmain.exe', 'Uonline.dll', 'netmine.dll']
    
    for filename in files:
        if os.path.exists(filename):
            try:
                with open(filename, 'rb') as f:
                    data = f.read()
                
                ports = []
                for i in range(len(data) - 1):
                    try:
                        # 读取2字节作为端口 (网络字节序)
                        port = struct.unpack('>H', data[i:i+2])[0]
                        if 1000 <= port <= 65535:  # 常见端口范围
                            ports.append(port)
                    except:
                        continue
                
                # 统计最常见的端口
                port_count = {}
                for port in ports:
                    port_count[port] = port_count.get(port, 0) + 1
                
                # 排序并显示前10个
                sorted_ports = sorted(port_count.items(), key=lambda x: x[1], reverse=True)
                
                if sorted_ports:
                    print(f"  {filename}:")
                    for port, count in sorted_ports[:10]:
                        print(f"    {port} (出现{count}次)")
                        
            except Exception as e:
                print(f"  {filename}: 分析失败 - {e}")

def analyze_message_ctf_simple():
    """简单分析Message.ctf"""
    print("\n📄 Message.ctf 分析:")
    
    if not os.path.exists('Message.ctf'):
        print("  文件不存在")
        return
    
    try:
        with open('Message.ctf', 'rb') as f:
            data = f.read()
        
        print(f"  文件大小: {len(data):,} 字节")
        
        # 显示文件头
        if len(data) >= 16:
            header = data[:16]
            print(f"  文件头: {' '.join(f'{b:02X}' for b in header)}")
        
        # 查找可读文本
        text_parts = []
        current = ""
        
        for byte in data:
            if 32 <= byte <= 126:  # 可打印ASCII
                current += chr(byte)
            else:
                if len(current) >= 6:
                    text_parts.append(current)
                current = ""
        
        if current and len(current) >= 6:
            text_parts.append(current)
        
        print(f"  找到 {len(text_parts)} 个文本片段")
        if text_parts:
            print("  前10个文本:")
            for i, text in enumerate(text_parts[:10]):
                print(f"    {i+1}: {text}")
                
    except Exception as e:
        print(f"  分析失败: {e}")

def analyze_pak_headers():
    """分析PAK文件头"""
    print("\n📦 PAK文件头分析:")
    
    pak_files = [f for f in os.listdir('.') if f.endswith('.pak')]
    
    for pak in pak_files[:5]:  # 只分析前5个
        try:
            with open(pak, 'rb') as f:
                header = f.read(32)
            
            print(f"  {pak}:")
            print(f"    头部: {' '.join(f'{b:02X}' for b in header[:16])}")
            
            # 尝试解析为结构
            if len(header) >= 16:
                try:
                    magic, version, count, offset = struct.unpack('<IIII', header[:16])
                    print(f"    可能结构: Magic=0x{magic:08X}, Ver={version}, Count={count}, Offset=0x{offset:08X}")
                except:
                    pass
                    
        except Exception as e:
            print(f"    分析失败: {e}")

def find_function_patterns():
    """查找函数模式"""
    print("\n🎯 查找函数模式:")
    
    files = ['Prmain.exe', 'Uonline.dll']
    
    for filename in files:
        if os.path.exists(filename):
            try:
                with open(filename, 'rb') as f:
                    data = f.read()
                
                # 查找函数序言 (55 8B EC - push ebp; mov ebp, esp)
                prologues = []
                for i in range(len(data) - 2):
                    if data[i:i+3] == b'\x55\x8B\xEC':
                        prologues.append(i)
                
                print(f"  {filename}: 找到 {len(prologues)} 个函数序言")
                
                # 查找字符串引用
                string_refs = []
                for i in range(len(data) - 4):
                    if data[i] == 0x68:  # PUSH指令
                        addr = struct.unpack('<I', data[i+1:i+5])[0]
                        if 0x400000 <= addr <= 0x800000:  # 典型代码段地址
                            string_refs.append((i, addr))
                
                print(f"  {filename}: 找到 {len(string_refs)} 个可能的字符串引用")
                
            except Exception as e:
                print(f"  {filename}: 分析失败 - {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 Uonline 最终逆向分析")
    print("=" * 60)
    
    # 文件大小统计
    analyze_file_size()
    
    # 提取IP地址
    extract_ip_addresses()
    
    # 提取端口
    extract_ports()
    
    # 分析Message.ctf
    analyze_message_ctf_simple()
    
    # 分析PAK文件
    analyze_pak_headers()
    
    # 查找函数模式
    find_function_patterns()
    
    print(f"\n" + "=" * 60)
    print("✅ 最终分析完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
