#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度分析Uonline - 提取内存地址、API调用、游戏逻辑
"""

import struct
import os
import re

def analyze_pe_imports(filename):
    """分析PE文件的导入表"""
    print(f"\n🔍 分析 {filename} 的导入表...")
    
    if not os.path.exists(filename):
        print(f"❌ {filename} 不存在")
        return []
    
    try:
        with open(filename, 'rb') as f:
            # 读取DOS头
            dos_header = f.read(64)
            if dos_header[:2] != b'MZ':
                return []
            
            pe_offset = struct.unpack('<I', dos_header[60:64])[0]
            f.seek(pe_offset)
            
            # 读取PE头
            pe_sig = f.read(4)
            if pe_sig != b'PE\x00\x00':
                return []
            
            # 读取COFF头
            coff_header = f.read(20)
            machine, sections, timestamp, _, _, opt_size, characteristics = struct.unpack('<HHIIIHH', coff_header)
            
            # 读取可选头
            if opt_size >= 96:
                opt_header = f.read(96)
                # 获取数据目录
                if len(opt_header) >= 96:
                    import_rva = struct.unpack('<I', opt_header[80:84])[0]
                    import_size = struct.unpack('<I', opt_header[84:88])[0]
                    
                    if import_rva > 0:
                        print(f"  导入表RVA: 0x{import_rva:08X}")
                        print(f"  导入表大小: {import_size} 字节")
                        return [import_rva, import_size]
        
        return []
        
    except Exception as e:
        print(f"分析 {filename} 失败: {e}")
        return []

def extract_strings_advanced(filename, min_len=6):
    """高级字符串提取"""
    print(f"\n📝 提取 {filename} 中的字符串...")
    
    if not os.path.exists(filename):
        return {}
    
    try:
        with open(filename, 'rb') as f:
            data = f.read()
        
        # ASCII字符串
        ascii_strings = []
        current = ""
        offset = 0
        
        for i, byte in enumerate(data):
            if 32 <= byte <= 126:
                if not current:
                    offset = i
                current += chr(byte)
            else:
                if len(current) >= min_len:
                    ascii_strings.append((offset, current))
                current = ""
        
        # Unicode字符串 (UTF-16LE)
        unicode_strings = []
        for i in range(0, len(data) - 1, 2):
            try:
                if data[i+1] == 0 and 32 <= data[i] <= 126:
                    # 可能的Unicode字符串开始
                    unicode_str = ""
                    start_offset = i
                    j = i
                    while j < len(data) - 1:
                        if data[j+1] == 0 and 32 <= data[j] <= 126:
                            unicode_str += chr(data[j])
                            j += 2
                        else:
                            break
                    
                    if len(unicode_str) >= min_len:
                        unicode_strings.append((start_offset, unicode_str))
            except:
                continue
        
        # 分类字符串
        categories = {
            'api_calls': [],
            'file_paths': [],
            'urls': [],
            'error_msgs': [],
            'game_strings': [],
            'memory_addrs': []
        }
        
        all_strings = ascii_strings + unicode_strings
        
        for offset, string in all_strings:
            s_lower = string.lower()
            
            # API调用
            if any(api in s_lower for api in ['createfile', 'readfile', 'writefile', 'socket', 'connect', 'send', 'recv', 'loadlibrary', 'getprocaddress']):
                categories['api_calls'].append((offset, string))
            
            # 文件路径
            elif '\\' in string or ('.' in string and any(ext in s_lower for ext in ['.dll', '.exe', '.pak', '.ini', '.log'])):
                categories['file_paths'].append((offset, string))
            
            # URL
            elif 'http' in s_lower or 'www.' in s_lower or '.com' in s_lower:
                categories['urls'].append((offset, string))
            
            # 错误信息
            elif any(word in s_lower for word in ['error', 'fail', 'exception', 'invalid', 'cannot', 'unable']):
                categories['error_msgs'].append((offset, string))
            
            # 内存地址模式
            elif re.match(r'0x[0-9a-fA-F]{6,8}', string):
                categories['memory_addrs'].append((offset, string))
            
            # 游戏相关字符串
            elif len(string) >= 8 and not string.isdigit():
                categories['game_strings'].append((offset, string))
        
        # 输出结果
        for category, strings in categories.items():
            if strings:
                print(f"\n  📂 {category.upper()} ({len(strings)}个):")
                for offset, string in strings[:10]:
                    print(f"    0x{offset:08X}: {string}")
        
        return categories
        
    except Exception as e:
        print(f"字符串提取失败: {e}")
        return {}

def find_function_addresses(filename):
    """查找函数地址"""
    print(f"\n🎯 查找 {filename} 中的函数地址...")
    
    if not os.path.exists(filename):
        return []
    
    try:
        with open(filename, 'rb') as f:
            data = f.read()
        
        # 查找CALL指令 (E8 xx xx xx xx)
        call_addresses = []
        for i in range(len(data) - 4):
            if data[i] == 0xE8:  # CALL指令
                # 读取相对地址
                rel_addr = struct.unpack('<i', data[i+1:i+5])[0]
                abs_addr = i + 5 + rel_addr  # 计算绝对地址
                if 0 < abs_addr < len(data):
                    call_addresses.append((i, abs_addr))
        
        # 查找JMP指令 (E9 xx xx xx xx)
        jmp_addresses = []
        for i in range(len(data) - 4):
            if data[i] == 0xE9:  # JMP指令
                rel_addr = struct.unpack('<i', data[i+1:i+5])[0]
                abs_addr = i + 5 + rel_addr
                if 0 < abs_addr < len(data):
                    jmp_addresses.append((i, abs_addr))
        
        print(f"  找到 {len(call_addresses)} 个CALL指令")
        print(f"  找到 {len(jmp_addresses)} 个JMP指令")
        
        # 显示前10个
        if call_addresses:
            print("  CALL地址:")
            for i, (offset, target) in enumerate(call_addresses[:10]):
                print(f"    0x{offset:08X} -> 0x{target:08X}")
        
        return call_addresses + jmp_addresses
        
    except Exception as e:
        print(f"函数地址查找失败: {e}")
        return []

def analyze_message_ctf():
    """深度分析Message.ctf文件"""
    print(f"\n📄 深度分析 Message.ctf...")
    
    if not os.path.exists('Message.ctf'):
        return
    
    try:
        with open('Message.ctf', 'rb') as f:
            data = f.read()
        
        print(f"文件大小: {len(data):,} 字节")
        
        # 分析文件头
        if len(data) >= 16:
            header = data[:16]
            print(f"文件头: {' '.join(f'{b:02X}' for b in header)}")
            
            # 尝试解析为结构
            try:
                magic, version, count, offset = struct.unpack('<IIII', header)
                print(f"可能的结构:")
                print(f"  Magic: 0x{magic:08X}")
                print(f"  Version: {version}")
                print(f"  Count: {count}")
                print(f"  Offset: 0x{offset:08X}")
            except:
                pass
        
        # 查找文本字符串
        text_strings = []
        current = ""
        
        for i, byte in enumerate(data):
            if 32 <= byte <= 126 or byte in [9, 10, 13]:  # 可打印字符+制表符换行符
                current += chr(byte)
            else:
                if len(current) >= 4:
                    text_strings.append(current)
                current = ""
        
        if current and len(current) >= 4:
            text_strings.append(current)
        
        # 过滤游戏相关文本
        game_texts = []
        for text in text_strings:
            if len(text) >= 6 and not text.isdigit():
                # 可能是游戏文本
                game_texts.append(text)
        
        print(f"找到 {len(game_texts)} 个可能的游戏文本:")
        for i, text in enumerate(game_texts[:20]):
            print(f"  {i+1}: {text}")
        
    except Exception as e:
        print(f"Message.ctf分析失败: {e}")

def main():
    """主函数"""
    print("=" * 70)
    print("🎯 Uonline 深度逆向分析")
    print("=" * 70)
    
    # 分析主要文件
    files_to_analyze = ['Prmain.exe', 'Uonline.dll', 'netmine.dll', 'GrpLib.dll']
    
    for filename in files_to_analyze:
        if os.path.exists(filename):
            print(f"\n{'='*50}")
            print(f"🔍 分析 {filename}")
            print(f"{'='*50}")
            
            # 文件基本信息
            size = os.path.getsize(filename)
            print(f"文件大小: {size:,} 字节")
            
            # PE导入表分析
            analyze_pe_imports(filename)
            
            # 字符串提取
            extract_strings_advanced(filename)
            
            # 函数地址查找
            find_function_addresses(filename)
    
    # 分析Message.ctf
    analyze_message_ctf()
    
    print(f"\n" + "=" * 70)
    print("✅ 深度分析完成")
    print("=" * 70)

if __name__ == "__main__":
    main()
