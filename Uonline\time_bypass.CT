<?xml version="1.0" encoding="utf-8"?>
<CheatTable>
  <CheatEntries>
    <CheatEntry>
      <ID>0</ID>
      <Description>"天之游侠 - 移除24小时物品限制"</Description>
      <LastState/>
      <VariableType>Auto Assembler Script</VariableType>
      <AssemblerScript>[ENABLE]
// 天之游侠 24小时物品限制绕过
// 查找并修改时间检查函数

aobscanmodule(timecheck1,Prmain.exe,80 51 01 00) // 86400秒 (24小时)
registersymbol(timecheck1)

aobscanmodule(timecheck2,Prmain.exe,00 51 01 00) // 86400秒变体
registersymbol(timecheck2)

// 修改时间检查为总是返回true (绕过限制)
timecheck1:
  mov eax,#1
  nop
  nop
  nop

timecheck2:
  mov eax,#1
  nop
  nop
  nop

[DISABLE]
// 恢复原始代码
timecheck1:
  db 80 51 01 00

timecheck2:
  db 00 51 01 00

unregistersymbol(timecheck1)
unregistersymbol(timecheck2)
</AssemblerScript>
    </CheatEntry>
    
    <CheatEntry>
      <ID>1</ID>
      <Description>"物品冷却时间修改"</Description>
      <LastState/>
      <VariableType>4 Bytes</VariableType>
      <Address>Prmain.exe+15180</Address>
      <Offsets>
        <Offset>0</Offset>
      </Offsets>
    </CheatEntry>
    
    <CheatEntry>
      <ID>2</ID>
      <Description>"强制重置物品使用时间"</Description>
      <LastState/>
      <VariableType>Auto Assembler Script</VariableType>
      <AssemblerScript>[ENABLE]
// 查找物品使用时间存储位置并重置
alloc(newmem,1024)
label(code)
label(return)

newmem:
  // 当游戏检查物品使用时间时，总是返回可以使用
  mov eax,0  // 设置为0表示没有冷却
  jmp return

code:
  // 原始代码会被替换
  jmp return

return:

[DISABLE]
dealloc(newmem)
</AssemblerScript>
    </CheatEntry>
  </CheatEntries>
</CheatTable>
