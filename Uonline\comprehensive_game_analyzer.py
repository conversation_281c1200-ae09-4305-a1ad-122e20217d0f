#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天之游侠游戏架构全面分析器
"""

import os
import struct
import hashlib
import json
from pathlib import Path
from datetime import datetime

class GameArchitectureAnalyzer:
    def __init__(self, game_dir):
        self.game_dir = Path(game_dir)
        self.analysis_results = {}
        
    def analyze_file_structure(self):
        """分析文件结构"""
        print("=== 文件结构分析 ===")
        
        file_categories = {
            'executables': [],
            'libraries': [],
            'resources': [],
            'configs': [],
            'maps': [],
            'characters': [],
            'items': [],
            'sounds': [],
            'anti_cheat': [],
            'tools': []
        }
        
        for file_path in self.game_dir.rglob('*'):
            if file_path.is_file():
                name = file_path.name.lower()
                size = file_path.stat().st_size
                
                # 分类文件
                if name.endswith('.exe'):
                    file_categories['executables'].append((str(file_path), size))
                elif name.endswith('.dll'):
                    file_categories['libraries'].append((str(file_path), size))
                elif name.endswith('.pak'):
                    if 'map-' in name:
                        file_categories['maps'].append((str(file_path), size))
                    elif any(x in name for x in ['item', 'eqp']):
                        file_categories['items'].append((str(file_path), size))
                    elif any(x in name for x in ['i_', 't_', 'c_']):
                        file_categories['characters'].append((str(file_path), size))
                    else:
                        file_categories['resources'].append((str(file_path), size))
                elif name.endswith(('.ini', '.cfg', '.txt', '.xml')):
                    file_categories['configs'].append((str(file_path), size))
                elif name.endswith(('.mp3', '.wav')):
                    file_categories['sounds'].append((str(file_path), size))
                elif 'hackshield' in name or 'hshield' in name:
                    file_categories['anti_cheat'].append((str(file_path), size))
                elif name.endswith(('.py', '.ps1', '.bat', '.ct')):
                    file_categories['tools'].append((str(file_path), size))
                    
        self.analysis_results['file_structure'] = file_categories
        
        # 打印统计
        for category, files in file_categories.items():
            total_size = sum(size for _, size in files)
            print(f"{category}: {len(files)} 文件, 总大小: {total_size:,} 字节")
            
    def analyze_pak_files(self):
        """深度分析PAK文件"""
        print("\n=== PAK文件深度分析 ===")
        
        pak_analysis = {}
        
        # 重点分析的PAK文件
        important_paks = [
            'item0001.pak', 'item0002.pak', 'item0003.pak',
            'eqp0001.pak', 'eqp0002.pak', 'eqp0003.pak', 'eqp0004.pak',
            'etc.pak', 'Common.pak'
        ]
        
        for pak_name in important_paks:
            pak_path = self.game_dir / pak_name
            if pak_path.exists():
                print(f"\n分析: {pak_name}")
                analysis = self.analyze_single_pak(pak_path)
                pak_analysis[pak_name] = analysis
                
        self.analysis_results['pak_files'] = pak_analysis
        
    def analyze_single_pak(self, pak_path):
        """分析单个PAK文件"""
        try:
            with open(pak_path, 'rb') as f:
                data = f.read()
                
            analysis = {
                'size': len(data),
                'md5': hashlib.md5(data).hexdigest(),
                'header_analysis': self.analyze_pak_header(data),
                'content_analysis': self.analyze_pak_content(data),
                'hp_data': self.find_hp_data_in_pak(data),
                'time_restrictions': self.find_time_restrictions_in_pak(data)
            }
            
            print(f"  大小: {len(data):,} 字节")
            print(f"  MD5: {analysis['md5']}")
            print(f"  HP+数据: {len(analysis['hp_data'])} 条")
            print(f"  时间限制: {len(analysis['time_restrictions'])} 条")
            
            return analysis
            
        except Exception as e:
            print(f"  分析失败: {e}")
            return {'error': str(e)}
            
    def analyze_pak_header(self, data):
        """分析PAK文件头"""
        if len(data) < 64:
            return {'error': 'File too small'}
            
        # 尝试不同的头部格式
        header_info = {}
        
        # 前16字节
        header_info['first_16_bytes'] = data[:16].hex()
        
        # 查找可能的签名
        possible_signatures = []
        for i in range(0, min(64, len(data)), 4):
            chunk = data[i:i+4]
            if all(32 <= b <= 126 for b in chunk):  # 可打印ASCII
                possible_signatures.append((i, chunk.decode('ascii', errors='ignore')))
                
        header_info['possible_signatures'] = possible_signatures
        
        # 查找可能的文件数量和偏移
        for i in range(0, min(64, len(data) - 4), 4):
            value = struct.unpack('<I', data[i:i+4])[0]
            if 1 <= value <= 10000:  # 合理的文件数量范围
                header_info[f'possible_file_count_at_{i}'] = value
                
        return header_info
        
    def analyze_pak_content(self, data):
        """分析PAK文件内容"""
        content_info = {}
        
        # 查找文件扩展名
        extensions = set()
        for i in range(len(data) - 4):
            if data[i] == ord('.'):
                ext = data[i:i+4]
                if all(97 <= b <= 122 or b == ord('.') for b in ext):  # 小写字母
                    extensions.add(ext.decode('ascii', errors='ignore'))
                    
        content_info['found_extensions'] = list(extensions)
        
        # 查找可能的文件名
        filenames = []
        current_name = b""
        for byte in data:
            if 32 <= byte <= 126:  # 可打印字符
                current_name += bytes([byte])
            else:
                if len(current_name) > 5 and b'.' in current_name:
                    try:
                        name = current_name.decode('ascii')
                        if any(ext in name for ext in ['.bmp', '.txt', '.dat', '.cfg']):
                            filenames.append(name)
                    except:
                        pass
                current_name = b""
                
        content_info['possible_filenames'] = filenames[:20]  # 只保留前20个
        
        return content_info
        
    def find_hp_data_in_pak(self, data):
        """在PAK文件中查找HP数据"""
        hp_data = []
        
        i = 0
        while i < len(data) - 10:
            if data[i:i+3] == b'HP+':
                # 提取数字
                j = i + 3
                num_str = b""
                while j < len(data) and data[j:j+1].isdigit():
                    num_str += data[j:j+1]
                    j += 1
                    
                if num_str:
                    try:
                        value = int(num_str)
                        hp_data.append({'offset': i, 'value': value})
                    except:
                        pass
            i += 1
            
        return hp_data
        
    def find_time_restrictions_in_pak(self, data):
        """在PAK文件中查找时间限制"""
        restrictions = []
        
        # 查找24小时相关的模式
        patterns = [
            b'24\x00\x00\x00',  # 24的32位小端序
            b'\x18\x00\x00\x00',  # 24的十六进制
            b'24',  # 直接的24
        ]
        
        for pattern in patterns:
            i = 0
            while i < len(data) - len(pattern):
                if data[i:i+len(pattern)] == pattern:
                    restrictions.append({'offset': i, 'pattern': pattern.hex()})
                i += 1
                
        return restrictions
        
    def save_analysis_report(self):
        """保存分析报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = self.game_dir / f"COMPREHENSIVE_ANALYSIS_{timestamp}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.analysis_results, f, indent=2, ensure_ascii=False)
            
        print(f"\n分析报告已保存: {report_file}")
        
        # 创建简化的文本报告
        text_report = self.game_dir / f"ANALYSIS_SUMMARY_{timestamp}.txt"
        with open(text_report, 'w', encoding='utf-8') as f:
            f.write("天之游侠游戏架构分析报告\n")
            f.write("=" * 50 + "\n\n")
            
            # 文件结构摘要
            f.write("文件结构摘要:\n")
            for category, files in self.analysis_results['file_structure'].items():
                total_size = sum(size for _, size in files)
                f.write(f"  {category}: {len(files)} 文件, {total_size:,} 字节\n")
                
            # PAK文件摘要
            f.write("\nPAK文件分析摘要:\n")
            for pak_name, analysis in self.analysis_results.get('pak_files', {}).items():
                if 'error' not in analysis:
                    f.write(f"  {pak_name}:\n")
                    f.write(f"    大小: {analysis['size']:,} 字节\n")
                    f.write(f"    HP+数据: {len(analysis['hp_data'])} 条\n")
                    f.write(f"    时间限制: {len(analysis['time_restrictions'])} 条\n")
                    
        print(f"文本摘要已保存: {text_report}")

def main():
    """主函数"""
    print("天之游侠游戏架构全面分析器")
    print("=" * 50)
    
    analyzer = GameArchitectureAnalyzer(".")
    
    # 执行分析
    analyzer.analyze_file_structure()
    analyzer.analyze_pak_files()
    
    # 保存报告
    analyzer.save_analysis_report()
    
    print("\n分析完成！")

if __name__ == "__main__":
    main()
