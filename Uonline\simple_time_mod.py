#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单时间修改工具 - 使用Windows原生命令
"""

import subprocess
import os
from datetime import datetime, timedelta
import time

def run_cmd_as_admin(command):
    """以管理员身份运行cmd命令"""
    try:
        # 创建临时批处理文件
        batch_content = f"""@echo off
{command}
pause
"""
        
        with open("temp_admin.bat", "w", encoding="gbk") as f:
            f.write(batch_content)
        
        # 以管理员身份运行
        result = subprocess.run(
            f'powershell -Command "Start-Process temp_admin.bat -Verb RunAs"',
            shell=True,
            capture_output=True,
            text=True
        )
        
        # 等待用户操作
        input("请在弹出的窗口中完成操作后按回车...")
        
        # 清理临时文件
        try:
            os.remove("temp_admin.bat")
        except:
            pass
            
        return True
        
    except Exception as e:
        print(f"❌ 命令执行失败: {e}")
        return False

def set_time_manual(target_datetime):
    """手动设置时间"""
    try:
        # 格式化时间
        date_str = target_datetime.strftime("%m-%d-%Y")
        time_str = target_datetime.strftime("%H:%M:%S")
        
        print(f"🔧 准备设置时间为: {target_datetime.strftime('%Y-%m-%d %H:%M:%S')}")
        print("📋 将会弹出管理员窗口，请按照提示操作")
        print()
        
        # 创建设置时间的命令
        command = f"""echo 正在设置系统时间...
date {date_str}
time {time_str}
echo 时间设置完成!
echo 当前时间:
date /t
time /t"""
        
        return run_cmd_as_admin(command)
        
    except Exception as e:
        print(f"❌ 设置时间出错: {e}")
        return False

def restore_time():
    """恢复时间"""
    try:
        print("🔄 准备恢复网络时间...")
        print("📋 将会弹出管理员窗口进行时间同步")
        print()
        
        # 创建恢复时间的命令
        command = """echo 正在恢复网络时间...
w32tm /resync
echo 时间同步完成!
echo 当前时间:
date /t
time /t"""
        
        return run_cmd_as_admin(command)
        
    except Exception as e:
        print(f"❌ 恢复时间出错: {e}")
        return False

def check_game():
    """检查游戏状态"""
    try:
        result = subprocess.run(
            'tasklist /FI "IMAGENAME eq Prmain.exe"',
            shell=True, capture_output=True, text=True
        )
        return "Prmain.exe" in result.stdout
    except:
        return False

def kill_game():
    """关闭游戏"""
    try:
        if check_game():
            print("🎮 检测到游戏正在运行，正在关闭...")
            result = subprocess.run(
                'taskkill /F /IM Prmain.exe',
                shell=True, capture_output=True, text=True
            )
            if result.returncode == 0:
                print("✅ 游戏已关闭")
                time.sleep(2)
                return True
            else:
                print("❌ 无法关闭游戏")
                return False
        else:
            print("✅ 游戏未运行")
            return True
    except Exception as e:
        print(f"❌ 关闭游戏失败: {e}")
        return False

def main():
    print("=" * 60)
    print("🛠️ 简单时间修改工具 - Windows原生命令版")
    print("=" * 60)
    print()
    
    # 获取时间
    current_time = datetime.now()
    target_time = current_time + timedelta(hours=25)
    
    print(f"📅 当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 目标时间: {target_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    print("选择操作:")
    print("1. 🕐 修改时间到25小时后")
    print("2. 🔄 恢复网络同步时间")
    print("3. 🎯 完整测试流程")
    print("4. 📋 显示手动操作步骤")
    print("5. 🚪 退出")
    print()
    
    choice = input("请选择 (1-5): ").strip()
    
    if choice == "1":
        print("\n🕐 开始修改时间...")
        print()
        
        # 关闭游戏
        if not kill_game():
            print("⚠️ 请手动关闭游戏后重试")
            return
        
        # 修改时间
        if set_time_manual(target_time):
            print("\n🎉 时间修改操作完成!")
            print("📋 现在可以:")
            print("   1. 启动游戏")
            print("   2. 登录角色")
            print("   3. 测试24小时限制物品")
            print("   4. 记录测试结果")
            print()
            print("⚠️ 测试完成后请选择选项2恢复时间")
        else:
            print("❌ 时间修改失败")
    
    elif choice == "2":
        print("\n🔄 恢复网络时间...")
        print()
        
        if restore_time():
            print("\n✅ 时间恢复操作完成!")
            current = datetime.now()
            print(f"📅 当前时间: {current.strftime('%Y-%m-%d %H:%M:%S')}")
        else:
            print("❌ 时间恢复失败")
    
    elif choice == "3":
        print("\n🎯 完整测试流程...")
        print()
        
        # 步骤1
        print("📋 步骤1: 关闭游戏")
        if not kill_game():
            print("❌ 请手动关闭游戏")
            input("关闭游戏后按回车继续...")
        
        # 步骤2
        print("\n📋 步骤2: 修改时间")
        if set_time_manual(target_time):
            print("\n✅ 时间修改完成!")
            print()
            print("🎮 现在请测试:")
            print("   1. 启动游戏")
            print("   2. 登录角色")
            print("   3. 找到有'24小时限制'的物品")
            print("   4. 尝试使用该物品")
            print("   5. 记录结果:")
            print("      ✅ 可以使用 = 绕过成功 (客户端验证)")
            print("      ❌ 仍然限制 = 绕过失败 (服务器验证)")
            print()
            
            input("测试完成后按回车恢复时间...")
            
            # 步骤3
            print("\n📋 步骤3: 恢复时间")
            if restore_time():
                print("\n✅ 完整测试流程完成!")
                print()
                print("📊 根据测试结果判断:")
                print("🟢 绕过成功 = 我的逆向分析正确")
                print("   - timeGetTime API用于客户端验证")
                print("   - 时间修改方法有效")
                print()
                print("🔴 绕过失败 = 主要是服务器验证")
                print("   - 需要更复杂的绕过方法")
            else:
                print("❌ 时间恢复失败")
        else:
            print("❌ 时间修改失败，测试中止")
    
    elif choice == "4":
        print("\n📋 手动操作步骤:")
        print("=" * 40)
        print("🕐 修改时间:")
        print("1. 以管理员身份打开命令提示符")
        print("2. 输入命令:")
        print(f"   date {target_time.strftime('%m-%d-%Y')}")
        print(f"   time {target_time.strftime('%H:%M:%S')}")
        print()
        print("🔄 恢复时间:")
        print("1. 以管理员身份打开命令提示符")
        print("2. 输入命令:")
        print("   w32tm /resync")
        print()
        print("🎮 测试步骤:")
        print("1. 修改时间后启动游戏")
        print("2. 测试24小时限制物品")
        print("3. 记录结果并恢复时间")
    
    elif choice == "5":
        print("\n👋 程序退出")
        return
    
    else:
        print("❌ 无效选择")
    
    print()
    input("按回车键退出...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n🛑 用户中断")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
        input("按回车键退出...")
