#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动画文件分析器
专门分析游戏中的动作和动画数据
"""

import os
import struct
import re
from collections import defaultdict

class AnimationAnalyzer:
    def __init__(self):
        self.animation_files = []
        self.character_files = []
        self.effect_files = []
        self.findings = defaultdict(list)
        
    def scan_animation_files(self):
        """扫描所有可能包含动画的文件"""
        print("🎬 扫描动画相关文件...")
        
        # 角色相关PAK文件 (很可能包含动作)
        character_patterns = [
            'i_m_', 'i_w_', 't_m_', 't_w_',  # 角色文件
            'c_', 'I_', 'T_',                # 角色变体
            'angel', 'butcher', 'executor',   # 特定角色
            'zombie', 'fang', 'grizzly'      # 怪物角色
        ]
        
        # 效果相关文件
        effect_patterns = [
            'Effect.pak',     # 特效文件
            'Common.pak',     # 通用资源
            'floatsprite.pak' # 浮动精灵
        ]
        
        for filename in os.listdir('.'):
            if filename.endswith('.pak'):
                # 检查是否是角色文件
                for pattern in character_patterns:
                    if pattern in filename.lower():
                        self.character_files.append(filename)
                        break
                
                # 检查是否是效果文件
                if filename in effect_patterns:
                    self.effect_files.append(filename)
                    
                # 所有PAK都可能包含动画
                self.animation_files.append(filename)
        
        print(f"找到 {len(self.character_files)} 个角色文件")
        print(f"找到 {len(self.effect_files)} 个效果文件")
        print(f"总共 {len(self.animation_files)} 个PAK文件")
        
    def analyze_pak_for_animations(self, pak_file):
        """分析PAK文件中的动画数据"""
        if not os.path.exists(pak_file):
            return
            
        try:
            with open(pak_file, 'rb') as f:
                data = f.read()
                
            print(f"\n=== 分析 {pak_file} ===")
            print(f"文件大小: {len(data):,} 字节")
            
            # 检查文件头
            if len(data) >= 4:
                header = data[:4]
                if header == b'PGFN':
                    print("✅ 确认PGFN格式")
                    self.analyze_pgfn_animations(pak_file, data)
                else:
                    print(f"❓ 未知格式: {header}")
                    
        except Exception as e:
            print(f"❌ 分析失败: {e}")
            
    def analyze_pgfn_animations(self, filename, data):
        """分析PGFN格式中的动画数据"""
        try:
            # 解析PGFN头部
            if len(data) < 32:
                return
                
            version = struct.unpack('<I', data[4:8])[0]
            file_table_size = struct.unpack('<I', data[24:28])[0] if len(data) >= 28 else 0
            file_count = struct.unpack('<I', data[28:32])[0] if len(data) >= 32 else 0
            
            print(f"  版本: {version}")
            print(f"  文件表大小: {file_table_size}")
            print(f"  文件数量: {file_count}")
            
            # 查找动画相关的数据模式
            self.find_animation_patterns(filename, data)
            
        except Exception as e:
            print(f"  ❌ PGFN分析失败: {e}")
            
    def find_animation_patterns(self, filename, data):
        """查找动画相关的数据模式"""
        patterns_found = 0
        
        # 1. 查找动画帧数据 (常见的动画帧率: 30, 60, 24, 15)
        frame_rates = [15, 24, 30, 60, 120]
        for rate in frame_rates:
            rate_bytes = struct.pack('<I', rate)
            count = data.count(rate_bytes)
            if count > 5:  # 如果出现多次，可能是帧率设置
                print(f"  🎬 可能的帧率 {rate}: 出现 {count} 次")
                self.findings[filename].append(f"帧率{rate}: {count}次")
                patterns_found += 1
        
        # 2. 查找动画时长 (毫秒为单位: 100-5000ms)
        for duration_ms in range(100, 5001, 100):
            duration_bytes = struct.pack('<I', duration_ms)
            count = data.count(duration_bytes)
            if count > 3:
                print(f"  ⏱️ 可能的动画时长 {duration_ms}ms: 出现 {count} 次")
                self.findings[filename].append(f"时长{duration_ms}ms: {count}次")
                patterns_found += 1
        
        # 3. 查找动作ID模式 (0-255的小数值)
        action_ids = {}
        for i in range(0, 256):
            if i < 50:  # 前50个ID更可能是动作
                id_bytes = struct.pack('<I', i)
                count = data.count(id_bytes)
                if count > 10:
                    action_ids[i] = count
        
        if action_ids:
            print(f"  🎭 可能的动作ID:")
            for action_id, count in sorted(action_ids.items(), key=lambda x: x[1], reverse=True)[:10]:
                print(f"    动作ID {action_id}: 出现 {count} 次")
                self.findings[filename].append(f"动作ID{action_id}: {count}次")
                patterns_found += 1
        
        # 4. 查找动画文件名模式
        animation_keywords = [
            b'walk', b'run', b'attack', b'skill', b'cast', b'die', b'idle',
            b'stand', b'move', b'fight', b'magic', b'bow', b'sword'
        ]
        
        for keyword in animation_keywords:
            if keyword in data:
                positions = []
                start = 0
                while True:
                    pos = data.find(keyword, start)
                    if pos == -1:
                        break
                    positions.append(pos)
                    start = pos + 1
                    if len(positions) >= 10:  # 限制搜索结果
                        break
                
                if positions:
                    print(f"  📝 动画关键词 '{keyword.decode()}': 找到 {len(positions)} 处")
                    self.findings[filename].append(f"关键词{keyword.decode()}: {len(positions)}处")
                    patterns_found += 1
        
        # 5. 查找可能的动画序列数据
        self.find_animation_sequences(filename, data)
        
        if patterns_found == 0:
            print(f"  ❌ 未找到明显的动画模式")
        else:
            print(f"  ✅ 找到 {patterns_found} 种动画模式")
            
    def find_animation_sequences(self, filename, data):
        """查找动画序列数据"""
        # 查找重复的数值序列，可能是动画帧序列
        sequence_length = 4  # 查找4字节序列
        sequences = defaultdict(int)
        
        for i in range(0, len(data) - sequence_length * 4, 4):
            try:
                seq = struct.unpack('<IIII', data[i:i+16])
                # 检查是否是有意义的序列 (递增、有规律等)
                if self.is_meaningful_sequence(seq):
                    sequences[seq] += 1
            except:
                continue
        
        # 报告找到的序列
        meaningful_sequences = [(seq, count) for seq, count in sequences.items() if count >= 3]
        if meaningful_sequences:
            print(f"  🔢 可能的动画序列:")
            for seq, count in sorted(meaningful_sequences, key=lambda x: x[1], reverse=True)[:5]:
                print(f"    序列 {seq}: 出现 {count} 次")
                self.findings[filename].append(f"序列{seq}: {count}次")
    
    def is_meaningful_sequence(self, seq):
        """判断序列是否有意义"""
        # 检查是否是递增序列
        if all(seq[i] < seq[i+1] for i in range(len(seq)-1)):
            return True
        
        # 检查是否有规律的间隔
        if len(set(seq[i+1] - seq[i] for i in range(len(seq)-1))) == 1:
            return True
            
        # 检查是否在合理的数值范围内 (动画相关)
        if all(0 <= x <= 10000 for x in seq):
            return True
            
        return False
    
    def analyze_character_animations(self):
        """重点分析角色动画文件"""
        print(f"\n🎭 重点分析角色动画文件")
        print("=" * 60)
        
        priority_files = [
            'i_m_d.pak',    # 男性角色-战士
            'i_w_d.pak',    # 女性角色-战士  
            'c_pc.pak',     # 玩家角色
            'Common.pak',   # 通用资源
            'Effect.pak'    # 特效
        ]
        
        for pak_file in priority_files:
            if os.path.exists(pak_file):
                self.analyze_pak_for_animations(pak_file)
            else:
                print(f"❌ 文件不存在: {pak_file}")
    
    def generate_animation_report(self):
        """生成动画分析报告"""
        print(f"\n📊 动画分析报告")
        print("=" * 60)
        
        if not self.findings:
            print("❌ 未找到任何动画相关数据")
            return
        
        # 按发现数量排序
        sorted_files = sorted(self.findings.items(), key=lambda x: len(x[1]), reverse=True)
        
        print(f"发现动画数据的文件: {len(sorted_files)} 个")
        
        for filename, discoveries in sorted_files[:10]:  # 显示前10个
            print(f"\n📁 {filename}:")
            for discovery in discoveries[:5]:  # 每个文件显示前5个发现
                print(f"  • {discovery}")
        
        # 生成修改建议
        self.generate_modification_suggestions()
    
    def generate_modification_suggestions(self):
        """生成修改建议"""
        print(f"\n💡 动画修改建议")
        print("=" * 60)
        
        suggestions = []
        
        # 基于发现的数据给出建议
        for filename, discoveries in self.findings.items():
            for discovery in discoveries:
                if '时长' in discovery and 'ms' in discovery:
                    suggestions.append(f"修改 {filename} 中的动画时长数据")
                elif '动作ID' in discovery:
                    suggestions.append(f"修改 {filename} 中的动作ID映射")
                elif '帧率' in discovery:
                    suggestions.append(f"提高 {filename} 中的动画帧率")
        
        if suggestions:
            print("基于分析结果的修改建议:")
            unique_suggestions = list(set(suggestions))[:10]
            for i, suggestion in enumerate(unique_suggestions, 1):
                print(f"{i}. {suggestion}")
        else:
            print("需要更深入的分析才能给出具体建议")
        
        print(f"\n🎯 推荐的修改策略:")
        print("1. 将动画时长改为1ms (几乎瞬间完成)")
        print("2. 将动作ID全部映射到'待机'动作")
        print("3. 提高动画播放速度到1000倍")
        print("4. 替换动画文件为静态帧")

def main():
    """主函数"""
    print("🎬 天之游侠动画分析器")
    print("=" * 60)
    
    analyzer = AnimationAnalyzer()
    
    # 扫描文件
    analyzer.scan_animation_files()
    
    # 重点分析角色动画
    analyzer.analyze_character_animations()
    
    # 生成报告
    analyzer.generate_animation_report()
    
    print(f"\n✅ 动画分析完成!")

if __name__ == "__main__":
    main()
