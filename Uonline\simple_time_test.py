#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的时间修改测试工具
手动指导版本
"""

import os
import subprocess
from datetime import datetime, timedelta

def check_game_running():
    """检查游戏是否在运行"""
    try:
        result = subprocess.run('tasklist /FI "IMAGENAME eq Prmain.exe"', 
                              shell=True, capture_output=True, text=True)
        return "Prmain.exe" in result.stdout
    except:
        return False

def get_target_time():
    """计算目标时间"""
    current = datetime.now()
    target = current + timedelta(hours=25)
    return current, target

def main():
    print("=" * 60)
    print("🎮 天之游侠 24小时限制绕过测试")
    print("=" * 60)
    print()
    
    # 检查游戏状态
    if check_game_running():
        print("⚠️  检测到游戏正在运行!")
        print("📋 请先关闭游戏再进行测试")
        print()
    
    # 计算时间
    current, target = get_target_time()
    
    print(f"📅 当前时间: {current.strftime('%Y年%m月%d日 %H:%M:%S')}")
    print(f"🎯 目标时间: {target.strftime('%Y年%m月%d日 %H:%M:%S')}")
    print(f"⏰ 需要前进: 25小时")
    print()
    
    print("🔧 手动修改系统时间步骤:")
    print("=" * 40)
    print("1. 右键点击任务栏右下角的时间")
    print("2. 选择 '调整日期/时间'")
    print("3. 关闭 '自动设置时间'")
    print("4. 点击 '更改' 按钮")
    print("5. 设置新的日期和时间:")
    print(f"   📅 日期: {target.strftime('%Y年%m月%d日')}")
    print(f"   🕐 时间: {target.strftime('%H:%M:%S')}")
    print("6. 点击 '确定' 保存")
    print()
    
    print("🎮 测试步骤:")
    print("=" * 40)
    print("1. 修改系统时间 (按上面步骤)")
    print("2. 启动游戏")
    print("3. 登录角色")
    print("4. 找到有 '24小时限制' 的物品")
    print("5. 尝试使用该物品")
    print("6. 记录结果:")
    print("   ✅ 如果可以使用 = 绕过成功 (客户端验证)")
    print("   ❌ 如果仍然限制 = 绕过失败 (服务器验证)")
    print()
    
    print("🔄 测试完成后恢复时间:")
    print("=" * 40)
    print("1. 右键点击任务栏时间")
    print("2. 选择 '调整日期/时间'")
    print("3. 开启 '自动设置时间'")
    print("4. 系统会自动同步正确时间")
    print()
    
    print("📊 结果分析:")
    print("=" * 40)
    print("🟢 如果绕过成功:")
    print("   - 说明主要是客户端时间验证")
    print("   - timeGetTime API检查本地时间")
    print("   - 可以用时间修改方法绕过")
    print()
    print("🔴 如果绕过失败:")
    print("   - 说明有服务器端验证")
    print("   - 服务器记录物品使用时间戳")
    print("   - 需要更复杂的绕过方法")
    print()
    
    print("⚠️  重要提醒:")
    print("- 测试前确保游戏完全关闭")
    print("- 修改时间可能影响其他程序")
    print("- 测试完成后及时恢复时间")
    print("- 如果出现问题，重启电脑可恢复")
    print()
    
    input("按回车键退出...")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ 程序出错: {e}")
        input("按回车键退出...")
