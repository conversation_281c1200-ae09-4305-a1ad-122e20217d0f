#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查MCP环境和安装指南
"""

import subprocess
import sys
import os
import json
from pathlib import Path

def check_command(command):
    """检查命令是否存在"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout.strip()
    except:
        return False, ""

def check_python_packages():
    """检查Python包"""
    packages = ['mcp', 'anthropic-mcp', 'mcp-server']
    installed = []
    
    for package in packages:
        try:
            result = subprocess.run([sys.executable, '-m', 'pip', 'show', package], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                installed.append(package)
        except:
            pass
    
    return installed

def check_node_packages():
    """检查Node.js包"""
    try:
        result = subprocess.run(['npm', 'list', '-g', '--depth=0'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            return 'mcp' in result.stdout
    except:
        pass
    return False

def check_environment_variables():
    """检查环境变量"""
    mcp_vars = {}
    for key in os.environ:
        if 'MCP' in key.upper():
            mcp_vars[key] = os.environ[key]
    return mcp_vars

def check_config_files():
    """检查配置文件"""
    config_locations = [
        Path.home() / '.mcp',
        Path.home() / '.config' / 'mcp',
        Path('mcp.json'),
        Path('mcp-config.json'),
        Path('.mcp.json')
    ]
    
    found_configs = []
    for config_path in config_locations:
        if config_path.exists():
            found_configs.append(str(config_path))
    
    return found_configs

def main():
    print("=" * 60)
    print("🔍 MCP环境检查工具")
    print("=" * 60)
    print()
    
    # 检查基础环境
    print("📋 基础环境检查:")
    print("-" * 30)
    
    # Python
    python_ok, python_version = check_command('python --version')
    py_ok, py_version = check_command('py --version')
    print(f"Python: {'✅' if python_ok else '❌'} {python_version}")
    if py_ok and not python_ok:
        print(f"Python (py): ✅ {py_version}")
    
    # Node.js
    node_ok, node_version = check_command('node --version')
    print(f"Node.js: {'✅' if node_ok else '❌'} {node_version}")
    
    # npm
    npm_ok, npm_version = check_command('npm --version')
    print(f"npm: {'✅' if npm_ok else '❌'} {npm_version}")
    
    # Git
    git_ok, git_version = check_command('git --version')
    print(f"Git: {'✅' if git_ok else '❌'} {git_version}")
    
    print()
    
    # 检查MCP相关包
    print("📦 MCP相关包检查:")
    print("-" * 30)
    
    # Python包
    python_packages = check_python_packages()
    if python_packages:
        print("✅ 已安装的Python MCP包:")
        for pkg in python_packages:
            print(f"  - {pkg}")
    else:
        print("❌ 未找到Python MCP包")
    
    # Node.js包
    node_mcp = check_node_packages()
    print(f"Node.js MCP包: {'✅' if node_mcp else '❌'}")
    
    print()
    
    # 检查环境变量
    print("🌍 环境变量检查:")
    print("-" * 30)
    
    mcp_vars = check_environment_variables()
    if mcp_vars:
        print("✅ 找到MCP相关环境变量:")
        for key, value in mcp_vars.items():
            print(f"  {key} = {value}")
    else:
        print("❌ 未找到MCP环境变量")
    
    print()
    
    # 检查配置文件
    print("📁 配置文件检查:")
    print("-" * 30)
    
    config_files = check_config_files()
    if config_files:
        print("✅ 找到MCP配置文件:")
        for config in config_files:
            print(f"  - {config}")
    else:
        print("❌ 未找到MCP配置文件")
    
    print()
    
    # 总结和建议
    print("📊 检查结果总结:")
    print("=" * 30)
    
    has_python = python_ok or py_ok
    has_node = node_ok
    has_mcp_packages = len(python_packages) > 0 or node_mcp
    has_mcp_config = len(mcp_vars) > 0 or len(config_files) > 0
    
    if has_mcp_packages and has_mcp_config:
        print("🎉 MCP环境已配置!")
        print("✅ 可以使用MCP增强功能")
    elif has_python and has_node:
        print("⚠️ 基础环境就绪，但MCP未配置")
        print("📋 需要安装和配置MCP")
        print()
        print("🚀 安装步骤:")
        print("1. 安装MCP Python包:")
        print("   pip install mcp anthropic-mcp")
        print()
        print("2. 安装MCP Node.js包:")
        print("   npm install -g @anthropic-ai/mcp")
        print()
        print("3. 配置MCP服务器")
        print("4. 设置环境变量")
    else:
        print("❌ 基础环境不完整")
        print("📋 需要先安装:")
        if not has_python:
            print("  - Python 3.8+")
        if not has_node:
            print("  - Node.js 16+")
        print("  - 然后安装MCP")
    
    print()
    
    # 提供安装指南
    print("📖 详细安装指南:")
    print("=" * 30)
    print("1. 访问: https://github.com/anthropics/mcp")
    print("2. 按照官方文档安装MCP")
    print("3. 配置逆向工程工具连接")
    print("4. 重新运行此检查脚本")
    
    print()
    input("按回车键退出...")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ 检查过程出错: {e}")
        input("按回车键退出...")
