#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实际PAK文件解包器 - 真正提取游戏资源
"""

import os
import struct
from pathlib import Path

def extract_real_resources():
    """实际提取PAK文件中的资源"""
    print("🎯 开始实际提取PAK资源...")
    
    # 查找PAK文件
    pak_files = [f for f in os.listdir('.') if f.endswith('.pak')]
    print(f"找到 {len(pak_files)} 个PAK文件")
    
    if not pak_files:
        print("❌ 未找到PAK文件")
        return
    
    # 创建输出目录
    output_dir = Path('extracted_resources')
    output_dir.mkdir(exist_ok=True)
    
    extracted_count = 0
    
    # 分析前10个PAK文件
    for pak_file in pak_files[:10]:
        print(f"\n📦 分析 {pak_file}...")
        
        try:
            with open(pak_file, 'rb') as f:
                data = f.read()
                file_size = len(data)
                
                print(f"  文件大小: {file_size} 字节")
                
                # 检查文件类型
                if data.startswith(b'\x89PNG'):
                    # 这是PNG文件
                    output_file = output_dir / f"{Path(pak_file).stem}.png"
                    with open(output_file, 'wb') as out:
                        out.write(data)
                    print(f"  ✅ 提取PNG: {output_file}")
                    extracted_count += 1
                    
                elif b'PGFN' in data[:100]:
                    # 包含PGFN标识的PAK容器
                    print(f"  📁 这是PAK容器文件")
                    
                    # 查找内嵌的PNG文件
                    offset = 0
                    png_count = 0
                    while True:
                        png_start = data.find(b'\x89PNG\r\n\x1a\n', offset)
                        if png_start == -1:
                            break
                        
                        # 查找PNG结束
                        png_end = data.find(b'IEND\xaeB`\x82', png_start)
                        if png_end != -1:
                            png_end += 8
                            png_data = data[png_start:png_end]
                            
                            if len(png_data) > 100:  # 过滤太小的文件
                                png_file = output_dir / f"{Path(pak_file).stem}_png_{png_count:03d}.png"
                                with open(png_file, 'wb') as out:
                                    out.write(png_data)
                                print(f"    ✅ 提取PNG: {png_file} ({len(png_data)} 字节)")
                                png_count += 1
                                extracted_count += 1
                        
                        offset = png_start + 1
                    
                    # 查找其他资源
                    # BMP文件
                    bmp_start = data.find(b'BM')
                    if bmp_start != -1 and bmp_start < 1000:  # 在文件开头附近
                        bmp_size = struct.unpack('<I', data[bmp_start+2:bmp_start+6])[0]
                        if bmp_size < file_size:
                            bmp_data = data[bmp_start:bmp_start+bmp_size]
                            bmp_file = output_dir / f"{Path(pak_file).stem}.bmp"
                            with open(bmp_file, 'wb') as out:
                                out.write(bmp_data)
                            print(f"    ✅ 提取BMP: {bmp_file} ({len(bmp_data)} 字节)")
                            extracted_count += 1
                    
                    # WAV文件
                    wav_start = data.find(b'RIFF')
                    if wav_start != -1:
                        try:
                            wav_size = struct.unpack('<I', data[wav_start+4:wav_start+8])[0] + 8
                            if wav_size < file_size:
                                wav_data = data[wav_start:wav_start+wav_size]
                                wav_file = output_dir / f"{Path(pak_file).stem}.wav"
                                with open(wav_file, 'wb') as out:
                                    out.write(wav_data)
                                print(f"    ✅ 提取WAV: {wav_file} ({len(wav_data)} 字节)")
                                extracted_count += 1
                        except:
                            pass
                
                else:
                    # 未知格式，尝试查找文本
                    text_strings = []
                    current_string = ""
                    
                    for byte in data[:10000]:  # 只检查前10KB
                        if 32 <= byte <= 126:  # 可打印ASCII
                            current_string += chr(byte)
                        else:
                            if len(current_string) > 10:
                                text_strings.append(current_string)
                            current_string = ""
                    
                    if text_strings:
                        text_file = output_dir / f"{Path(pak_file).stem}_strings.txt"
                        with open(text_file, 'w', encoding='utf-8') as out:
                            for string in text_strings[:50]:  # 只保存前50个
                                out.write(string + '\n')
                        print(f"    ✅ 提取文本: {text_file} ({len(text_strings)} 个字符串)")
                        extracted_count += 1
                
        except Exception as e:
            print(f"  ❌ 处理失败: {e}")
    
    print(f"\n🎉 提取完成！总共提取了 {extracted_count} 个文件到 {output_dir}")
    
    # 显示提取的文件列表
    if extracted_count > 0:
        print(f"\n📋 提取的文件:")
        for file in sorted(output_dir.iterdir()):
            print(f"  {file.name} ({file.stat().st_size} 字节)")

def analyze_message_ctf():
    """分析Message.ctf文件，提取游戏数据"""
    print(f"\n🔍 分析Message.ctf文件...")
    
    if not os.path.exists('Message.ctf'):
        print("❌ 未找到Message.ctf文件")
        return
    
    try:
        with open('Message.ctf', 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
        
        print(f"  文件包含 {len(lines)} 行")
        
        # 提取技能信息
        skills = []
        items = []
        npcs = []
        
        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
            
            # 查找技能相关
            if '技能' in line or '攻击' in line or '魔法' in line:
                skills.append((i+1, line))
            
            # 查找物品相关
            elif '装备' in line or '武器' in line or '防具' in line:
                items.append((i+1, line))
            
            # 查找NPC相关
            elif 'NPC' in line or '商人' in line or '村民' in line:
                npcs.append((i+1, line))
        
        # 保存分析结果
        output_dir = Path('extracted_resources')
        output_dir.mkdir(exist_ok=True)
        
        if skills:
            with open(output_dir / 'skills.txt', 'w', encoding='utf-8') as f:
                f.write(f"技能信息 ({len(skills)} 条):\n\n")
                for line_num, content in skills[:20]:  # 只保存前20条
                    f.write(f"行{line_num}: {content}\n")
            print(f"  ✅ 提取技能信息: {len(skills)} 条")
        
        if items:
            with open(output_dir / 'items.txt', 'w', encoding='utf-8') as f:
                f.write(f"物品信息 ({len(items)} 条):\n\n")
                for line_num, content in items[:20]:
                    f.write(f"行{line_num}: {content}\n")
            print(f"  ✅ 提取物品信息: {len(items)} 条")
        
        if npcs:
            with open(output_dir / 'npcs.txt', 'w', encoding='utf-8') as f:
                f.write(f"NPC信息 ({len(npcs)} 条):\n\n")
                for line_num, content in npcs[:20]:
                    f.write(f"行{line_num}: {content}\n")
            print(f"  ✅ 提取NPC信息: {len(npcs)} 条")
        
    except Exception as e:
        print(f"  ❌ 分析失败: {e}")

def create_server_config():
    """创建私服配置文件"""
    print(f"\n🌐 创建私服配置...")
    
    config_content = """# Uonline 私服配置
# 修改connection.ini使用此配置

[SERVER]
# 原服务器
ORIGINAL_IP=**************
ORIGINAL_PORT=4001

# 私服设置 (修改为你的服务器)
PRIVATE_IP=127.0.0.1
PRIVATE_PORT=4001

[URLS]
# 原网站
ORIGINAL_SITE=http://www.t2uu.com
ORIGINAL_REG=http://wud88member.t2uu.com/reg.aspx

# 私服网站 (修改为你的网站)
PRIVATE_SITE=http://localhost
PRIVATE_REG=http://localhost/register

[MODIFICATION]
# 要修改的文件
CONFIG_FILE=connection.ini
HOSTS_FILE=C:\\Windows\\System32\\drivers\\etc\\hosts

# 修改命令
# 1. 备份原文件: copy connection.ini connection.ini.bak
# 2. 修改IP: 将**************改为127.0.0.1
# 3. 修改hosts: echo "127.0.0.1 wud88member.t2uu.com" >> hosts
"""
    
    output_dir = Path('extracted_resources')
    output_dir.mkdir(exist_ok=True)
    
    with open(output_dir / 'private_server_config.txt', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print(f"  ✅ 私服配置已保存到 extracted_resources/private_server_config.txt")

def main():
    print("=" * 60)
    print("🎯 Uonline 实际资源提取器")
    print("=" * 60)
    
    # 提取PAK资源
    extract_real_resources()
    
    # 分析Message.ctf
    analyze_message_ctf()
    
    # 创建私服配置
    create_server_config()
    
    print(f"\n" + "=" * 60)
    print("✅ 所有提取工作完成！")
    print("📁 检查 'extracted_resources' 目录查看结果")
    print("=" * 60)

if __name__ == "__main__":
    main()
