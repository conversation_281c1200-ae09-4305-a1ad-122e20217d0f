#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PAK文件格式逆向工程器
专门分析PAK文件的内部结构和数据格式
"""

import os
import struct
import zlib
from pathlib import Path

class PAKFormatReverser:
    def __init__(self, pak_file):
        self.pak_file = pak_file
        self.data = None
        self.file_entries = []
        
    def load_pak(self):
        """加载PAK文件"""
        try:
            with open(self.pak_file, 'rb') as f:
                self.data = f.read()
            print(f"加载PAK文件: {self.pak_file} ({len(self.data):,} 字节)")
            return True
        except Exception as e:
            print(f"加载失败: {e}")
            return False
            
    def analyze_header_structure(self):
        """分析头部结构"""
        print(f"\n=== {self.pak_file} 头部结构分析 ===")
        
        if len(self.data) < 64:
            print("文件太小，无法分析")
            return
            
        # 显示前64字节的十六进制和ASCII
        print("前64字节十六进制:")
        for i in range(0, min(64, len(self.data)), 16):
            hex_part = ' '.join(f'{b:02x}' for b in self.data[i:i+16])
            ascii_part = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in self.data[i:i+16])
            print(f"{i:04x}: {hex_part:<48} {ascii_part}")
            
        # 尝试解析可能的头部字段
        print("\n可能的头部字段:")
        for offset in range(0, min(32, len(self.data) - 4), 4):
            value_le = struct.unpack('<I', self.data[offset:offset+4])[0]
            value_be = struct.unpack('>I', self.data[offset:offset+4])[0]
            print(f"偏移 {offset:02x}: {value_le:10} (LE) / {value_be:10} (BE)")
            
    def find_file_table(self):
        """查找文件表"""
        print(f"\n=== 查找文件表 ===")
        
        # 查找可能的文件名模式
        potential_files = []
        
        # 方法1: 查找连续的可打印字符串
        current_string = b""
        string_start = 0
        
        for i, byte in enumerate(self.data):
            if 32 <= byte <= 126:  # 可打印ASCII
                if not current_string:
                    string_start = i
                current_string += bytes([byte])
            else:
                if len(current_string) >= 5:  # 至少5个字符
                    try:
                        text = current_string.decode('ascii')
                        if '.' in text and any(ext in text.lower() for ext in ['.bmp', '.txt', '.dat', '.cfg', '.tga']):
                            potential_files.append((string_start, text))
                    except:
                        pass
                current_string = b""
                
        print(f"找到 {len(potential_files)} 个可能的文件名:")
        for offset, filename in potential_files[:20]:  # 只显示前20个
            print(f"  {offset:08x}: {filename}")
            
        return potential_files
        
    def analyze_data_patterns(self):
        """分析数据模式"""
        print(f"\n=== 数据模式分析 ===")
        
        # 查找重复的4字节模式（可能是数值）
        value_counts = {}
        for i in range(0, len(self.data) - 4, 4):
            value = struct.unpack('<I', self.data[i:i+4])[0]
            if 1 <= value <= 1000000:  # 合理范围的数值
                value_counts[value] = value_counts.get(value, 0) + 1
                
        # 显示最常见的数值
        common_values = sorted(value_counts.items(), key=lambda x: x[1], reverse=True)[:20]
        print("最常见的数值:")
        for value, count in common_values:
            print(f"  {value:8}: 出现 {count:4} 次")
            
        # 查找可能的HP值范围
        hp_candidates = []
        for value, count in value_counts.items():
            if 50 <= value <= 50000 and count >= 2:  # HP值的合理范围
                hp_candidates.append((value, count))
                
        hp_candidates.sort(key=lambda x: x[1], reverse=True)
        print(f"\n可能的HP值 (50-50000范围):")
        for value, count in hp_candidates[:15]:
            print(f"  HP {value:5}: 出现 {count:3} 次")
            
    def search_binary_hp_data(self):
        """搜索二进制HP数据"""
        print(f"\n=== 搜索二进制HP数据 ===")
        
        # 查找可能的属性结构
        # 假设HP数据可能以特定模式存储
        hp_patterns = []
        
        # 查找连续的数值序列（可能是属性数据）
        for i in range(0, len(self.data) - 20, 4):
            try:
                # 读取5个连续的32位值
                values = []
                for j in range(5):
                    value = struct.unpack('<I', self.data[i+j*4:i+j*4+4])[0]
                    values.append(value)
                    
                # 检查是否像属性数据
                if all(0 <= v <= 100000 for v in values):  # 合理的属性范围
                    # 检查是否有递增或特定模式
                    if any(v > 100 for v in values):  # 至少有一个较大的值
                        hp_patterns.append((i, values))
                        
            except:
                continue
                
        print(f"找到 {len(hp_patterns)} 个可能的属性数据模式:")
        for offset, values in hp_patterns[:10]:  # 只显示前10个
            print(f"  {offset:08x}: {values}")
            
    def extract_time_restriction_data(self):
        """提取时间限制数据"""
        print(f"\n=== 时间限制数据分析 ===")
        
        # 查找24小时相关的二进制模式
        time_patterns = [
            struct.pack('<I', 24),      # 24的32位小端序
            struct.pack('<H', 24),      # 24的16位小端序
            struct.pack('B', 24),       # 24的8位
            struct.pack('<I', 86400),   # 24小时的秒数
            struct.pack('<I', 1440),    # 24小时的分钟数
        ]
        
        restriction_data = []
        
        for pattern in time_patterns:
            count = 0
            i = 0
            while i < len(self.data) - len(pattern):
                if self.data[i:i+len(pattern)] == pattern:
                    # 获取周围的上下文数据
                    context_start = max(0, i - 16)
                    context_end = min(len(self.data), i + len(pattern) + 16)
                    context = self.data[context_start:context_end]
                    
                    restriction_data.append({
                        'offset': i,
                        'pattern': pattern.hex(),
                        'context': context.hex()
                    })
                    count += 1
                i += 1
                
            print(f"模式 {pattern.hex()}: 找到 {count} 处")
            
        return restriction_data[:50]  # 只返回前50个
        
    def attempt_decompression(self):
        """尝试解压缩"""
        print(f"\n=== 尝试解压缩 ===")
        
        # 尝试不同的解压缩方法
        compression_methods = [
            ('zlib', lambda data: zlib.decompress(data)),
            ('zlib_skip_header', lambda data: zlib.decompress(data[2:])),
            ('gzip_like', lambda data: zlib.decompress(data, -zlib.MAX_WBITS)),
        ]
        
        # 尝试从不同偏移开始解压
        test_offsets = [0, 4, 8, 16, 32, 64, 128]
        
        for method_name, decompress_func in compression_methods:
            for offset in test_offsets:
                try:
                    if offset >= len(self.data):
                        continue
                        
                    decompressed = decompress_func(self.data[offset:offset+1024])
                    print(f"{method_name} (偏移 {offset}): 成功解压 {len(decompressed)} 字节")
                    
                    # 显示解压后的前64字节
                    preview = decompressed[:64]
                    hex_preview = ' '.join(f'{b:02x}' for b in preview)
                    print(f"  预览: {hex_preview}")
                    
                    return True
                    
                except Exception as e:
                    continue
                    
        print("未找到有效的压缩格式")
        return False

def main():
    """主函数"""
    print("PAK文件格式逆向工程器")
    print("=" * 50)
    
    # 分析关键的PAK文件
    key_paks = ['item0001.pak', 'eqp0001.pak', 'Common.pak']
    
    for pak_name in key_paks:
        if not os.path.exists(pak_name):
            print(f"跳过不存在的文件: {pak_name}")
            continue
            
        print(f"\n{'='*60}")
        print(f"分析文件: {pak_name}")
        print(f"{'='*60}")
        
        reverser = PAKFormatReverser(pak_name)
        if not reverser.load_pak():
            continue
            
        # 执行各种分析
        reverser.analyze_header_structure()
        reverser.find_file_table()
        reverser.analyze_data_patterns()
        reverser.search_binary_hp_data()
        reverser.extract_time_restriction_data()
        reverser.attempt_decompression()

if __name__ == "__main__":
    main()
