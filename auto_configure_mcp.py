#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动配置MCP逆向工具
为Augment配置专业逆向分析工具
"""

import json
import os
import subprocess
import sys
from pathlib import Path
import requests
import zipfile
import shutil

def check_existing_tools():
    """检查现有的逆向工具"""
    tools = {}
    
    # 检查IDA Pro
    ida_paths = [
        r"C:\Program Files\IDA Pro 8.4\ida64.exe",
        r"C:\Program Files\IDA Pro 8.3\ida64.exe", 
        r"C:\Program Files\IDA Pro 8.2\ida64.exe",
        r"C:\Program Files (x86)\IDA Pro 8.4\ida64.exe",
        r"C:\Program Files (x86)\IDA Pro 8.3\ida64.exe"
    ]
    
    for path in ida_paths:
        if os.path.exists(path):
            tools['ida_pro'] = path
            break
    
    # 检查Ghidra
    ghidra_paths = [
        r"C:\ghidra\ghidraRun.bat",
        r"C:\Program Files\ghidra\ghidraRun.bat",
        r"C:\tools\ghidra\ghidraRun.bat"
    ]
    
    for path in ghidra_paths:
        if os.path.exists(path):
            tools['ghidra'] = path
            break
    
    # 检查x64dbg
    x64dbg_paths = [
        r"C:\x64dbg\x64dbg.exe",
        r"C:\Program Files\x64dbg\x64dbg.exe",
        r"C:\tools\x64dbg\x64dbg.exe"
    ]
    
    for path in x64dbg_paths:
        if os.path.exists(path):
            tools['x64dbg'] = path
            break
    
    return tools

def download_ghidra():
    """下载并安装Ghidra（免费工具）"""
    print("📥 下载Ghidra（NSA免费逆向工具）...")
    
    try:
        # Ghidra下载URL
        ghidra_url = "https://github.com/NationalSecurityAgency/ghidra/releases/download/Ghidra_11.0.1_build/ghidra_11.0.1_PUBLIC_20240130.zip"
        
        # 下载目录
        download_dir = Path("C:/tools")
        download_dir.mkdir(exist_ok=True)
        
        zip_path = download_dir / "ghidra.zip"
        
        print("⏬ 正在下载Ghidra...")
        response = requests.get(ghidra_url, stream=True)
        response.raise_for_status()
        
        with open(zip_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        print("📦 正在解压Ghidra...")
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(download_dir)
        
        # 找到解压后的目录
        ghidra_dir = None
        for item in download_dir.iterdir():
            if item.is_dir() and "ghidra" in item.name.lower():
                ghidra_dir = item
                break
        
        if ghidra_dir:
            ghidra_exe = ghidra_dir / "ghidraRun.bat"
            if ghidra_exe.exists():
                print(f"✅ Ghidra安装成功: {ghidra_exe}")
                return str(ghidra_exe)
        
        print("❌ Ghidra安装失败")
        return None
        
    except Exception as e:
        print(f"❌ 下载Ghidra失败: {e}")
        return None

def create_binary_analyzer_mcp():
    """创建二进制分析MCP服务器"""
    mcp_server_code = '''#!/usr/bin/env python3
"""
二进制分析MCP服务器
为Augment提供PE文件分析功能
"""

import asyncio
import json
import struct
import re
from pathlib import Path
from mcp.server import Server
from mcp.server.stdio import stdio_server
from mcp.types import Tool, TextContent

app = Server("binary-analyzer")

@app.list_tools()
async def list_tools():
    return [
        Tool(
            name="analyze_pe_structure",
            description="分析PE文件结构",
            inputSchema={
                "type": "object",
                "properties": {
                    "file_path": {"type": "string", "description": "PE文件路径"}
                },
                "required": ["file_path"]
            }
        ),
        Tool(
            name="find_api_calls",
            description="查找API调用",
            inputSchema={
                "type": "object", 
                "properties": {
                    "file_path": {"type": "string", "description": "文件路径"},
                    "api_names": {"type": "array", "items": {"type": "string"}, "description": "要搜索的API名称"}
                },
                "required": ["file_path"]
            }
        ),
        Tool(
            name="find_time_constants",
            description="查找时间相关常量",
            inputSchema={
                "type": "object",
                "properties": {
                    "file_path": {"type": "string", "description": "文件路径"}
                },
                "required": ["file_path"]
            }
        ),
        Tool(
            name="extract_strings",
            description="提取字符串",
            inputSchema={
                "type": "object",
                "properties": {
                    "file_path": {"type": "string", "description": "文件路径"},
                    "min_length": {"type": "integer", "description": "最小字符串长度", "default": 4}
                },
                "required": ["file_path"]
            }
        )
    ]

@app.call_tool()
async def call_tool(name: str, arguments: dict):
    if name == "analyze_pe_structure":
        return await analyze_pe_structure(arguments["file_path"])
    elif name == "find_api_calls":
        api_names = arguments.get("api_names", ["timeGetTime", "GetSystemTime", "send", "recv"])
        return await find_api_calls(arguments["file_path"], api_names)
    elif name == "find_time_constants":
        return await find_time_constants(arguments["file_path"])
    elif name == "extract_strings":
        min_length = arguments.get("min_length", 4)
        return await extract_strings(arguments["file_path"], min_length)
    else:
        raise ValueError(f"Unknown tool: {name}")

async def analyze_pe_structure(file_path: str):
    """分析PE文件结构"""
    try:
        with open(file_path, 'rb') as f:
            data = f.read()
        
        if data[:2] != b'MZ':
            return [TextContent(type="text", text="❌ 不是有效的PE文件")]
        
        # 获取PE头偏移
        pe_offset = struct.unpack('<I', data[0x3C:0x40])[0]
        
        if data[pe_offset:pe_offset+4] != b'PE\\x00\\x00':
            return [TextContent(type="text", text="❌ 无效的PE签名")]
        
        # 分析COFF头
        coff_header = data[pe_offset+4:pe_offset+24]
        machine, num_sections, timestamp = struct.unpack('<HHI', coff_header[:8])
        
        result = {
            "file_type": "PE",
            "machine_type": f"0x{machine:04X}",
            "sections_count": num_sections,
            "timestamp": timestamp,
            "sections": []
        }
        
        # 分析节表
        optional_header_size = struct.unpack('<H', coff_header[16:18])[0]
        section_table_offset = pe_offset + 24 + optional_header_size
        
        for i in range(num_sections):
            section_offset = section_table_offset + i * 40
            section_data = data[section_offset:section_offset+40]
            
            name = section_data[:8].rstrip(b'\\x00').decode('ascii', errors='ignore')
            virtual_size = struct.unpack('<I', section_data[8:12])[0]
            virtual_address = struct.unpack('<I', section_data[12:16])[0]
            
            result["sections"].append({
                "name": name,
                "virtual_address": f"0x{virtual_address:08X}",
                "virtual_size": f"0x{virtual_size:08X}"
            })
        
        return [TextContent(type="text", text=json.dumps(result, indent=2))]
        
    except Exception as e:
        return [TextContent(type="text", text=f"❌ PE分析失败: {e}")]

async def find_api_calls(file_path: str, api_names: list):
    """查找API调用"""
    try:
        with open(file_path, 'rb') as f:
            data = f.read()
        
        results = {}
        for api in api_names:
            api_bytes = api.encode('ascii')
            matches = list(re.finditer(re.escape(api_bytes), data))
            if matches:
                results[api] = {
                    "count": len(matches),
                    "offsets": [f"0x{m.start():08X}" for m in matches[:5]]
                }
        
        return [TextContent(type="text", text=json.dumps(results, indent=2))]
        
    except Exception as e:
        return [TextContent(type="text", text=f"❌ API搜索失败: {e}")]

async def find_time_constants(file_path: str):
    """查找时间常量"""
    try:
        with open(file_path, 'rb') as f:
            data = f.read()
        
        time_constants = {
            86400: "24小时(秒)",
            86400000: "24小时(毫秒)",
            3600: "1小时(秒)", 
            3600000: "1小时(毫秒)",
            24: "24小时"
        }
        
        results = {}
        for constant, desc in time_constants.items():
            little_endian = struct.pack('<I', constant)
            matches = list(re.finditer(re.escape(little_endian), data))
            if matches:
                results[desc] = {
                    "value": constant,
                    "count": len(matches),
                    "offsets": [f"0x{m.start():08X}" for m in matches[:3]]
                }
        
        return [TextContent(type="text", text=json.dumps(results, indent=2))]
        
    except Exception as e:
        return [TextContent(type="text", text=f"❌ 时间常量搜索失败: {e}")]

async def extract_strings(file_path: str, min_length: int):
    """提取字符串"""
    try:
        with open(file_path, 'rb') as f:
            data = f.read()
        
        # ASCII字符串
        ascii_pattern = rb'[!-~]{' + str(min_length).encode() + rb',}'
        ascii_strings = re.findall(ascii_pattern, data)
        
        # 过滤有趣的字符串
        keywords = ['time', 'hour', '小时', 'limit', 'restrict', 'server', 'valid']
        interesting = []
        
        for s in ascii_strings:
            s_str = s.decode('ascii', errors='ignore')
            if any(keyword in s_str.lower() for keyword in keywords):
                interesting.append(s_str)
        
        result = {
            "total_strings": len(ascii_strings),
            "interesting_strings": interesting[:20]
        }
        
        return [TextContent(type="text", text=json.dumps(result, indent=2))]
        
    except Exception as e:
        return [TextContent(type="text", text=f"❌ 字符串提取失败: {e}")]

async def main():
    async with stdio_server() as (read_stream, write_stream):
        await app.run(read_stream, write_stream, app.create_initialization_options())

if __name__ == "__main__":
    asyncio.run(main())
'''
    
    # 保存MCP服务器代码
    mcp_file = Path("binary_analyzer_mcp.py")
    with open(mcp_file, 'w', encoding='utf-8') as f:
        f.write(mcp_server_code)
    
    print(f"✅ 创建二进制分析MCP服务器: {mcp_file}")
    return str(mcp_file.absolute())

def generate_mcp_config(tools, binary_analyzer_path):
    """生成MCP配置JSON"""
    config = {
        "mcpServers": {}
    }
    
    # 添加二进制分析器
    config["mcpServers"]["binary-analyzer"] = {
        "command": "python",
        "args": [str(binary_analyzer_path)],
        "description": "二进制文件分析工具"
    }
    
    # 添加发现的工具
    if 'ida_pro' in tools:
        config["mcpServers"]["ida-pro"] = {
            "command": "ida-pro-mcp",
            "args": ["--stdio"],
            "description": "IDA Pro 逆向工程工具"
        }
    
    if 'ghidra' in tools:
        config["mcpServers"]["ghidra"] = {
            "command": "python",
            "args": ["-m", "ghidra_mcp", "--stdio"],
            "description": "Ghidra 逆向工程工具"
        }
    
    if 'x64dbg' in tools:
        config["mcpServers"]["x64dbg"] = {
            "command": "python", 
            "args": ["-m", "x64dbg_mcp", "--stdio"],
            "description": "x64dbg 调试器"
        }
    
    return config

def main():
    print("=" * 60)
    print("🚀 自动配置MCP逆向工具")
    print("=" * 60)
    print()
    
    # 检查现有工具
    print("🔍 检查现有逆向工具...")
    existing_tools = check_existing_tools()
    
    if existing_tools:
        print("✅ 发现的工具:")
        for tool, path in existing_tools.items():
            print(f"  {tool}: {path}")
    else:
        print("❌ 未发现专业逆向工具")
        
        # 询问是否下载Ghidra
        choice = input("是否下载免费的Ghidra逆向工具? (y/n): ").lower()
        if choice == 'y':
            ghidra_path = download_ghidra()
            if ghidra_path:
                existing_tools['ghidra'] = ghidra_path
    
    # 创建二进制分析MCP服务器
    print("\n🔧 创建二进制分析MCP服务器...")
    binary_analyzer_path = create_binary_analyzer_mcp()
    
    # 生成MCP配置
    print("\n📝 生成MCP配置...")
    mcp_config = generate_mcp_config(existing_tools, binary_analyzer_path)
    
    # 保存配置文件
    config_file = Path("mcp_reverse_config.json")
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(mcp_config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ MCP配置已保存: {config_file}")
    print()
    
    # 显示配置内容
    print("📋 生成的MCP配置:")
    print("-" * 30)
    print(json.dumps(mcp_config, indent=2, ensure_ascii=False))
    print()
    
    # 提供导入指导
    print("🎯 下一步操作:")
    print("=" * 30)
    print("1. 在Augment中点击 'Import from JSON'")
    print(f"2. 选择文件: {config_file.absolute()}")
    print("3. 导入配置后就可以使用专业逆向工具了!")
    print()
    print("🔥 配置完成后，我就能调用:")
    print("  - PE文件结构分析")
    print("  - API调用检测") 
    print("  - 时间常量搜索")
    print("  - 字符串提取")
    if existing_tools:
        print("  - 专业逆向工具集成")
    
    print()
    input("按回车键退出...")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ 配置失败: {e}")
        input("按回车键退出...")
