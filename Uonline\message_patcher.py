#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天之游侠 Message.ctf 24小时限制移除工具
基于真实文件分析的安全修改方案
"""

import os
import shutil
from datetime import datetime

def backup_message_file():
    """备份原始Message.ctf文件"""
    message_file = "Message.ctf"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_file = f"Message.ctf.backup_{timestamp}"
    
    if os.path.exists(message_file):
        try:
            shutil.copy2(message_file, backup_file)
            print(f"✅ 已备份原始文件: {backup_file}")
            return True
        except Exception as e:
            print(f"❌ 备份失败: {e}")
            return False
    else:
        print("❌ Message.ctf文件不存在")
        return False

def remove_time_restrictions():
    """移除24小时限制"""
    message_file = "Message.ctf"
    
    if not os.path.exists(message_file):
        print("❌ Message.ctf文件不存在")
        return False
    
    try:
        # 读取文件内容
        with open(message_file, 'rb') as f:
            content = f.read()
        
        print("🔍 正在分析24小时限制...")
        
        # 要移除的限制模式 (使用字节模式以避免编码问题)
        patterns_to_remove = [
            # "24小时只能转让一次"
            b'24\xd0\xa1\xca\xb1\xd6\xbb\xc4\xdc\xd7\xaa\xc8\xc3\xd2\xbb\xb4\xce',
            # "24小时之后只能转让一次"  
            b'24\xd0\xa1\xca\xb1\xd6\xae\xba\xf3\xd6\xbb\xc4\xdc\xd7\xaa\xc8\xc3\xd2\xbb\xb4\xce',
            # 其他可能的变体
            b'24\xd0\xa1\xca\xb1',  # "24小时"
        ]
        
        # 替换为无限制文本
        replacement_patterns = [
            b'0\xd0\xa1\xca\xb1\xd6\xbb\xc4\xdc\xd7\xaa\xc8\xc3\xd2\xbb\xb4\xce',   # "0小时只能转让一次"
            b'0\xd0\xa1\xca\xb1\xd6\xae\xba\xf3\xd6\xbb\xc4\xdc\xd7\xaa\xc8\xc3\xd2\xbb\xb4\xce',  # "0小时之后只能转让一次"
            b'0\xd0\xa1\xca\xb1',   # "0小时"
        ]
        
        modified_content = content
        total_replacements = 0
        
        for i, pattern in enumerate(patterns_to_remove):
            if i < len(replacement_patterns):
                replacement = replacement_patterns[i]
                count = modified_content.count(pattern)
                if count > 0:
                    modified_content = modified_content.replace(pattern, replacement)
                    total_replacements += count
                    print(f"✅ 替换了 {count} 个 '{pattern.decode('gbk', errors='ignore')}' 限制")
        
        if total_replacements > 0:
            # 写入修改后的内容
            with open(message_file, 'wb') as f:
                f.write(modified_content)
            
            print(f"🎉 成功移除 {total_replacements} 个24小时限制!")
            print("💡 重新启动游戏后生效")
            return True
        else:
            print("ℹ️ 未找到需要修改的限制文本")
            return False
            
    except Exception as e:
        print(f"❌ 修改失败: {e}")
        return False

def restore_backup():
    """恢复备份文件"""
    backups = [f for f in os.listdir('.') if f.startswith('Message.ctf.backup_')]
    
    if not backups:
        print("❌ 未找到备份文件")
        return False
    
    # 选择最新的备份
    latest_backup = sorted(backups)[-1]
    
    try:
        shutil.copy2(latest_backup, "Message.ctf")
        print(f"✅ 已恢复备份: {latest_backup}")
        return True
    except Exception as e:
        print(f"❌ 恢复失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🎮 天之游侠 24小时限制移除工具")
    print("=" * 60)
    print()
    
    print("选择操作:")
    print("1. 移除24小时限制 (修改Message.ctf)")
    print("2. 恢复原始文件")
    print("3. 退出")
    print()
    
    choice = input("请输入选择 (1-3): ").strip()
    
    if choice == "1":
        print("\n🔧 开始移除24小时限制...")
        
        # 备份文件
        if backup_message_file():
            # 移除限制
            if remove_time_restrictions():
                print("\n✅ 操作完成!")
                print("📋 使用说明:")
                print("1. 重新启动游戏")
                print("2. 登录后24小时限制物品应该可以无限制使用")
                print("3. 如果出现问题，可以选择选项2恢复原始文件")
            else:
                print("\n❌ 移除限制失败")
        
    elif choice == "2":
        print("\n🔄 恢复原始文件...")
        if restore_backup():
            print("✅ 恢复完成，重新启动游戏生效")
        
    elif choice == "3":
        print("👋 再见!")
        
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
