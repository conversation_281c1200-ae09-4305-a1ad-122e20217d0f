#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
服务器重定向工具 - 实际修改游戏连接
这个工具可以真正修改游戏的服务器连接
"""

import os
import shutil
from datetime import datetime

def backup_config_files():
    """备份配置文件"""
    print("📁 备份配置文件...")
    
    backup_dir = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(backup_dir, exist_ok=True)
    
    files_to_backup = ['connection.ini', 'prconfig.ini']
    
    for file in files_to_backup:
        if os.path.exists(file):
            shutil.copy2(file, backup_dir)
            print(f"  ✅ 备份: {file} -> {backup_dir}/{file}")
        else:
            print(f"  ⚠️ 文件不存在: {file}")
    
    return backup_dir

def modify_connection_ini(new_ip="127.0.0.1", new_port="4001"):
    """修改connection.ini文件"""
    print(f"🌐 修改服务器连接: {new_ip}:{new_port}")
    
    if not os.path.exists('connection.ini'):
        print("❌ connection.ini文件不存在")
        return False
    
    try:
        # 读取原文件
        with open('connection.ini', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("原配置:")
        print(content)
        
        # 修改IP和端口
        new_content = content.replace('**************', new_ip)
        new_content = new_content.replace('4001', new_port)
        
        # 写入新文件
        with open('connection.ini', 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"\n✅ 修改完成！新配置:")
        with open('connection.ini', 'r', encoding='utf-8') as f:
            print(f.read())
        
        return True
        
    except Exception as e:
        print(f"❌ 修改失败: {e}")
        return False

def modify_hosts_file(domain="wud88member.t2uu.com", new_ip="127.0.0.1"):
    """修改hosts文件重定向域名"""
    print(f"🌍 修改hosts文件: {domain} -> {new_ip}")
    
    hosts_file = r"C:\Windows\System32\drivers\etc\hosts"
    
    try:
        # 检查是否有管理员权限
        if not os.access(hosts_file, os.W_OK):
            print("⚠️ 需要管理员权限修改hosts文件")
            print("请以管理员身份运行此脚本，或手动添加以下行到hosts文件:")
            print(f"{new_ip} {domain}")
            return False
        
        # 读取hosts文件
        with open(hosts_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 检查是否已存在
        entry = f"{new_ip} {domain}\n"
        if entry in lines:
            print("✅ hosts条目已存在")
            return True
        
        # 添加新条目
        lines.append(f"\n# Uonline 私服重定向\n")
        lines.append(entry)
        
        # 写入hosts文件
        with open(hosts_file, 'w', encoding='utf-8') as f:
            f.writelines(lines)
        
        print(f"✅ hosts文件修改成功")
        return True
        
    except Exception as e:
        print(f"❌ 修改hosts失败: {e}")
        return False

def create_private_server_launcher():
    """创建私服启动器"""
    print("🚀 创建私服启动器...")
    
    launcher_content = '''@echo off
echo ================================
echo    Uonline 私服启动器
echo ================================
echo.

echo 检查配置文件...
if not exist "connection.ini" (
    echo 错误: 未找到connection.ini文件
    pause
    exit
)

echo 启动游戏...
start Prmain.exe

echo.
echo 游戏已启动！
echo 如果连接失败，请检查:
echo 1. 私服是否正在运行
echo 2. IP地址是否正确
echo 3. 防火墙设置
echo.
pause
'''
    
    try:
        with open('start_private_server.bat', 'w', encoding='gbk') as f:
            f.write(launcher_content)
        
        print("✅ 私服启动器已创建: start_private_server.bat")
        return True
        
    except Exception as e:
        print(f"❌ 创建启动器失败: {e}")
        return False

def create_restore_script(backup_dir):
    """创建恢复脚本"""
    print("🔄 创建恢复脚本...")
    
    restore_content = f'''@echo off
echo ================================
echo    Uonline 配置恢复工具
echo ================================
echo.

echo 恢复配置文件...
if exist "{backup_dir}\\connection.ini" (
    copy "{backup_dir}\\connection.ini" "connection.ini"
    echo 已恢复 connection.ini
)

if exist "{backup_dir}\\prconfig.ini" (
    copy "{backup_dir}\\prconfig.ini" "prconfig.ini"
    echo 已恢复 prconfig.ini
)

echo.
echo 配置已恢复到修改前状态
pause
'''
    
    try:
        with open('restore_config.bat', 'w', encoding='gbk') as f:
            f.write(restore_content)
        
        print("✅ 恢复脚本已创建: restore_config.bat")
        return True
        
    except Exception as e:
        print(f"❌ 创建恢复脚本失败: {e}")
        return False

def show_instructions():
    """显示使用说明"""
    print("\n" + "=" * 60)
    print("📖 使用说明")
    print("=" * 60)
    print("1. 配置文件已修改，游戏将连接到新服务器")
    print("2. 使用 start_private_server.bat 启动游戏")
    print("3. 如需恢复原配置，运行 restore_config.bat")
    print("4. 确保私服在指定IP和端口运行")
    print("\n⚠️ 注意事项:")
    print("- 私服必须兼容原游戏协议")
    print("- 防火墙可能需要允许连接")
    print("- 某些反作弊可能检测修改")
    print("=" * 60)

def main():
    print("=" * 60)
    print("🎯 Uonline 服务器重定向工具")
    print("=" * 60)
    
    # 获取用户输入
    print("请输入私服信息:")
    new_ip = input("服务器IP (默认127.0.0.1): ").strip() or "127.0.0.1"
    new_port = input("服务器端口 (默认4001): ").strip() or "4001"
    
    print(f"\n将重定向到: {new_ip}:{new_port}")
    confirm = input("确认修改? (y/N): ").strip().lower()
    
    if confirm != 'y':
        print("❌ 操作已取消")
        return
    
    # 备份配置
    backup_dir = backup_config_files()
    
    # 修改配置
    success = True
    
    if modify_connection_ini(new_ip, new_port):
        print("✅ connection.ini 修改成功")
    else:
        success = False
    
    # 创建工具
    create_private_server_launcher()
    create_restore_script(backup_dir)
    
    # 显示说明
    show_instructions()
    
    if success:
        print("\n🎉 服务器重定向配置完成！")
        print(f"📁 备份目录: {backup_dir}")
    else:
        print("\n⚠️ 部分配置可能失败，请检查错误信息")

if __name__ == "__main__":
    main()
