#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版 Uonline.dll 反混淆工具
"""

import asyncio
import json
import re
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

async def analyze_uonline_dll():
    """分析Uonline.dll的混淆"""
    print("🔍 开始分析Uonline.dll混淆...")
    
    # 连接IDA Pro
    server_params = StdioServerParameters(
        command="C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python.exe",
        args=["C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\ida_pro_mcp\\server.py"]
    )
    
    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            
            # 1. 获取基本信息
            print("\n📊 获取DLL基本信息...")
            metadata = await session.call_tool("get_metadata", arguments={})
            print(f"✅ DLL信息: {metadata.content[0].text}")
            
            # 2. 分析函数列表
            print("\n📋 分析函数列表...")
            functions_result = await session.call_tool("list_functions", arguments={"offset": 0, "count": 50})
            functions_data = json.loads(functions_result.content[0].text)
            
            real_functions = []
            suspicious_functions = []
            
            print(f"📈 总函数数: {len(functions_data['data'])}")
            
            # 3. 分析每个函数
            for i, func in enumerate(functions_data['data'][:20]):  # 分析前20个函数
                addr = func['address']
                name = func['name']
                size = int(func['size'], 16)
                
                print(f"\n🔍 分析函数 {i+1}/20: {name} ({addr})")
                
                try:
                    # 获取反汇编
                    disasm_result = await session.call_tool("disassemble_function", arguments={"start_address": addr})
                    disasm_text = disasm_result.content[0].text
                    
                    # 分析混淆特征
                    analysis = analyze_function_obfuscation(disasm_text, size)
                    
                    if analysis['is_real']:
                        real_functions.append({
                            'address': addr,
                            'name': name,
                            'size': size,
                            'confidence': analysis['confidence']
                        })
                        print(f"  ✅ 真实函数 (置信度: {analysis['confidence']:.2f})")
                    else:
                        suspicious_functions.append({
                            'address': addr,
                            'name': name,
                            'junk_ratio': analysis['junk_ratio']
                        })
                        print(f"  ⚠️ 可疑函数 (垃圾比例: {analysis['junk_ratio']:.2f})")
                        
                except Exception as e:
                    print(f"  ❌ 分析失败: {e}")
                    continue
            
            # 4. 寻找时间相关函数
            print("\n⏰ 寻找时间相关函数...")
            time_functions = []
            
            # 搜索时间相关字符串
            time_keywords = ["time", "hour", "24", "timer", "clock"]
            for keyword in time_keywords:
                try:
                    search_result = await session.call_tool("search_strings", arguments={
                        "pattern": keyword, "offset": 0, "count": 10
                    })
                    search_data = json.loads(search_result.content[0].text)
                    if search_data['data']:
                        print(f"  🎯 发现关键词 '{keyword}': {len(search_data['data'])} 个匹配")
                        for match in search_data['data']:
                            time_functions.append({
                                'keyword': keyword,
                                'address': match['address'],
                                'string': match['string']
                            })
                except:
                    continue
            
            # 5. 尝试反混淆最有希望的函数
            print("\n🔧 尝试反混淆真实函数...")
            deobfuscated_functions = []
            
            for func in real_functions[:3]:  # 反混淆前3个最可能的真实函数
                try:
                    print(f"\n🎯 反混淆函数: {func['name']} ({func['address']})")
                    
                    # 尝试反编译
                    try:
                        decompile_result = await session.call_tool("decompile_function", arguments={"address": func['address']})
                        decompiled_code = decompile_result.content[0].text
                        
                        # 清理代码
                        cleaned_code = clean_decompiled_code(decompiled_code)
                        
                        deobfuscated_functions.append({
                            'address': func['address'],
                            'name': func['name'],
                            'cleaned_code': cleaned_code
                        })
                        
                        print(f"  ✅ 反编译成功")
                        print(f"  📝 清理后代码预览:")
                        print("  " + "="*50)
                        preview = cleaned_code[:300] + "..." if len(cleaned_code) > 300 else cleaned_code
                        for line in preview.split('\n')[:10]:
                            print(f"  {line}")
                        print("  " + "="*50)
                        
                    except Exception as e:
                        print(f"  ⚠️ 反编译失败: {e}")
                        
                        # 使用反汇编
                        disasm_result = await session.call_tool("disassemble_function", arguments={"start_address": func['address']})
                        disasm_text = disasm_result.content[0].text
                        cleaned_asm = clean_assembly_code(disasm_text)
                        
                        deobfuscated_functions.append({
                            'address': func['address'],
                            'name': func['name'],
                            'cleaned_asm': cleaned_asm
                        })
                        
                        print(f"  📝 清理后汇编预览:")
                        print("  " + "="*50)
                        for line in cleaned_asm.split('\n')[:10]:
                            print(f"  {line}")
                        print("  " + "="*50)
                        
                except Exception as e:
                    print(f"  ❌ 反混淆失败: {e}")
            
            # 6. 生成报告
            report = {
                'dll_info': json.loads(metadata.content[0].text),
                'analysis_summary': {
                    'total_functions_analyzed': len(functions_data['data']),
                    'real_functions': len(real_functions),
                    'suspicious_functions': len(suspicious_functions),
                    'time_related_strings': len(time_functions)
                },
                'real_functions': real_functions,
                'time_functions': time_functions,
                'deobfuscated_functions': deobfuscated_functions
            }
            
            # 保存报告
            with open('uonline_deobfuscation_report.json', 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            print("\n" + "="*60)
            print("🎉 反混淆分析完成！")
            print("="*60)
            print(f"📊 分析结果:")
            print(f"  - 总函数数: {len(functions_data['data'])}")
            print(f"  - 真实函数: {len(real_functions)}")
            print(f"  - 可疑函数: {len(suspicious_functions)}")
            print(f"  - 时间相关: {len(time_functions)}")
            print(f"  - 成功反混淆: {len(deobfuscated_functions)}")
            print(f"\n📄 详细报告: uonline_deobfuscation_report.json")
            
            return report

def analyze_function_obfuscation(disasm_text, size):
    """分析函数混淆程度"""
    lines = disasm_text.split('\n')
    instruction_lines = [l for l in lines if ':' in l and l.strip()]
    
    if not instruction_lines:
        return {'is_real': False, 'confidence': 0.0, 'junk_ratio': 1.0}
    
    junk_count = 0
    total_instructions = len(instruction_lines)
    
    # 垃圾指令模式
    junk_patterns = [
        'pushf', 'popf', 'nop',
        'xor.*,.*; same',
        'add.*,.*0',
        'sub.*,.*0',
        'bswap.*bswap',
        'neg.*neg',
        'not.*not'
    ]
    
    for line in instruction_lines:
        instruction = line.split(':', 1)[1].strip() if ':' in line else line
        
        for pattern in junk_patterns:
            if re.search(pattern, instruction, re.IGNORECASE):
                junk_count += 1
                break
    
    junk_ratio = junk_count / total_instructions if total_instructions > 0 else 1.0
    confidence = 1.0 - junk_ratio
    is_real = junk_ratio < 0.5 and size > 10
    
    return {
        'is_real': is_real,
        'confidence': confidence,
        'junk_ratio': junk_ratio
    }

def clean_decompiled_code(code):
    """清理反编译代码"""
    lines = code.split('\n')
    cleaned_lines = []
    
    for line in lines:
        line = line.strip()
        
        # 跳过注释和空行
        if not line or line.startswith('/*') or line.startswith('//'):
            continue
        
        # 跳过行号注释
        if re.match(r'/\* line: \d+', line):
            continue
        
        # 简化变量名
        line = re.sub(r'\bv\d+\b', 'var', line)
        line = re.sub(r'\ba\d+\b', 'arg', line)
        
        cleaned_lines.append(line)
    
    return '\n'.join(cleaned_lines)

def clean_assembly_code(code):
    """清理汇编代码"""
    lines = code.split('\n')
    cleaned_lines = []
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
        
        # 跳过垃圾指令
        if any(junk in line.lower() for junk in ['pushf', 'popf', 'nop']):
            continue
        
        # 跳过重复的位操作
        if re.search(r'bswap.*bswap|neg.*neg|not.*not', line, re.IGNORECASE):
            continue
        
        cleaned_lines.append(line)
    
    return '\n'.join(cleaned_lines)

if __name__ == "__main__":
    try:
        asyncio.run(analyze_uonline_dll())
        input("\n按回车键退出...")
    except Exception as e:
        print(f"❌ 程序出错: {e}")
        input("按回车键退出...")
