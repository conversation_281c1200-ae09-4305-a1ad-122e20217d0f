#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析white.dll - 可能包含24小时限制逻辑的神秘DLL
"""

import os
import re
import struct
from pathlib import Path

def analyze_pe_header(file_path):
    """分析PE文件头"""
    print(f"🔍 分析 {file_path} PE文件头...")
    
    try:
        with open(file_path, 'rb') as f:
            # 读取DOS头
            dos_header = f.read(64)
            if dos_header[:2] != b'MZ':
                print("❌ 不是有效的PE文件")
                return None
            
            # 获取PE头偏移
            pe_offset = struct.unpack('<L', dos_header[60:64])[0]
            f.seek(pe_offset)
            
            # 读取PE签名
            pe_sig = f.read(4)
            if pe_sig != b'PE\x00\x00':
                print("❌ 无效的PE签名")
                return None
            
            # 读取COFF头
            coff_header = f.read(20)
            machine, num_sections, timestamp, ptr_to_sym, num_symbols, opt_header_size, characteristics = struct.unpack('<HHLLLLH', coff_header)
            
            print(f"  📋 PE文件信息:")
            print(f"    - 机器类型: 0x{machine:04X}")
            print(f"    - 节数量: {num_sections}")
            print(f"    - 时间戳: 0x{timestamp:08X}")
            print(f"    - 特征: 0x{characteristics:04X}")
            
            # 读取可选头
            opt_header = f.read(opt_header_size)
            if len(opt_header) >= 28:
                magic, major_linker, minor_linker, size_of_code, size_of_init_data, size_of_uninit_data, entry_point, base_of_code = struct.unpack('<HHBBLLLL', opt_header[:28])
                
                print(f"    - 入口点: 0x{entry_point:08X}")
                print(f"    - 代码大小: 0x{size_of_code:X}")
                print(f"    - 初始化数据大小: 0x{size_of_init_data:X}")
            
            return {
                'machine': machine,
                'sections': num_sections,
                'timestamp': timestamp,
                'entry_point': entry_point if 'entry_point' in locals() else 0,
                'code_size': size_of_code if 'size_of_code' in locals() else 0
            }
            
    except Exception as e:
        print(f"❌ 分析PE头失败: {e}")
        return None

def extract_strings(file_path, min_length=4):
    """提取ASCII字符串"""
    print(f"🔍 提取 {file_path} 中的字符串...")
    
    strings = []
    try:
        with open(file_path, 'rb') as f:
            data = f.read()
            
        # 查找ASCII字符串
        pattern = rb'[\x20-\x7E]{' + str(min_length).encode() + rb',}'
        matches = re.findall(pattern, data)
        
        for match in matches:
            try:
                string = match.decode('ascii')
                if len(string) >= min_length:
                    strings.append(string)
            except:
                continue
        
        print(f"  📊 找到 {len(strings)} 个字符串")
        
        # 查找可疑的字符串
        suspicious = []
        keywords = ['time', 'hour', '24', 'limit', 'restrict', 'check', 'valid', 'expire', 'transfer', 'trade']
        
        for s in strings:
            for keyword in keywords:
                if keyword.lower() in s.lower():
                    suspicious.append(s)
                    break
        
        if suspicious:
            print(f"  🎯 可疑字符串 ({len(suspicious)}):")
            for s in suspicious[:10]:  # 只显示前10个
                print(f"    - {s}")
        
        return strings, suspicious
        
    except Exception as e:
        print(f"❌ 提取字符串失败: {e}")
        return [], []

def find_api_calls(file_path):
    """查找API调用"""
    print(f"🔍 查找 {file_path} 中的API调用...")
    
    api_calls = []
    time_apis = []
    
    try:
        with open(file_path, 'rb') as f:
            data = f.read()
        
        # 常见的时间相关API
        time_api_list = [
            b'GetSystemTime', b'GetLocalTime', b'GetTickCount', b'timeGetTime',
            b'GetFileTime', b'SystemTimeToFileTime', b'FileTimeToSystemTime',
            b'QueryPerformanceCounter', b'GetSystemTimeAsFileTime'
        ]
        
        # 其他重要API
        other_apis = [
            b'CreateFile', b'ReadFile', b'WriteFile', b'RegOpenKey', b'RegQueryValue',
            b'LoadLibrary', b'GetProcAddress', b'VirtualAlloc', b'CreateProcess'
        ]
        
        all_apis = time_api_list + other_apis
        
        for api in all_apis:
            if api in data:
                api_name = api.decode('ascii')
                api_calls.append(api_name)
                if api in time_api_list:
                    time_apis.append(api_name)
        
        print(f"  📊 找到 {len(api_calls)} 个API调用")
        if api_calls:
            print(f"    - API列表: {', '.join(api_calls)}")
        
        if time_apis:
            print(f"  ⏰ 时间相关API ({len(time_apis)}):")
            for api in time_apis:
                print(f"    - {api}")
        
        return api_calls, time_apis
        
    except Exception as e:
        print(f"❌ 查找API调用失败: {e}")
        return [], []

def analyze_imports(file_path):
    """分析导入表"""
    print(f"🔍 分析 {file_path} 导入表...")
    
    try:
        with open(file_path, 'rb') as f:
            data = f.read()
        
        # 查找常见的DLL名称
        dlls = [b'kernel32.dll', b'user32.dll', b'advapi32.dll', b'winmm.dll', 
                b'msvcrt.dll', b'mfc42.dll', b'ntdll.dll']
        
        found_dlls = []
        for dll in dlls:
            if dll.lower() in data.lower():
                found_dlls.append(dll.decode('ascii'))
        
        if found_dlls:
            print(f"  📚 导入的DLL ({len(found_dlls)}):")
            for dll in found_dlls:
                print(f"    - {dll}")
        
        return found_dlls
        
    except Exception as e:
        print(f"❌ 分析导入表失败: {e}")
        return []

def search_24_hour_patterns(file_path):
    """搜索24小时相关的模式"""
    print(f"🔍 搜索 {file_path} 中的24小时模式...")
    
    patterns_found = []
    
    try:
        with open(file_path, 'rb') as f:
            data = f.read()
        
        # 搜索数字24的各种表示
        patterns = [
            b'\x18\x00\x00\x00',  # 24 as little-endian DWORD
            b'\x00\x00\x00\x18',  # 24 as big-endian DWORD
            b'\x18\x00',          # 24 as little-endian WORD
            b'\x00\x18',          # 24 as big-endian WORD
            b'\x18',              # 24 as byte
            b'24',                # ASCII "24"
        ]
        
        for i, pattern in enumerate(patterns):
            count = data.count(pattern)
            if count > 0:
                pattern_name = [
                    "24 (DWORD LE)", "24 (DWORD BE)", "24 (WORD LE)", 
                    "24 (WORD BE)", "24 (BYTE)", "ASCII '24'"
                ][i]
                patterns_found.append((pattern_name, count))
                print(f"  🎯 找到 {pattern_name}: {count} 次")
        
        # 搜索86400 (24*60*60秒)
        seconds_24h = [
            b'\x80\x51\x01\x00',  # 86400 as little-endian DWORD
            b'\x00\x01\x51\x80',  # 86400 as big-endian DWORD
        ]
        
        for i, pattern in enumerate(seconds_24h):
            count = data.count(pattern)
            if count > 0:
                pattern_name = f"86400秒 ({'LE' if i == 0 else 'BE'})"
                patterns_found.append((pattern_name, count))
                print(f"  🎯 找到 {pattern_name}: {count} 次")
        
        return patterns_found
        
    except Exception as e:
        print(f"❌ 搜索24小时模式失败: {e}")
        return []

def main():
    white_dll_path = "Uonline/white.dll"
    
    print("=" * 60)
    print("🔍 white.dll 深度分析")
    print("=" * 60)
    
    if not os.path.exists(white_dll_path):
        print(f"❌ 文件不存在: {white_dll_path}")
        return
    
    # PE头分析
    pe_info = analyze_pe_header(white_dll_path)
    
    print("\n" + "-" * 50)
    
    # 字符串提取
    strings, suspicious = extract_strings(white_dll_path)
    
    print("\n" + "-" * 50)
    
    # API调用分析
    api_calls, time_apis = find_api_calls(white_dll_path)
    
    print("\n" + "-" * 50)
    
    # 导入表分析
    imports = analyze_imports(white_dll_path)
    
    print("\n" + "-" * 50)
    
    # 24小时模式搜索
    patterns = search_24_hour_patterns(white_dll_path)
    
    print("\n" + "=" * 60)
    print("📊 分析总结")
    print("=" * 60)
    
    print(f"\n✅ 基本信息:")
    if pe_info:
        print(f"  - 入口点: 0x{pe_info.get('entry_point', 0):08X}")
        print(f"  - 代码大小: 0x{pe_info.get('code_size', 0):X}")
    
    print(f"\n✅ 发现:")
    print(f"  - 字符串总数: {len(strings)}")
    print(f"  - 可疑字符串: {len(suspicious)}")
    print(f"  - API调用: {len(api_calls)}")
    print(f"  - 时间API: {len(time_apis)}")
    print(f"  - 导入DLL: {len(imports)}")
    print(f"  - 24小时模式: {len(patterns)}")
    
    if time_apis:
        print(f"\n🎯 关键发现 - 时间API:")
        for api in time_apis:
            print(f"  - {api}")
    
    if patterns:
        print(f"\n🎯 关键发现 - 24小时模式:")
        for pattern_name, count in patterns:
            print(f"  - {pattern_name}: {count} 次")
    
    print(f"\n💡 分析结论:")
    if time_apis or patterns:
        print("  🔥 white.dll 可能包含时间验证逻辑!")
        print("  🔥 建议进一步逆向分析此文件")
    else:
        print("  ❓ 未发现明显的时间验证特征")
        print("  ❓ 可能需要动态分析或更深入的静态分析")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
