#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Uonline 游戏全面逆向分析
不局限于24小时，寻找所有有趣的游戏机制和可修改内容
"""

import os
import re
import struct
from pathlib import Path

def analyze_message_ctf_for_game_mechanics():
    """分析Message.ctf寻找游戏机制"""
    print("🔍 分析Message.ctf中的游戏机制...")
    
    try:
        with open("Uonline/Message.ctf", 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # 寻找各种游戏机制
        mechanics = {
            "经验值": [],
            "金币": [],
            "属性加成": [],
            "技能": [],
            "装备": [],
            "怪物": [],
            "地图": [],
            "任务": [],
            "PK系统": [],
            "公会": [],
            "商店": [],
            "强化": []
        }
        
        # 经验值相关
        exp_patterns = [r'经验.*?(\d+)', r'EXP.*?(\d+)', r'exp.*?(\d+)']
        for pattern in exp_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            mechanics["经验值"].extend(matches)
        
        # 金币相关
        gold_patterns = [r'金币.*?(\d+)', r'gold.*?(\d+)', r'钱.*?(\d+)']
        for pattern in gold_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            mechanics["金币"].extend(matches)
        
        # 属性加成
        stat_patterns = [
            r'攻击力.*?(\d+)', r'防御力.*?(\d+)', r'HP.*?(\d+)', r'MP.*?(\d+)',
            r'力量.*?(\d+)', r'敏捷.*?(\d+)', r'智力.*?(\d+)', r'体力.*?(\d+)'
        ]
        for pattern in stat_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            mechanics["属性加成"].extend(matches)
        
        # 技能相关
        skill_patterns = [r'技能.*?(\d+)', r'魔法.*?(\d+)', r'法术.*?(\d+)']
        for pattern in skill_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            mechanics["技能"].extend(matches)
        
        # 装备相关
        equipment_patterns = [r'武器.*?(\d+)', r'防具.*?(\d+)', r'装备.*?(\d+)']
        for pattern in equipment_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            mechanics["装备"].extend(matches)
        
        # 显示结果
        for category, values in mechanics.items():
            if values:
                unique_values = list(set(values))[:10]  # 去重并只显示前10个
                print(f"  🎯 {category}: {len(unique_values)} 种 - {', '.join(unique_values)}")
        
        return mechanics
        
    except Exception as e:
        print(f"❌ 分析Message.ctf失败: {e}")
        return {}

def analyze_pak_files():
    """分析PAK文件结构"""
    print("🔍 分析PAK文件...")
    
    pak_files = []
    for file in os.listdir("Uonline"):
        if file.endswith('.pak'):
            pak_files.append(file)
    
    print(f"  📊 找到 {len(pak_files)} 个PAK文件")
    
    # 分类PAK文件
    categories = {
        "地图": [],
        "角色": [],
        "装备": [],
        "物品": [],
        "效果": [],
        "音效": [],
        "其他": []
    }
    
    for pak in pak_files:
        if pak.startswith('map-'):
            categories["地图"].append(pak)
        elif any(x in pak.lower() for x in ['char', 'npc', 'player']):
            categories["角色"].append(pak)
        elif any(x in pak.lower() for x in ['eqp', 'weapon', 'armor']):
            categories["装备"].append(pak)
        elif any(x in pak.lower() for x in ['item', 'potion']):
            categories["物品"].append(pak)
        elif any(x in pak.lower() for x in ['effect', 'fx']):
            categories["效果"].append(pak)
        elif any(x in pak.lower() for x in ['wav', 'sound']):
            categories["音效"].append(pak)
        else:
            categories["其他"].append(pak)
    
    for category, files in categories.items():
        if files:
            print(f"  🎯 {category} ({len(files)}): {', '.join(files[:5])}")
            if len(files) > 5:
                print(f"      ... 还有 {len(files) - 5} 个文件")
    
    return categories

def analyze_executable_for_cheats():
    """分析主程序寻找作弊相关功能"""
    print("🔍 分析主程序寻找作弊/调试功能...")
    
    try:
        with open("Uonline/Prmain.exe", 'rb') as f:
            data = f.read()
        
        # 寻找调试/作弊相关字符串
        cheat_keywords = [
            b'debug', b'cheat', b'god', b'admin', b'gm', b'test',
            b'console', b'command', b'hack', b'modify', b'edit'
        ]
        
        found_cheats = []
        for keyword in cheat_keywords:
            if keyword in data.lower():
                found_cheats.append(keyword.decode('ascii'))
        
        if found_cheats:
            print(f"  🎯 可能的调试/作弊关键词: {', '.join(found_cheats)}")
        
        # 寻找常见的作弊数值
        cheat_values = [
            999999,    # 常见的最大值
            9999999,   # 更大的最大值
            100000,    # 十万
            1000000,   # 一百万
        ]
        
        found_values = []
        for value in cheat_values:
            pattern = struct.pack('<L', value)
            if pattern in data:
                found_values.append(value)
        
        if found_values:
            print(f"  🎯 可能的作弊数值: {', '.join(map(str, found_values))}")
        
        return found_cheats, found_values
        
    except Exception as e:
        print(f"❌ 分析主程序失败: {e}")
        return [], []

def analyze_config_files():
    """分析配置文件"""
    print("🔍 分析配置文件...")
    
    config_files = ['connection.ini', 'prconfig.ini', 'version.dat']
    
    for config_file in config_files:
        file_path = f"Uonline/{config_file}"
        if os.path.exists(file_path):
            print(f"\n  📄 {config_file}:")
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # 寻找有趣的配置项
                lines = content.split('\n')[:20]  # 只显示前20行
                for line in lines:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        print(f"    - {line}")
                        
            except Exception as e:
                print(f"    ❌ 读取失败: {e}")

def find_network_protocols():
    """寻找网络协议相关信息"""
    print("🔍 寻找网络协议信息...")
    
    try:
        # 分析主程序中的网络相关内容
        with open("Uonline/Prmain.exe", 'rb') as f:
            data = f.read()
        
        # 寻找IP地址模式
        ip_pattern = re.compile(rb'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}')
        ips = ip_pattern.findall(data)
        
        if ips:
            unique_ips = list(set(ips))
            print(f"  🌐 发现IP地址: {', '.join([ip.decode() for ip in unique_ips])}")
        
        # 寻找端口号
        common_ports = [4001, 8080, 3306, 1433, 5432, 6379]
        found_ports = []
        
        for port in common_ports:
            port_bytes = struct.pack('<H', port)
            if port_bytes in data:
                found_ports.append(port)
        
        if found_ports:
            print(f"  🔌 可能的端口: {', '.join(map(str, found_ports))}")
        
        # 寻找协议关键词
        protocol_keywords = [
            b'http', b'tcp', b'udp', b'socket', b'connect',
            b'send', b'recv', b'packet', b'protocol'
        ]
        
        found_protocols = []
        for keyword in protocol_keywords:
            if keyword in data.lower():
                found_protocols.append(keyword.decode('ascii'))
        
        if found_protocols:
            print(f"  📡 网络协议关键词: {', '.join(found_protocols)}")
        
        return unique_ips if 'unique_ips' in locals() else [], found_ports, found_protocols
        
    except Exception as e:
        print(f"❌ 分析网络协议失败: {e}")
        return [], [], []

def analyze_anti_cheat_systems():
    """分析反作弊系统"""
    print("🔍 分析反作弊系统...")
    
    # 分析HShield目录
    hshield_path = "Uonline/HShield"
    hackshield_path = "Uonline/HackShield"
    
    for shield_path, shield_name in [(hshield_path, "HShield"), (hackshield_path, "HackShield")]:
        if os.path.exists(shield_path):
            print(f"\n  🛡️ {shield_name}:")
            files = os.listdir(shield_path)
            for file in files:
                file_path = os.path.join(shield_path, file)
                if os.path.isfile(file_path):
                    size = os.path.getsize(file_path)
                    print(f"    - {file}: {size:,} 字节")

def main():
    print("=" * 70)
    print("🎮 Uonline 游戏全面逆向分析")
    print("🔍 寻找所有有趣的游戏机制和可修改内容")
    print("=" * 70)
    
    # 1. 游戏机制分析
    print("\n📋 第一部分: 游戏机制分析")
    print("-" * 50)
    mechanics = analyze_message_ctf_for_game_mechanics()
    
    # 2. 资源文件分析
    print("\n📦 第二部分: 游戏资源分析")
    print("-" * 50)
    pak_categories = analyze_pak_files()
    
    # 3. 作弊功能分析
    print("\n🔧 第三部分: 调试/作弊功能分析")
    print("-" * 50)
    cheats, values = analyze_executable_for_cheats()
    
    # 4. 配置文件分析
    print("\n⚙️ 第四部分: 配置文件分析")
    print("-" * 50)
    analyze_config_files()
    
    # 5. 网络协议分析
    print("\n🌐 第五部分: 网络协议分析")
    print("-" * 50)
    ips, ports, protocols = find_network_protocols()
    
    # 6. 反作弊系统分析
    print("\n🛡️ 第六部分: 反作弊系统分析")
    print("-" * 50)
    analyze_anti_cheat_systems()
    
    # 总结
    print("\n" + "=" * 70)
    print("📊 发现总结")
    print("=" * 70)
    
    print("\n🎯 有趣的发现:")
    
    if mechanics:
        print("  🎮 游戏机制:")
        for category, values in mechanics.items():
            if values:
                print(f"    - {category}: {len(set(values))} 种不同数值")
    
    if pak_categories:
        total_paks = sum(len(files) for files in pak_categories.values())
        print(f"  📦 游戏资源: {total_paks} 个PAK文件")
    
    if cheats:
        print(f"  🔧 调试功能: 发现 {len(cheats)} 个相关关键词")
    
    if ips:
        print(f"  🌐 网络: {len(ips)} 个IP地址, {len(ports)} 个端口")
    
    print("\n💡 可以进一步研究的方向:")
    print("  1. 🎮 修改游戏数值 (经验值、金币、属性)")
    print("  2. 📦 提取和修改PAK资源文件")
    print("  3. 🔧 寻找隐藏的调试命令")
    print("  4. 🌐 分析网络协议和服务器通信")
    print("  5. 🛡️ 绕过反作弊系统")
    print("  6. 🗺️ 修改地图和游戏内容")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
