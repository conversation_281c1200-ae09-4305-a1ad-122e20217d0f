#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import struct
import os

def analyze_pe_entry_point(filename):
    """分析PE文件入口点"""
    try:
        with open(filename, 'rb') as f:
            # 读取DOS头
            f.seek(0x3C)
            pe_offset = struct.unpack('<I', f.read(4))[0]
            
            # 跳到PE头
            f.seek(pe_offset + 4)  # 跳过PE签名
            
            # 读取COFF头
            f.read(20)  # 跳过COFF头
            
            # 读取可选头
            f.seek(pe_offset + 24 + 16)  # 跳到入口点
            entry_point = struct.unpack('<I', f.read(4))[0]
            
            f.seek(pe_offset + 24 + 28)  # 跳到镜像基址
            image_base = struct.unpack('<I', f.read(4))[0]
            
            print(f"{filename}:")
            print(f"  镜像基址: 0x{image_base:08X}")
            print(f"  入口点RVA: 0x{entry_point:08X}")
            print(f"  实际入口: 0x{image_base + entry_point:08X}")
            
            return image_base, entry_point
            
    except Exception as e:
        print(f"{filename}: 分析失败 - {e}")
        return None, None

def find_call_instructions(filename, max_calls=20):
    """查找CALL指令"""
    try:
        with open(filename, 'rb') as f:
            data = f.read()
        
        calls = []
        for i in range(len(data) - 4):
            if data[i] == 0xE8:  # CALL指令
                rel_addr = struct.unpack('<i', data[i+1:i+5])[0]
                target = i + 5 + rel_addr
                if 0 <= target < len(data):
                    calls.append((i, target))
        
        print(f"{filename} - CALL指令 (前{max_calls}个):")
        for i, (offset, target) in enumerate(calls[:max_calls]):
            print(f"  0x{offset:08X}: CALL 0x{target:08X}")
            
        return calls
        
    except Exception as e:
        print(f"{filename}: CALL分析失败 - {e}")
        return []

def find_push_addresses(filename, max_push=15):
    """查找PUSH地址指令"""
    try:
        with open(filename, 'rb') as f:
            data = f.read()
        
        pushes = []
        for i in range(len(data) - 4):
            if data[i] == 0x68:  # PUSH immediate
                addr = struct.unpack('<I', data[i+1:i+5])[0]
                if 0x400000 <= addr <= 0x800000:  # 可能的代码/数据地址
                    pushes.append((i, addr))
        
        print(f"{filename} - PUSH地址 (前{max_push}个):")
        for i, (offset, addr) in enumerate(pushes[:max_push]):
            print(f"  0x{offset:08X}: PUSH 0x{addr:08X}")
            
        return pushes
        
    except Exception as e:
        print(f"{filename}: PUSH分析失败 - {e}")
        return []

def find_api_imports(filename):
    """查找API导入"""
    try:
        with open(filename, 'rb') as f:
            data = f.read()
        
        # 常见API名称
        apis = [
            'CreateFileA', 'CreateFileW', 'ReadFile', 'WriteFile',
            'VirtualAlloc', 'VirtualProtect', 'LoadLibraryA', 'GetProcAddress',
            'socket', 'connect', 'send', 'recv', 'WSAStartup',
            'CreateThread', 'CreateProcess', 'TerminateProcess'
        ]
        
        found = []
        for api in apis:
            pos = data.find(api.encode('ascii'))
            if pos != -1:
                found.append((pos, api))
        
        print(f"{filename} - API导入:")
        for pos, api in found:
            print(f"  0x{pos:08X}: {api}")
            
        return found
        
    except Exception as e:
        print(f"{filename}: API分析失败 - {e}")
        return []

def find_network_constants(filename):
    """查找网络相关常量"""
    try:
        with open(filename, 'rb') as f:
            data = f.read()
        
        # 查找端口4001 (0x0FA1)
        port_4001 = struct.pack('<H', 4001)
        positions = []
        offset = 0
        while True:
            pos = data.find(port_4001, offset)
            if pos == -1:
                break
            positions.append(pos)
            offset = pos + 1
        
        print(f"{filename} - 端口4001出现位置:")
        for pos in positions[:10]:
            print(f"  0x{pos:08X}")
        
        # 查找IP地址 **************
        ip_bytes = struct.pack('BBBB', 121, 40, 205, 138)
        ip_pos = data.find(ip_bytes)
        if ip_pos != -1:
            print(f"  IP地址**************: 0x{ip_pos:08X}")
        
        return positions
        
    except Exception as e:
        print(f"{filename}: 网络分析失败 - {e}")
        return []

def dump_hex_at_offset(filename, offset, size=64):
    """在指定偏移处转储十六进制"""
    try:
        with open(filename, 'rb') as f:
            f.seek(offset)
            data = f.read(size)
        
        print(f"{filename} - 0x{offset:08X} 处的十六进制:")
        for i in range(0, len(data), 16):
            chunk = data[i:i+16]
            hex_str = ' '.join(f'{b:02X}' for b in chunk)
            ascii_str = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in chunk)
            print(f"  {offset+i:08X}: {hex_str:<48} {ascii_str}")
            
    except Exception as e:
        print(f"转储失败: {e}")

def main():
    print("🎯 二进制逆向分析")
    print("=" * 50)
    
    files = ['Prmain.exe', 'Uonline.dll', 'netmine.dll']
    
    for filename in files:
        if os.path.exists(filename):
            print(f"\n📁 分析 {filename}")
            print("-" * 40)
            
            # PE入口点分析
            base, entry = analyze_pe_entry_point(filename)
            
            # CALL指令分析
            calls = find_call_instructions(filename, 10)
            
            # PUSH地址分析
            pushes = find_push_addresses(filename, 10)
            
            # API导入分析
            apis = find_api_imports(filename)
            
            # 网络常量分析
            if 'net' in filename.lower() or filename == 'Prmain.exe':
                find_network_constants(filename)
            
            # 如果找到了入口点，转储入口点代码
            if entry:
                print(f"\n入口点代码:")
                dump_hex_at_offset(filename, entry, 64)

if __name__ == "__main__":
    main()
