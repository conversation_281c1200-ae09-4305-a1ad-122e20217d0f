#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试IDA Pro MCP连接
"""

import asyncio
import json
import sys
from pathlib import Path

# 尝试导入MCP
try:
    from mcp import ClientSession, StdioServerParameters
    from mcp.client.stdio import stdio_client
    MCP_AVAILABLE = True
    print("✅ MCP模块可用")
except ImportError as e:
    MCP_AVAILABLE = False
    print(f"❌ MCP模块不可用: {e}")

async def test_ida_pro_mcp():
    """测试IDA Pro MCP连接"""
    if not MCP_AVAILABLE:
        print("❌ MCP不可用，无法测试")
        return False
    
    try:
        print("🔍 尝试连接IDA Pro MCP...")
        
        # IDA Pro MCP服务器参数
        server_params = StdioServerParameters(
            command="D:\\python3.11\\python.exe",
            args=["D:\\python3.11\\Lib\\site-packages\\ida_pro_mcp\\server.py"]
        )
        
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                print("🔗 成功连接到IDA Pro MCP服务器！")
                
                # 获取可用工具
                tools_result = await session.list_tools()
                available_tools = [tool.name for tool in tools_result.tools]
                
                print(f"📋 可用工具数量: {len(available_tools)}")
                print("🛠️ 可用工具列表:")
                for tool in available_tools:
                    print(f"  - {tool}")
                
                # 测试连接检查
                if "check_connection" in available_tools:
                    print("\n🧪 测试连接状态...")
                    try:
                        result = await session.call_tool(
                            "check_connection",
                            arguments={}
                        )
                        print("✅ 连接测试成功")
                        print(f"📄 结果: {result.content[0].text}")
                    except Exception as e:
                        print(f"❌ 连接测试失败: {e}")
                
                # 测试获取元数据
                if "get_metadata" in available_tools:
                    print("\n📊 获取IDA Pro元数据...")
                    try:
                        result = await session.call_tool(
                            "get_metadata",
                            arguments={}
                        )
                        print("✅ 元数据获取成功")
                        print(f"📄 结果: {result.content[0].text}")
                    except Exception as e:
                        print(f"❌ 元数据获取失败: {e}")
                
                # 测试字符串搜索
                if "search_strings" in available_tools:
                    print("\n🔍 测试字符串搜索功能...")
                    try:
                        result = await session.call_tool(
                            "search_strings",
                            arguments={"pattern": "timeGetTime"}
                        )
                        print("✅ 字符串搜索成功")
                        print(f"📄 结果: {result.content[0].text}")
                    except Exception as e:
                        print(f"❌ 字符串搜索失败: {e}")
                
                return True
                
    except Exception as e:
        print(f"❌ IDA Pro MCP连接失败: {e}")
        return False

async def test_basic_ida_functions():
    """测试基础IDA功能"""
    if not MCP_AVAILABLE:
        return False
    
    try:
        print("\n" + "="*50)
        print("🎯 测试IDA Pro基础功能")
        print("="*50)
        
        server_params = StdioServerParameters(
            command="D:\\python3.11\\python.exe",
            args=["D:\\python3.11\\Lib\\site-packages\\ida_pro_mcp\\server.py"]
        )
        
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                
                # 测试列出函数
                print("\n📋 测试列出函数...")
                try:
                    result = await session.call_tool(
                        "list_functions",
                        arguments={}
                    )
                    print("✅ 函数列表获取成功")
                    print(f"📄 结果预览: {result.content[0].text[:200]}...")
                except Exception as e:
                    print(f"❌ 函数列表获取失败: {e}")
                
                # 测试列出字符串
                print("\n📝 测试列出字符串...")
                try:
                    result = await session.call_tool(
                        "list_strings",
                        arguments={}
                    )
                    print("✅ 字符串列表获取成功")
                    print(f"📄 结果预览: {result.content[0].text[:200]}...")
                except Exception as e:
                    print(f"❌ 字符串列表获取失败: {e}")
                
                # 测试获取入口点
                print("\n🚪 测试获取入口点...")
                try:
                    result = await session.call_tool(
                        "get_entry_points",
                        arguments={}
                    )
                    print("✅ 入口点获取成功")
                    print(f"📄 结果: {result.content[0].text}")
                except Exception as e:
                    print(f"❌ 入口点获取失败: {e}")
                
                return True
                
    except Exception as e:
        print(f"❌ IDA Pro功能测试失败: {e}")
        return False

async def main():
    print("=" * 60)
    print("🧪 IDA Pro MCP连接测试")
    print("=" * 60)
    print()
    
    # 基础连接测试
    connection_success = await test_ida_pro_mcp()
    
    if connection_success:
        print("\n🎉 IDA Pro MCP连接成功！")
        
        # 进一步功能测试
        function_success = await test_basic_ida_functions()
        
        if function_success:
            print("\n🚀 IDA Pro MCP完全可用！")
            print("💡 现在可以进行专业逆向分析了！")
        else:
            print("\n⚠️ IDA Pro MCP连接成功，但部分功能不可用")
    
    else:
        print("\n❌ IDA Pro MCP连接失败")
        print("💡 可能的原因:")
        print("  1. IDA Pro未安装或路径不正确")
        print("  2. ida-pro-mcp包未正确安装")
        print("  3. Python路径配置问题")
        print("  4. 权限问题")
    
    print("\n" + "="*60)
    print("🎯 测试完成")
    print("="*60)

if __name__ == "__main__":
    try:
        asyncio.run(main())
        input("\n按回车键退出...")
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        input("按回车键退出...")
