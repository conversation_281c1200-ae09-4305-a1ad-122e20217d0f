#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Uonline 游戏修改器 - 实际修改游戏数据
基于真实数据分析的修改工具
"""

import re
import os
import shutil
from datetime import datetime

class UonlineModifier:
    def __init__(self):
        self.backup_dir = "backups"
        self.message_file = "Uonline/Message.ctf"
        
        # 确保备份目录存在
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
    
    def backup_file(self, filepath):
        """备份文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = os.path.basename(filepath)
        backup_path = os.path.join(self.backup_dir, f"{filename}.{timestamp}.backup")
        
        try:
            shutil.copy2(filepath, backup_path)
            print(f"✅ 备份成功: {backup_path}")
            return backup_path
        except Exception as e:
            print(f"❌ 备份失败: {e}")
            return None
    
    def modify_hp_values(self, multiplier=10):
        """修改HP数值 - 将所有HP值乘以倍数"""
        print(f"🔧 修改HP数值 (倍数: {multiplier})")
        
        if not os.path.exists(self.message_file):
            print(f"❌ 文件不存在: {self.message_file}")
            return False
        
        # 备份原文件
        backup_path = self.backup_file(self.message_file)
        if not backup_path:
            return False
        
        try:
            # 读取文件
            with open(self.message_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # 修改HP数值
            def replace_hp(match):
                original_value = int(match.group(1))
                new_value = original_value * multiplier
                return f"HP+{new_value}"
            
            # 应用修改
            modified_content = re.sub(r'HP\+(\d+)', replace_hp, content)
            
            # 写回文件
            with open(self.message_file, 'w', encoding='utf-8') as f:
                f.write(modified_content)
            
            print(f"✅ HP数值修改完成，所有HP值已乘以 {multiplier}")
            return True
            
        except Exception as e:
            print(f"❌ 修改HP失败: {e}")
            # 恢复备份
            if backup_path:
                shutil.copy2(backup_path, self.message_file)
                print("🔄 已恢复原始文件")
            return False
    
    def modify_currency_values(self, multiplier=100):
        """修改货币数值"""
        print(f"🔧 修改货币数值 (倍数: {multiplier})")
        
        if not os.path.exists(self.message_file):
            print(f"❌ 文件不存在: {self.message_file}")
            return False
        
        # 备份原文件
        backup_path = self.backup_file(self.message_file)
        if not backup_path:
            return False
        
        try:
            # 读取文件
            with open(self.message_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # 修改货币数值
            def replace_currency(match):
                original_value = int(match.group(1))
                new_value = original_value * multiplier
                return f"{new_value}个游戏币"
            
            # 应用修改
            modified_content = re.sub(r'(\d+)个游戏币', replace_currency, content)
            modified_content = re.sub(r'(\d+)游戏币', lambda m: f"{int(m.group(1)) * multiplier}游戏币", modified_content)
            
            # 写回文件
            with open(self.message_file, 'w', encoding='utf-8') as f:
                f.write(modified_content)
            
            print(f"✅ 货币数值修改完成，所有货币值已乘以 {multiplier}")
            return True
            
        except Exception as e:
            print(f"❌ 修改货币失败: {e}")
            # 恢复备份
            if backup_path:
                shutil.copy2(backup_path, self.message_file)
                print("🔄 已恢复原始文件")
            return False
    
    def remove_time_restrictions(self):
        """移除时间限制"""
        print("🔧 移除24小时时间限制")
        
        if not os.path.exists(self.message_file):
            print(f"❌ 文件不存在: {self.message_file}")
            return False
        
        # 备份原文件
        backup_path = self.backup_file(self.message_file)
        if not backup_path:
            return False
        
        try:
            # 读取文件
            with open(self.message_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # 移除时间限制
            modifications = [
                (r'24小时只能转让一次', '无限制转让'),
                (r'24小时之后只能转让一次', '无限制转让'),
                (r'24小时', '0小时'),
                (r'制约24小时', '无制约'),
                (r'禁止交易', '允许交易'),
                (r'全天禁止交易', '允许交易')
            ]
            
            modified_content = content
            for pattern, replacement in modifications:
                modified_content = re.sub(pattern, replacement, modified_content)
            
            # 写回文件
            with open(self.message_file, 'w', encoding='utf-8') as f:
                f.write(modified_content)
            
            print("✅ 时间限制移除完成")
            return True
            
        except Exception as e:
            print(f"❌ 移除时间限制失败: {e}")
            # 恢复备份
            if backup_path:
                shutil.copy2(backup_path, self.message_file)
                print("🔄 已恢复原始文件")
            return False
    
    def enhance_success_rates(self):
        """提升成功率到100%"""
        print("🔧 提升所有成功率到100%")
        
        if not os.path.exists(self.message_file):
            print(f"❌ 文件不存在: {self.message_file}")
            return False
        
        # 备份原文件
        backup_path = self.backup_file(self.message_file)
        if not backup_path:
            return False
        
        try:
            # 读取文件
            with open(self.message_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # 修改成功率
            success_patterns = [
                (r'成功率：(\d+)%', '成功率：100%'),
                (r'成功率:(\d+)%', '成功率:100%'),
                (r'(\d+)%成功', '100%成功'),
                (r'成功率(\d+)%', '成功率100%')
            ]
            
            modified_content = content
            for pattern, replacement in success_patterns:
                modified_content = re.sub(pattern, replacement, modified_content)
            
            # 写回文件
            with open(self.message_file, 'w', encoding='utf-8') as f:
                f.write(modified_content)
            
            print("✅ 成功率提升完成，所有成功率已设为100%")
            return True
            
        except Exception as e:
            print(f"❌ 提升成功率失败: {e}")
            # 恢复备份
            if backup_path:
                shutil.copy2(backup_path, self.message_file)
                print("🔄 已恢复原始文件")
            return False
    
    def create_super_items(self):
        """创建超级物品"""
        print("🔧 创建超级物品")
        
        if not os.path.exists(self.message_file):
            print(f"❌ 文件不存在: {self.message_file}")
            return False
        
        # 备份原文件
        backup_path = self.backup_file(self.message_file)
        if not backup_path:
            return False
        
        try:
            # 读取文件
            with open(self.message_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # 添加超级物品
            super_items = """
2	1	99999      超级恢复药水
特殊属性：HP+999999
无限制物品
可无限使用

2	1	99998      超级力量药水  
特殊属性：攻击力+99999
无限制物品
永久效果

2	1	99997      超级防御药水
特殊属性：防御力+99999
无限制物品
永久效果

2	1	99996      超级金币袋
特殊属性：获得999999999游戏币
无限制物品
可重复使用

2	1	99995      超级经验药水
特殊属性：经验值+999999
无限制物品
快速升级
"""
            
            # 在文件末尾添加超级物品
            modified_content = content + super_items
            
            # 写回文件
            with open(self.message_file, 'w', encoding='utf-8') as f:
                f.write(modified_content)
            
            print("✅ 超级物品创建完成")
            return True
            
        except Exception as e:
            print(f"❌ 创建超级物品失败: {e}")
            # 恢复备份
            if backup_path:
                shutil.copy2(backup_path, self.message_file)
                print("🔄 已恢复原始文件")
            return False
    
    def restore_from_backup(self, backup_filename=None):
        """从备份恢复"""
        print("🔄 从备份恢复文件")
        
        if not os.path.exists(self.backup_dir):
            print("❌ 备份目录不存在")
            return False
        
        # 列出所有备份文件
        backup_files = [f for f in os.listdir(self.backup_dir) if f.endswith('.backup')]
        
        if not backup_files:
            print("❌ 没有找到备份文件")
            return False
        
        if backup_filename is None:
            # 使用最新的备份
            backup_files.sort(reverse=True)
            backup_filename = backup_files[0]
        
        backup_path = os.path.join(self.backup_dir, backup_filename)
        
        try:
            shutil.copy2(backup_path, self.message_file)
            print(f"✅ 已从备份恢复: {backup_filename}")
            return True
        except Exception as e:
            print(f"❌ 恢复失败: {e}")
            return False

def main():
    print("=" * 70)
    print("🎮 Uonline 游戏修改器")
    print("🔧 实际修改游戏数据")
    print("=" * 70)
    
    modifier = UonlineModifier()
    
    while True:
        print("\n📋 选择修改选项:")
        print("1. 🎯 修改HP数值 (增强10倍)")
        print("2. 💰 修改货币数值 (增强100倍)")
        print("3. ⏰ 移除时间限制")
        print("4. 📈 提升成功率到100%")
        print("5. 🌟 创建超级物品")
        print("6. 🔄 从备份恢复")
        print("7. 🚀 全部修改")
        print("0. ❌ 退出")
        
        choice = input("\n请选择 (0-7): ").strip()
        
        if choice == '0':
            print("👋 再见！")
            break
        elif choice == '1':
            modifier.modify_hp_values(10)
        elif choice == '2':
            modifier.modify_currency_values(100)
        elif choice == '3':
            modifier.remove_time_restrictions()
        elif choice == '4':
            modifier.enhance_success_rates()
        elif choice == '5':
            modifier.create_super_items()
        elif choice == '6':
            modifier.restore_from_backup()
        elif choice == '7':
            print("🚀 执行全部修改...")
            modifier.modify_hp_values(10)
            modifier.modify_currency_values(100)
            modifier.remove_time_restrictions()
            modifier.enhance_success_rates()
            modifier.create_super_items()
            print("✅ 全部修改完成！")
        else:
            print("❌ 无效选择，请重试")
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    main()
