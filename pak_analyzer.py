#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Uonline PAK文件分析器 - 分析游戏资源文件
发现隐藏的游戏内容和可修改的资源
"""

import os
import struct
import re
from collections import defaultdict

class PAKAnalyzer:
    def __init__(self, game_dir="Uonline"):
        self.game_dir = game_dir
        self.pak_files = []
        self.analysis_results = {}
        
    def find_pak_files(self):
        """查找所有PAK文件"""
        print("🔍 搜索PAK文件...")
        
        for root, dirs, files in os.walk(self.game_dir):
            for file in files:
                if file.lower().endswith('.pak'):
                    full_path = os.path.join(root, file)
                    self.pak_files.append(full_path)
        
        print(f"📊 发现 {len(self.pak_files)} 个PAK文件")
        return self.pak_files
    
    def categorize_pak_files(self):
        """分类PAK文件"""
        print("📂 分类PAK文件...")
        
        categories = {
            "角色/NPC": [],
            "装备": [],
            "地图": [],
            "怪物": [],
            "特效": [],
            "音效": [],
            "界面": [],
            "其他": []
        }
        
        for pak_file in self.pak_files:
            filename = os.path.basename(pak_file).lower()
            
            # 角色/NPC
            if any(keyword in filename for keyword in ['ivan', 'temo', 'keeper', 'novice', 'patroler', 'villager', 'banker', 'gambler']):
                categories["角色/NPC"].append(pak_file)
            # 装备
            elif any(keyword in filename for keyword in ['eqp', 'item', 'weapon', 'armor']):
                categories["装备"].append(pak_file)
            # 地图
            elif filename.startswith('map-'):
                categories["地图"].append(pak_file)
            # 怪物
            elif any(keyword in filename for keyword in ['monster', 'zombie', 'wolf', 'spider', 'angel', 'buffalo', 'fang', 'grizzly']):
                categories["怪物"].append(pak_file)
            # 特效
            elif any(keyword in filename for keyword in ['effect', 'floatsprite']):
                categories["特效"].append(pak_file)
            # 音效
            elif any(keyword in filename for keyword in ['wav', 'sound']):
                categories["音效"].append(pak_file)
            # 界面
            elif any(keyword in filename for keyword in ['icon', 'common', 'etc']):
                categories["界面"].append(pak_file)
            else:
                categories["其他"].append(pak_file)
        
        # 显示分类结果
        for category, files in categories.items():
            if files:
                print(f"  📁 {category}: {len(files)} 个文件")
                for file in files[:3]:  # 显示前3个
                    print(f"    - {os.path.basename(file)}")
                if len(files) > 3:
                    print(f"    ... 还有 {len(files) - 3} 个文件")
        
        return categories
    
    def analyze_pak_structure(self, pak_file):
        """分析PAK文件结构"""
        try:
            with open(pak_file, 'rb') as f:
                # 读取文件头
                header = f.read(16)
                if len(header) < 16:
                    return None
                
                # 尝试解析PAK文件格式
                file_size = os.path.getsize(pak_file)
                
                # 查找可能的文件签名
                f.seek(0)
                first_bytes = f.read(64)
                
                analysis = {
                    'file_size': file_size,
                    'header_hex': first_bytes.hex()[:32],
                    'possible_format': self.guess_pak_format(first_bytes),
                    'text_content': self.extract_text_content(pak_file)
                }
                
                return analysis
                
        except Exception as e:
            return {'error': str(e)}
    
    def guess_pak_format(self, header_bytes):
        """猜测PAK文件格式"""
        header_hex = header_bytes.hex()
        
        # 常见的文件格式签名
        if header_hex.startswith('504b0304'):  # ZIP
            return "ZIP压缩格式"
        elif header_hex.startswith('1f8b'):  # GZIP
            return "GZIP压缩格式"
        elif header_hex.startswith('425a68'):  # BZIP2
            return "BZIP2压缩格式"
        elif header_hex.startswith('377abcaf271c'):  # 7Z
            return "7Z压缩格式"
        elif b'PAK' in header_bytes[:16]:
            return "自定义PAK格式"
        elif header_bytes[:4] == b'\x00\x00\x00\x00':
            return "可能是索引文件"
        else:
            return "未知格式"
    
    def extract_text_content(self, pak_file):
        """提取PAK文件中的文本内容"""
        try:
            with open(pak_file, 'rb') as f:
                content = f.read()
            
            # 查找ASCII文本
            text_matches = re.findall(b'[a-zA-Z0-9_\-\.]{4,}', content)
            
            # 转换为字符串并去重
            texts = list(set([match.decode('ascii', errors='ignore') for match in text_matches]))
            
            # 过滤有意义的文本
            meaningful_texts = []
            for text in texts:
                if len(text) >= 4 and not text.isdigit():
                    meaningful_texts.append(text)
            
            return meaningful_texts[:20]  # 返回前20个
            
        except Exception as e:
            return []
    
    def find_interesting_files(self):
        """查找有趣的文件"""
        print("🎯 查找有趣的文件...")
        
        interesting_patterns = {
            "Boss怪物": r'boss|Boss|BOSS',
            "特殊装备": r'special|Special|rare|Rare|unique|Unique',
            "隐藏地图": r'hidden|Hidden|secret|Secret',
            "测试内容": r'test|Test|debug|Debug|dev|Dev',
            "GM内容": r'gm|GM|admin|Admin|master|Master',
            "事件内容": r'event|Event|quest|Quest'
        }
        
        interesting_files = defaultdict(list)
        
        for pak_file in self.pak_files:
            filename = os.path.basename(pak_file)
            
            for category, pattern in interesting_patterns.items():
                if re.search(pattern, filename, re.IGNORECASE):
                    interesting_files[category].append(pak_file)
        
        # 显示结果
        for category, files in interesting_files.items():
            if files:
                print(f"  🎯 {category}: {len(files)} 个文件")
                for file in files:
                    print(f"    - {os.path.basename(file)}")
        
        return interesting_files
    
    def analyze_equipment_files(self):
        """分析装备文件"""
        print("⚔️ 分析装备文件...")
        
        equipment_files = [f for f in self.pak_files if 'eqp' in os.path.basename(f).lower() or 'item' in os.path.basename(f).lower()]
        
        equipment_analysis = {}
        
        for eqp_file in equipment_files:
            filename = os.path.basename(eqp_file)
            analysis = self.analyze_pak_structure(eqp_file)
            
            if analysis and 'text_content' in analysis:
                # 查找装备相关的文本
                equipment_texts = []
                for text in analysis['text_content']:
                    if any(keyword in text.lower() for keyword in ['sword', 'armor', 'weapon', 'shield', 'helmet', 'boots']):
                        equipment_texts.append(text)
                
                if equipment_texts:
                    equipment_analysis[filename] = {
                        'size': analysis['file_size'],
                        'format': analysis['possible_format'],
                        'equipment_items': equipment_texts
                    }
        
        # 显示结果
        for filename, data in equipment_analysis.items():
            print(f"  📦 {filename}:")
            print(f"    大小: {data['size']:,} 字节")
            print(f"    格式: {data['format']}")
            if data['equipment_items']:
                print(f"    装备项目: {', '.join(data['equipment_items'][:5])}")
        
        return equipment_analysis
    
    def analyze_monster_files(self):
        """分析怪物文件"""
        print("👹 分析怪物文件...")
        
        monster_keywords = ['zombie', 'wolf', 'spider', 'angel', 'buffalo', 'fang', 'grizzly', 'boss', 'monster']
        monster_files = []
        
        for pak_file in self.pak_files:
            filename = os.path.basename(pak_file).lower()
            if any(keyword in filename for keyword in monster_keywords):
                monster_files.append(pak_file)
        
        monster_analysis = {}
        boss_monsters = []
        
        for monster_file in monster_files:
            filename = os.path.basename(monster_file)
            analysis = self.analyze_pak_structure(monster_file)
            
            if 'boss' in filename.lower():
                boss_monsters.append(filename)
            
            if analysis:
                monster_analysis[filename] = {
                    'size': analysis['file_size'],
                    'format': analysis['possible_format']
                }
        
        print(f"  📊 总怪物文件: {len(monster_files)}")
        print(f"  👑 Boss怪物: {len(boss_monsters)}")
        
        if boss_monsters:
            print("  🎯 Boss怪物列表:")
            for boss in boss_monsters:
                print(f"    - {boss}")
        
        return monster_analysis
    
    def generate_modification_suggestions(self):
        """生成修改建议"""
        print("💡 生成修改建议...")
        
        suggestions = [
            "🎯 装备文件修改:",
            "  - 提取eqp*.pak文件中的装备数据",
            "  - 修改装备属性值",
            "  - 添加新的装备类型",
            "",
            "👹 怪物文件修改:",
            "  - 修改怪物血量和攻击力",
            "  - 调整怪物掉落率",
            "  - 创建自定义Boss",
            "",
            "🗺️ 地图文件修改:",
            "  - 解锁隐藏地图区域",
            "  - 修改地图传送点",
            "  - 添加新的NPC位置",
            "",
            "🎮 界面文件修改:",
            "  - 修改游戏界面元素",
            "  - 添加自定义图标",
            "  - 修改菜单选项",
            "",
            "🔧 推荐工具:",
            "  - PAK文件提取器",
            "  - 十六进制编辑器",
            "  - 图像编辑软件",
            "  - 文本编辑器"
        ]
        
        for suggestion in suggestions:
            print(f"  {suggestion}")
        
        return suggestions

def main():
    print("=" * 70)
    print("🎮 Uonline PAK文件分析器")
    print("🔍 深度分析游戏资源文件")
    print("=" * 70)
    
    analyzer = PAKAnalyzer()
    
    # 1. 查找PAK文件
    print("\n📂 第一步: 查找PAK文件")
    print("-" * 50)
    pak_files = analyzer.find_pak_files()
    
    # 2. 分类PAK文件
    print("\n📊 第二步: 分类PAK文件")
    print("-" * 50)
    categories = analyzer.categorize_pak_files()
    
    # 3. 查找有趣的文件
    print("\n🎯 第三步: 查找有趣的文件")
    print("-" * 50)
    interesting = analyzer.find_interesting_files()
    
    # 4. 分析装备文件
    print("\n⚔️ 第四步: 分析装备文件")
    print("-" * 50)
    equipment = analyzer.analyze_equipment_files()
    
    # 5. 分析怪物文件
    print("\n👹 第五步: 分析怪物文件")
    print("-" * 50)
    monsters = analyzer.analyze_monster_files()
    
    # 6. 生成修改建议
    print("\n💡 第六步: 修改建议")
    print("-" * 50)
    suggestions = analyzer.generate_modification_suggestions()
    
    # 总结
    print("\n" + "=" * 70)
    print("📊 分析总结")
    print("=" * 70)
    
    print(f"\n✅ 发现统计:")
    print(f"  - 总PAK文件: {len(pak_files)}")
    print(f"  - 装备文件: {len([f for f in pak_files if 'eqp' in os.path.basename(f).lower() or 'item' in os.path.basename(f).lower()])}")
    print(f"  - 地图文件: {len([f for f in pak_files if 'map-' in os.path.basename(f).lower()])}")
    print(f"  - 怪物文件: {len([f for f in pak_files if any(k in os.path.basename(f).lower() for k in ['zombie', 'wolf', 'spider', 'monster'])])}")
    
    print(f"\n🎯 重点发现:")
    print(f"  🏆 Boss怪物文件可以修改难度")
    print(f"  ⚔️ 装备文件可以修改属性")
    print(f"  🗺️ 地图文件可以解锁区域")
    print(f"  🎨 界面文件可以自定义外观")
    
    print(f"\n🚀 下一步建议:")
    print(f"  1. 🔧 创建PAK文件提取工具")
    print(f"  2. 📝 分析具体文件格式")
    print(f"  3. 🎮 修改游戏数值")
    print(f"  4. 🧪 测试修改效果")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
