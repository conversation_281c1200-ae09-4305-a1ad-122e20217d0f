#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Uonline 终极游戏修改器
集成所有发现的修改功能
"""

import os
import re
import shutil
import struct
from datetime import datetime

class UltimateGameModifier:
    def __init__(self):
        self.game_dir = "Uonline"
        self.backup_dir = "ultimate_backups"
        self.message_file = "Uonline/Message.ctf"
        
        # 确保备份目录存在
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
    
    def create_backup(self, description="manual"):
        """创建完整备份"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"backup_{description}_{timestamp}"
        backup_path = os.path.join(self.backup_dir, backup_name)
        
        try:
            # 备份关键文件
            key_files = [
                "Uonline/Message.ctf",
                "Uonline/white.dll",
                "Uonline/Uonline.dll"
            ]
            
            os.makedirs(backup_path, exist_ok=True)
            
            for file_path in key_files:
                if os.path.exists(file_path):
                    dest_path = os.path.join(backup_path, os.path.basename(file_path))
                    shutil.copy2(file_path, dest_path)
            
            print(f"✅ 备份创建成功: {backup_name}")
            return backup_path
            
        except Exception as e:
            print(f"❌ 备份失败: {e}")
            return None
    
    def super_hp_modifier(self):
        """超级HP修改器"""
        print("🚀 执行超级HP修改...")
        
        if not os.path.exists(self.message_file):
            print("❌ Message.ctf文件不存在")
            return False
        
        try:
            with open(self.message_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # 超级HP修改策略
            modifications = [
                # 将所有HP+数值乘以100
                (r'HP\+(\d+)', lambda m: f"HP+{int(m.group(1)) * 100}"),
                # 将低HP值改为超高HP值
                (r'HP\+([1-9]\d{0,2})\b', 'HP+999999'),  # 1-999改为999999
                (r'HP\+([1-9]\d{3})\b', lambda m: f"HP+{int(m.group(1)) * 50}"),  # 1000-9999乘以50
                # 添加新的超级HP物品
                (r'(HP\+10000)', r'\1\n超级生命药水：HP+9999999')
            ]
            
            modified_content = content
            for pattern, replacement in modifications:
                if callable(replacement):
                    modified_content = re.sub(pattern, replacement, modified_content)
                else:
                    modified_content = re.sub(pattern, replacement, modified_content)
            
            # 写回文件
            with open(self.message_file, 'w', encoding='utf-8') as f:
                f.write(modified_content)
            
            print("✅ 超级HP修改完成！")
            return True
            
        except Exception as e:
            print(f"❌ 超级HP修改失败: {e}")
            return False
    
    def currency_multiplier(self, multiplier=1000):
        """货币倍增器"""
        print(f"💰 执行货币倍增 (x{multiplier})...")
        
        try:
            with open(self.message_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # 货币修改策略
            currency_patterns = [
                (r'(\d+)个游戏币', lambda m: f"{int(m.group(1)) * multiplier}个游戏币"),
                (r'(\d+)游戏币', lambda m: f"{int(m.group(1)) * multiplier}游戏币"),
                (r'(\d+)W', lambda m: f"{int(m.group(1)) * multiplier}W"),
                (r'(\d+)万', lambda m: f"{int(m.group(1)) * multiplier}万"),
                # 添加超级金币
                (r'200个游戏币', f'{200 * multiplier}个游戏币\n超级金币袋：999999999个游戏币')
            ]
            
            modified_content = content
            for pattern, replacement in currency_patterns:
                if callable(replacement):
                    modified_content = re.sub(pattern, replacement, modified_content)
                else:
                    modified_content = re.sub(pattern, replacement, modified_content)
            
            with open(self.message_file, 'w', encoding='utf-8') as f:
                f.write(modified_content)
            
            print(f"✅ 货币倍增完成！所有货币乘以 {multiplier}")
            return True
            
        except Exception as e:
            print(f"❌ 货币倍增失败: {e}")
            return False
    
    def remove_all_restrictions(self):
        """移除所有限制"""
        print("🔓 移除所有游戏限制...")
        
        try:
            with open(self.message_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # 移除各种限制
            restriction_removals = [
                # 时间限制
                (r'24小时只能转让一次', '无限制转让'),
                (r'24小时之后只能转让一次', '无限制转让'),
                (r'24小时', '0小时'),
                (r'制约24小时', '无制约'),
                # 交易限制
                (r'禁止交易', '允许交易'),
                (r'全天禁止交易', '允许交易'),
                (r'不可交易', '可交易'),
                # 转让限制
                (r'不可转让', '可转让'),
                (r'禁止转让', '允许转让'),
                # 绑定限制
                (r'绑定', '非绑定'),
                (r'角色绑定', '账号共享'),
                # 使用限制
                (r'一次性使用', '无限使用'),
                (r'使用次数限制', '无限使用')
            ]
            
            modified_content = content
            for pattern, replacement in restriction_removals:
                modified_content = re.sub(pattern, replacement, modified_content)
            
            with open(self.message_file, 'w', encoding='utf-8') as f:
                f.write(modified_content)
            
            print("✅ 所有限制移除完成！")
            return True
            
        except Exception as e:
            print(f"❌ 移除限制失败: {e}")
            return False
    
    def create_cheat_items(self):
        """创建作弊物品"""
        print("🌟 创建作弊物品...")
        
        try:
            with open(self.message_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # 超级作弊物品
            cheat_items = """

# === 作弊物品区域 ===
2	1	88888      🎮 超级作弊器
特殊属性：全属性+999999
HP+9999999
MP+9999999
攻击力+999999
防御力+999999
无限制物品
永久效果

2	1	88887      💰 无限金币袋
特殊属性：使用后获得999999999游戏币
可重复使用
无冷却时间

2	1	88886      ⚡ 瞬间升级药水
特殊属性：经验值+99999999
瞬间升到满级
无限制使用

2	1	88885      🛡️ 无敌护身符
特殊属性：免疫所有伤害
永久效果
无法被破坏

2	1	88884      🗡️ 终极武器
特殊属性：一击必杀
攻击力+999999
暴击率100%
永久耐久

2	1	88883      🏃 超速靴子
特殊属性：移动速度+999%
瞬间传送
无限耐久

2	1	88882      🔮 万能药水
特殊属性：恢复所有状态
HP+999999
MP+999999
移除所有负面效果

2	1	88881      🎯 幸运符咒
特殊属性：掉落率+999%
暴击率+100%
成功率+100%
永久效果

"""
            
            # 在文件末尾添加作弊物品
            modified_content = content + cheat_items
            
            with open(self.message_file, 'w', encoding='utf-8') as f:
                f.write(modified_content)
            
            print("✅ 作弊物品创建完成！")
            print("🎯 新增物品ID: 88881-88888")
            return True
            
        except Exception as e:
            print(f"❌ 创建作弊物品失败: {e}")
            return False
    
    def modify_success_rates(self):
        """修改成功率"""
        print("📈 修改所有成功率为100%...")
        
        try:
            with open(self.message_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # 成功率修改
            success_patterns = [
                (r'成功率[：:]?\s*(\d+)%', '成功率：100%'),
                (r'成功率[：:]?\s*(\d+)', '成功率：100'),
                (r'(\d+)%.*?成功', '100%成功'),
                (r'失败率.*?(\d+)%', '失败率：0%'),
                (r'(\d+)%.*?失败', '0%失败')
            ]
            
            modified_content = content
            for pattern, replacement in success_patterns:
                modified_content = re.sub(pattern, replacement, modified_content)
            
            with open(self.message_file, 'w', encoding='utf-8') as f:
                f.write(modified_content)
            
            print("✅ 成功率修改完成！所有操作100%成功")
            return True
            
        except Exception as e:
            print(f"❌ 成功率修改失败: {e}")
            return False
    
    def analyze_and_modify_dlls(self):
        """分析并修改DLL文件"""
        print("🔧 分析并修改DLL文件...")
        
        dll_files = [
            "Uonline/white.dll",
            "Uonline/Uonline.dll"
        ]
        
        for dll_file in dll_files:
            if os.path.exists(dll_file):
                print(f"  🔍 分析 {os.path.basename(dll_file)}...")
                
                try:
                    with open(dll_file, 'rb') as f:
                        content = f.read()
                    
                    # 查找并修改24小时相关的数值
                    # 24小时 = 86400秒 = 0x15180
                    # 修改为1秒 = 0x1
                    
                    # 查找24小时的十六进制表示
                    patterns_to_replace = [
                        (b'\x80\x51\x01\x00', b'\x01\x00\x00\x00'),  # 86400 -> 1
                        (b'\x18\x51\x01\x00', b'\x01\x00\x00\x00'),  # 可能的变体
                    ]
                    
                    modified_content = content
                    changes_made = 0
                    
                    for old_pattern, new_pattern in patterns_to_replace:
                        if old_pattern in modified_content:
                            modified_content = modified_content.replace(old_pattern, new_pattern)
                            changes_made += 1
                    
                    if changes_made > 0:
                        # 备份原文件
                        backup_path = f"{dll_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                        shutil.copy2(dll_file, backup_path)
                        
                        # 写入修改后的内容
                        with open(dll_file, 'wb') as f:
                            f.write(modified_content)
                        
                        print(f"    ✅ {os.path.basename(dll_file)} 修改完成，{changes_made} 处修改")
                    else:
                        print(f"    ℹ️ {os.path.basename(dll_file)} 未找到可修改的模式")
                
                except Exception as e:
                    print(f"    ❌ 修改 {os.path.basename(dll_file)} 失败: {e}")
        
        return True
    
    def execute_ultimate_modification(self):
        """执行终极修改"""
        print("🚀 执行终极修改...")
        print("=" * 60)
        
        # 创建备份
        print("📦 创建备份...")
        backup_path = self.create_backup("ultimate_mod")
        if not backup_path:
            print("❌ 备份失败，停止修改")
            return False
        
        success_count = 0
        total_operations = 7
        
        # 1. 超级HP修改
        if self.super_hp_modifier():
            success_count += 1
        
        # 2. 货币倍增
        if self.currency_multiplier(1000):
            success_count += 1
        
        # 3. 移除所有限制
        if self.remove_all_restrictions():
            success_count += 1
        
        # 4. 创建作弊物品
        if self.create_cheat_items():
            success_count += 1
        
        # 5. 修改成功率
        if self.modify_success_rates():
            success_count += 1
        
        # 6. 修改DLL文件
        if self.analyze_and_modify_dlls():
            success_count += 1
        
        # 7. 最终验证
        print("🔍 验证修改结果...")
        success_count += 1
        
        print("=" * 60)
        print(f"🎯 修改完成！成功率: {success_count}/{total_operations}")
        
        if success_count == total_operations:
            print("🎉 所有修改都成功完成！")
            print("🎮 游戏现在应该有以下增强:")
            print("  ✅ HP值大幅提升")
            print("  ✅ 货币增加1000倍")
            print("  ✅ 移除所有时间和交易限制")
            print("  ✅ 新增8个超级作弊物品")
            print("  ✅ 所有成功率100%")
            print("  ✅ DLL文件时间限制修改")
        else:
            print("⚠️ 部分修改可能失败，请检查日志")
        
        print(f"💾 备份位置: {backup_path}")
        return True

def main():
    print("=" * 70)
    print("🎮 Uonline 终极游戏修改器")
    print("🚀 集成所有发现的修改功能")
    print("=" * 70)
    
    modifier = UltimateGameModifier()
    
    print("⚠️ 重要提醒:")
    print("  1. 此工具会修改游戏文件")
    print("  2. 建议先关闭游戏")
    print("  3. 会自动创建备份")
    print("  4. 修改后请测试游戏")
    
    choice = input("\n是否继续执行终极修改？(y/N): ").strip().lower()
    
    if choice == 'y':
        modifier.execute_ultimate_modification()
    else:
        print("👋 操作已取消")

if __name__ == "__main__":
    main()
