#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
二进制文件深度分析工具
尝试更深入的逆向分析
"""

import struct
import re
import os
from collections import defaultdict

def analyze_pe_structure(filename):
    """分析PE文件结构"""
    try:
        with open(filename, 'rb') as f:
            data = f.read()
        
        print(f"🔍 分析 {filename} PE结构...")
        
        # 检查DOS头
        if data[:2] != b'MZ':
            print("❌ 不是有效的PE文件")
            return False
        
        # 获取PE头偏移
        pe_offset = struct.unpack('<I', data[0x3C:0x40])[0]
        
        if data[pe_offset:pe_offset+4] != b'PE\x00\x00':
            print("❌ 无效的PE签名")
            return False
        
        print("✅ 有效的PE文件")
        
        # 分析COFF头
        coff_header = data[pe_offset+4:pe_offset+24]
        machine, num_sections, timestamp = struct.unpack('<HHI', coff_header[:8])
        
        print(f"📋 机器类型: 0x{machine:04X}")
        print(f"📋 节数量: {num_sections}")
        print(f"📋 时间戳: {timestamp}")
        
        # 分析节表
        optional_header_size = struct.unpack('<H', coff_header[16:18])[0]
        section_table_offset = pe_offset + 24 + optional_header_size
        
        print(f"\n📂 节信息:")
        for i in range(num_sections):
            section_offset = section_table_offset + i * 40
            section_data = data[section_offset:section_offset+40]
            
            name = section_data[:8].rstrip(b'\x00').decode('ascii', errors='ignore')
            virtual_size = struct.unpack('<I', section_data[8:12])[0]
            virtual_address = struct.unpack('<I', section_data[12:16])[0]
            raw_size = struct.unpack('<I', section_data[16:20])[0]
            raw_address = struct.unpack('<I', section_data[20:24])[0]
            
            print(f"  {name:8} VA:0x{virtual_address:08X} Size:0x{virtual_size:08X} Raw:0x{raw_address:08X}")
        
        return True
        
    except Exception as e:
        print(f"❌ PE分析失败: {e}")
        return False

def find_api_calls(filename):
    """查找API调用"""
    try:
        with open(filename, 'rb') as f:
            data = f.read()
        
        print(f"\n🔍 查找 {filename} 中的API调用...")
        
        # 常见的Windows API
        apis = [
            b'GetSystemTime', b'GetLocalTime', b'timeGetTime', b'GetTickCount',
            b'CreateFile', b'ReadFile', b'WriteFile', b'CloseHandle',
            b'send', b'recv', b'WSASend', b'WSARecv', b'connect', b'socket',
            b'LoadLibrary', b'GetProcAddress', b'VirtualAlloc', b'VirtualProtect',
            b'CreateThread', b'CreateProcess', b'OpenProcess', b'ReadProcessMemory'
        ]
        
        found_apis = []
        for api in apis:
            matches = list(re.finditer(re.escape(api), data))
            if matches:
                found_apis.append((api.decode('ascii'), len(matches), [hex(m.start()) for m in matches[:3]]))
        
        if found_apis:
            print("✅ 发现的API调用:")
            for api, count, offsets in found_apis:
                print(f"  {api:20} 出现{count}次 位置: {', '.join(offsets)}")
        else:
            print("❌ 未找到明显的API调用")
        
        return found_apis
        
    except Exception as e:
        print(f"❌ API分析失败: {e}")
        return []

def find_time_constants(filename):
    """查找时间相关常量"""
    try:
        with open(filename, 'rb') as f:
            data = f.read()
        
        print(f"\n🕐 查找 {filename} 中的时间常量...")
        
        # 时间常量 (秒、毫秒)
        time_constants = {
            86400: "24小时(秒)",
            86400000: "24小时(毫秒)", 
            3600: "1小时(秒)",
            3600000: "1小时(毫秒)",
            1440: "24小时(分钟)",
            24: "24小时",
            1000: "1秒(毫秒)"
        }
        
        found_constants = []
        for constant, desc in time_constants.items():
            # 小端序
            little_endian = struct.pack('<I', constant)
            matches = list(re.finditer(re.escape(little_endian), data))
            if matches:
                found_constants.append((desc, "小端序", len(matches), [hex(m.start()) for m in matches[:3]]))
            
            # 大端序
            big_endian = struct.pack('>I', constant)
            matches = list(re.finditer(re.escape(big_endian), data))
            if matches:
                found_constants.append((desc, "大端序", len(matches), [hex(m.start()) for m in matches[:3]]))
        
        if found_constants:
            print("✅ 发现的时间常量:")
            for desc, endian, count, offsets in found_constants:
                print(f"  {desc:20} ({endian}) 出现{count}次 位置: {', '.join(offsets)}")
        else:
            print("❌ 未找到明显的时间常量")
        
        return found_constants
        
    except Exception as e:
        print(f"❌ 时间常量分析失败: {e}")
        return []

def analyze_strings(filename, min_length=4):
    """提取可读字符串"""
    try:
        with open(filename, 'rb') as f:
            data = f.read()
        
        print(f"\n📝 提取 {filename} 中的字符串 (最小长度{min_length})...")
        
        # ASCII字符串
        ascii_pattern = rb'[!-~]{' + str(min_length).encode() + rb',}'
        ascii_strings = re.findall(ascii_pattern, data)
        
        # Unicode字符串 (UTF-16LE)
        unicode_pattern = rb'(?:[!-~]\x00){' + str(min_length).encode() + rb',}'
        unicode_matches = re.findall(unicode_pattern, data)
        unicode_strings = [s.decode('utf-16le', errors='ignore') for s in unicode_matches]
        
        # 过滤有趣的字符串
        interesting_keywords = ['time', 'hour', '小时', 'limit', 'restrict', 'server', 'client', 'check', 'valid']
        
        interesting_strings = []
        for s in ascii_strings:
            s_str = s.decode('ascii', errors='ignore')
            if any(keyword in s_str.lower() for keyword in interesting_keywords):
                interesting_strings.append(s_str)
        
        for s in unicode_strings:
            if any(keyword in s.lower() for keyword in interesting_keywords):
                interesting_strings.append(s)
        
        if interesting_strings:
            print("✅ 发现有趣的字符串:")
            for s in interesting_strings[:20]:  # 显示前20个
                print(f"  {s}")
        else:
            print("❌ 未找到相关字符串")
        
        return interesting_strings
        
    except Exception as e:
        print(f"❌ 字符串分析失败: {e}")
        return []

def analyze_network_patterns(filename):
    """分析网络相关模式"""
    try:
        with open(filename, 'rb') as f:
            data = f.read()
        
        print(f"\n🌐 分析 {filename} 网络模式...")
        
        # IP地址模式
        ip_pattern = rb'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}'
        ip_matches = re.findall(ip_pattern, data)
        
        # 端口号模式 (常见端口范围)
        port_patterns = [
            struct.pack('<H', 4001),  # 游戏端口
            struct.pack('>H', 4001),
            struct.pack('<H', 80),    # HTTP
            struct.pack('<H', 443),   # HTTPS
        ]
        
        found_ips = []
        for ip in ip_matches:
            try:
                ip_str = ip.decode('ascii')
                found_ips.append(ip_str)
            except:
                pass
        
        found_ports = []
        for port_bytes in port_patterns:
            matches = list(re.finditer(re.escape(port_bytes), data))
            if matches:
                port = struct.unpack('<H' if len(port_bytes) == 2 else '>H', port_bytes)[0]
                found_ports.append((port, len(matches)))
        
        if found_ips:
            print("✅ 发现IP地址:")
            for ip in set(found_ips):
                print(f"  {ip}")
        
        if found_ports:
            print("✅ 发现端口:")
            for port, count in found_ports:
                print(f"  {port} (出现{count}次)")
        
        if not found_ips and not found_ports:
            print("❌ 未找到明显的网络模式")
        
        return found_ips, found_ports
        
    except Exception as e:
        print(f"❌ 网络分析失败: {e}")
        return [], []

def main():
    print("=" * 60)
    print("🔬 深度二进制分析工具")
    print("=" * 60)
    print()
    
    files_to_analyze = ['Prmain.exe', 'Uonline.dll']
    
    for filename in files_to_analyze:
        if os.path.exists(filename):
            print(f"\n{'='*20} 分析 {filename} {'='*20}")
            
            # PE结构分析
            analyze_pe_structure(filename)
            
            # API调用分析
            find_api_calls(filename)
            
            # 时间常量分析
            find_time_constants(filename)
            
            # 字符串分析
            analyze_strings(filename)
            
            # 网络模式分析
            analyze_network_patterns(filename)
            
        else:
            print(f"❌ 文件不存在: {filename}")
    
    print("\n" + "="*60)
    print("🎯 分析完成")
    print("="*60)

if __name__ == "__main__":
    try:
        main()
        input("\n按回车键退出...")
    except Exception as e:
        print(f"❌ 分析出错: {e}")
        input("按回车键退出...")
