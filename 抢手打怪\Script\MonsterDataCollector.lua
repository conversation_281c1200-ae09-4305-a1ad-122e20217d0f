-- MonsterDataCollector.lua
-- 怪物数据收集器

local MonsterDataCollector = {
    monsters_data = {},  -- 存储所有怪物数据
    current_time = 0,    -- 当前时间戳
    log_file = "monster_data.txt",  -- 日志文件
    analysis_file = "monster_analysis.txt", -- 分析结果文件
    is_collecting = true,  -- 是否正在收集数据
    collection_interval = 0.5,  -- 收集间隔(秒)
    last_collection_time = 0,  -- 上次收集时间
    last_analysis_time = 0,    -- 上次分析时间
    analysis_interval = 3600,  -- 分析间隔(秒)
    max_records_per_monster = 1000,  -- 每个怪物最大记录数
    debug_level = 1  -- 调试输出级别: 0=无, 1=基本, 2=详细
}

-- 初始化数据收集器
function MonsterDataCollector:init()
    -- 创建或清空日志文件
    local file = io.open(self.log_file, "w")
    if file then
        file:write("数据收集开始时间: " .. os.date("%Y-%m-%d %H:%M:%S") .. "\n")
        file:write("格式: 时间戳,怪物ID,怪物名称,X坐标,Y坐标,Z坐标,血量,最大血量,状态,目标ID\n")
        file:close()
    else
        _Log("警告: 无法创建数据日志文件")
    end
    
    self.current_time = os.time()
    self.last_collection_time = self.current_time
    self.last_analysis_time = self.current_time
    
    _Log("怪物数据收集器已初始化")
    return self
end

-- 确定怪物状态
function MonsterDataCollector:determine_state(monster, role)
    -- 简单实现: 根据怪物与角色的距离判断状态
    local distance = _取坐标距离(role.x, role.y, monster.x, monster.y)
    
    if distance < 5 then
        return "攻击"
    elseif distance < 20 then
        return "追击"
    else
        return "巡逻"
    end
end

-- 确定怪物目标
function MonsterDataCollector:determine_target(monster, role_id)
    -- 简单实现: 假设怪物目标是角色
    -- 实际游戏中可能需要更复杂的逻辑
    return role_id
end

-- 记录数据到日志
function MonsterDataCollector:log_record(record)
    local file = io.open(self.log_file, "a")
    if file then
        file:write(string.format("%d,%08X,%s,%.2f,%.2f,%.2f,%d,%d,%s,%08X\n",
            record.time, record.id, record.name, 
            record.x, record.y, record.z,
            record.hp, record.hp_max, record.state, record.target_id))
        file:close()
    end
end

-- 收集怪物数据
function MonsterDataCollector:collect()
    -- 检查是否应该收集数据
    self.current_time = os.time()
    if self.current_time - self.last_collection_time < self.collection_interval then
        return
    end
    self.last_collection_time = self.current_time
    
    -- 获取周围怪物信息
    local monsters = _取周围怪物信息()
    if not monsters then return end
    
    -- 获取角色信息(用于记录怪物目标)
    local role = _取人物信息()
    if not role or not role.x then return end
    
    local role_id = role.Id or 0
    
    -- 收集每个怪物的数据
    for _, monster in ipairs(monsters) do
        -- 更新怪物属性(获取更详细信息)
        local m_detail = _更新怪物属性(monster)
        if m_detail then
            -- 创建数据记录
            local record = {
                time = self.current_time,
                id = monster.Id,
                name = monster.Name,
                x = monster.x,
                y = monster.y,
                z = monster.z or 0,
                hp = m_detail.Hp,
                hp_max = m_detail.HpM,
                state = self:determine_state(monster, role),
                target_id = self:determine_target(monster, role_id)
            }
            
            -- 存储数据
            if not self.monsters_data[monster.Id] then
                self.monsters_data[monster.Id] = {
                    name = monster.Name,
                    records = {}
                }
            end
            
            -- 限制每个怪物的记录数量
            local records = self.monsters_data[monster.Id].records
            if #records >= self.max_records_per_monster then
                table.remove(records, 1)  -- 移除最旧的记录
            end
            
            -- 添加新记录
            table.insert(records, record)
            
            -- 写入日志文件
            self:log_record(record)
            
            if self.debug_level >= 2 then
                _Log(string.format("收集怪物数据: %s (ID:%08X) HP:%d/%d 状态:%s", 
                    monster.Name, monster.Id, m_detail.Hp, m_detail.HpM, record.state))
            end
        end
    end
    
    -- 检查是否应该分析数据
    if self.current_time - self.last_analysis_time >= self.analysis_interval then
        self:analyze()
        self.last_analysis_time = self.current_time
    end
end

-- 分析怪物数据
function MonsterDataCollector:analyze()
    if self.debug_level >= 1 then
        _Log("开始分析怪物数据...")
    end
    
    local results = {}
    
    -- 遍历所有怪物数据
    for id, data in pairs(self.monsters_data) do
        local records = data.records
        if #records > 10 then  -- 只分析有足够数据的怪物
            local analysis = {
                name = data.name,
                id = id,
                count = #records,
                movement_patterns = {},
                attack_range = 0,
                aggro_range = 0,
                avg_speed = 0,
                behavior_notes = ""
            }
            
            -- 计算移动模式和速度
            local total_speed = 0
            local speed_samples = 0
            local prev_record = records[1]
            
            for i = 2, #records do
                local curr_record = records[i]
                local time_diff = curr_record.time - prev_record.time
                
                if time_diff > 0 then
                    local dist = _取坐标距离(prev_record.x, prev_record.y, curr_record.x, curr_record.y)
                    local speed = dist / time_diff
                    
                    if speed > 0 then
                        total_speed = total_speed + speed
                        speed_samples = speed_samples + 1
                    end
                    
                    -- 记录状态变化
                    if prev_record.state ~= curr_record.state then
                        table.insert(analysis.movement_patterns, {
                            from_state = prev_record.state,
                            to_state = curr_record.state,
                            time = curr_record.time,
                            distance = dist
                        })
                    end
                end
                
                prev_record = curr_record
            end
            
            -- 计算平均速度
            if speed_samples > 0 then
                analysis.avg_speed = total_speed / speed_samples
            end
            
            -- 估计攻击范围和仇恨范围
            for _, pattern in ipairs(analysis.movement_patterns) do
                if pattern.from_state == "巡逻" and pattern.to_state == "追击" then
                    if pattern.distance > analysis.aggro_range then
                        analysis.aggro_range = pattern.distance
                    end
                elseif pattern.from_state == "追击" and pattern.to_state == "攻击" then
                    if pattern.distance > analysis.attack_range then
                        analysis.attack_range = pattern.distance
                    end
                end
            end
            
            -- 添加行为分析
            if #analysis.movement_patterns > 0 then
                analysis.behavior_notes = string.format(
                    "怪物在%.2f单位距离开始追击，在%.2f单位距离开始攻击。平均移动速度为%.2f单位/秒。",
                    analysis.aggro_range, analysis.attack_range, analysis.avg_speed
                )
            else
                analysis.behavior_notes = "数据不足，无法分析行为模式。"
            end
            
            -- 添加到结果
            results[id] = analysis
        end
    end
    
    -- 保存分析结果
    self:save_analysis(results)
    
    return results
end

-- 保存分析结果
function MonsterDataCollector:save_analysis(results)
    local file = io.open(self.analysis_file, "w")
    if file then
        file:write("怪物行为分析结果 - " .. os.date("%Y-%m-%d %H:%M:%S") .. "\n\n")
        
        for id, analysis in pairs(results) do
            file:write(string.format("怪物: %s (ID: %08X)\n", analysis.name, id))
            file:write(string.format("记录数量: %d\n", analysis.count))
            file:write(string.format("平均移动速度: %.2f 单位/秒\n", analysis.avg_speed))
            file:write(string.format("估计仇恨范围: %.2f 单位\n", analysis.aggro_range))
            file:write(string.format("估计攻击范围: %.2f 单位\n", analysis.attack_range))
            file:write(string.format("行为模式变化: %d 次\n", #analysis.movement_patterns))
            file:write(string.format("行为分析: %s\n", analysis.behavior_notes))
            
            if #analysis.movement_patterns > 0 then
                file:write("状态变化详情:\n")
                for i, pattern in ipairs(analysis.movement_patterns) do
                    file:write(string.format("  %d. %s -> %s (时间: %s, 距离: %.2f)\n", 
                        i, pattern.from_state, pattern.to_state, 
                        os.date("%H:%M:%S", pattern.time), pattern.distance))
                end
            end
            
            file:write("\n-----------------------------------\n\n")
        end
        
        file:close()
        
        if self.debug_level >= 1 then
            _Log("分析结果已保存到 " .. self.analysis_file)
        end
    else
        _Log("警告: 无法保存分析结果")
    end
end

-- 获取怪物推荐战斗距离
function MonsterDataCollector:get_recommended_distance(monster_name)
    for id, data in pairs(self.monsters_data) do
        if data.name == monster_name then
            -- 分析该怪物数据
            local analysis = self:analyze_single_monster(id)
            if analysis and analysis.attack_range > 0 then
                -- 推荐距离 = 攻击范围 + 安全边际
                return analysis.attack_range + 2.0
            end
        end
    end
    
    -- 默认推荐距离
    return 16.0
end

-- 分析单个怪物数据
function MonsterDataCollector:analyze_single_monster(monster_id)
    local data = self.monsters_data[monster_id]
    if not data or #data.records < 10 then
        return nil
    end
    
    -- 简化版分析，只计算攻击范围
    local analysis = {
        name = data.name,
        id = monster_id,
        attack_range = 0
    }
    
    local prev_record = data.records[1]
    for i = 2, #data.records do
        local curr_record = data.records[i]
        
        if prev_record.state == "追击" and curr_record.state == "攻击" then
            local dist = _取坐标距离(prev_record.x, prev_record.y, curr_record.x, curr_record.y)
            if dist > analysis.attack_range then
                analysis.attack_range = dist
            end
        end
        
        prev_record = curr_record
    end
    
    return analysis
end

-- 返回收集器对象
return MonsterDataCollector