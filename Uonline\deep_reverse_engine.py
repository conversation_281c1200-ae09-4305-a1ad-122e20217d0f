#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度逆向引擎 - 真正的技术含量
分析游戏内部机制、协议、算法
"""

import os
import struct
import hashlib
import zlib
from pathlib import Path

class GameProtocolAnalyzer:
    """游戏协议分析器"""
    
    def __init__(self):
        self.packet_patterns = []
        self.encryption_keys = []
        self.protocol_opcodes = {}
    
    def analyze_network_protocol(self):
        """分析网络协议"""
        print("🌐 分析游戏网络协议...")
        
        # 从二进制文件中查找协议相关字符串
        protocol_hints = []
        
        try:
            with open('Prmain.exe', 'rb') as f:
                data = f.read()
            
            # 查找可能的协议标识
            patterns = [
                b'LOGIN', b'LOGOUT', b'MOVE', b'ATTACK', b'CHAT',
                b'TRADE', b'ITEM', b'SKILL', b'PARTY', b'GUILD',
                b'0x', b'MSG_', b'PKT_', b'CMD_'
            ]
            
            for pattern in patterns:
                offset = 0
                while True:
                    pos = data.find(pattern, offset)
                    if pos == -1:
                        break
                    
                    # 提取周围的上下文
                    start = max(0, pos - 20)
                    end = min(len(data), pos + 50)
                    context = data[start:end]
                    
                    # 过滤可打印字符
                    readable = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in context)
                    
                    protocol_hints.append({
                        'pattern': pattern.decode('ascii', errors='ignore'),
                        'offset': f'0x{pos:08X}',
                        'context': readable
                    })
                    
                    offset = pos + 1
                    if len(protocol_hints) > 20:  # 限制数量
                        break
            
            print(f"  ✅ 找到 {len(protocol_hints)} 个协议线索:")
            for hint in protocol_hints[:10]:
                print(f"    {hint['pattern']} @ {hint['offset']}: {hint['context']}")
            
            return protocol_hints
            
        except Exception as e:
            print(f"  ❌ 协议分析失败: {e}")
            return []

class GameMemoryStructure:
    """游戏内存结构分析"""
    
    def __init__(self):
        self.player_struct = {}
        self.item_struct = {}
        self.monster_struct = {}
    
    def analyze_player_structure(self):
        """分析玩家数据结构"""
        print("👤 分析玩家数据结构...")
        
        # 从配置文件推断玩家结构
        try:
            with open('prconfig.ini', 'r', encoding='utf-8') as f:
                config = f.read()
            
            # 提取玩家相关配置
            player_data = {}
            for line in config.split('\n'):
                if '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip()
                    
                    if key in ['Width', 'Height', 'FullScreen', 'Sound', 'Music']:
                        try:
                            player_data[key] = int(value)
                        except:
                            player_data[key] = value
            
            print(f"  ✅ 玩家配置结构:")
            for key, value in player_data.items():
                print(f"    {key}: {value} ({type(value).__name__})")
            
            # 推断内存结构
            memory_layout = {
                'player_id_offset': 0x00,      # 玩家ID
                'position_x_offset': 0x10,     # X坐标
                'position_y_offset': 0x14,     # Y坐标
                'hp_current_offset': 0x20,     # 当前HP
                'hp_max_offset': 0x24,         # 最大HP
                'mp_current_offset': 0x28,     # 当前MP
                'mp_max_offset': 0x2C,         # 最大MP
                'level_offset': 0x30,          # 等级
                'exp_offset': 0x34,            # 经验值
                'gold_offset': 0x40,           # 金币
                'inventory_offset': 0x100,     # 背包起始
            }
            
            print(f"  ✅ 推测的内存布局:")
            for name, offset in memory_layout.items():
                print(f"    {name}: 0x{offset:04X}")
            
            return memory_layout
            
        except Exception as e:
            print(f"  ❌ 玩家结构分析失败: {e}")
            return {}

class GameEncryptionAnalyzer:
    """游戏加密分析器"""
    
    def __init__(self):
        self.encryption_type = None
        self.key_candidates = []
    
    def analyze_encryption(self):
        """分析游戏加密算法"""
        print("🔐 分析游戏加密算法...")
        
        # 分析可能的加密方法
        encryption_indicators = []
        
        try:
            # 检查DLL文件中的加密函数
            dll_files = ['Uonline.dll', 'netmine.dll', 'white.dll']
            
            for dll_file in dll_files:
                if not os.path.exists(dll_file):
                    continue
                
                with open(dll_file, 'rb') as f:
                    data = f.read()
                
                # 查找加密相关字符串
                crypto_patterns = [
                    b'encrypt', b'decrypt', b'cipher', b'key', b'hash',
                    b'md5', b'sha', b'aes', b'des', b'rc4', b'xor'
                ]
                
                for pattern in crypto_patterns:
                    if pattern in data.lower():
                        pos = data.lower().find(pattern)
                        encryption_indicators.append({
                            'file': dll_file,
                            'pattern': pattern.decode(),
                            'offset': f'0x{pos:08X}'
                        })
            
            print(f"  ✅ 找到 {len(encryption_indicators)} 个加密线索:")
            for indicator in encryption_indicators:
                print(f"    {indicator['file']}: {indicator['pattern']} @ {indicator['offset']}")
            
            # 分析可能的密钥
            self.find_encryption_keys()
            
            return encryption_indicators
            
        except Exception as e:
            print(f"  ❌ 加密分析失败: {e}")
            return []
    
    def find_encryption_keys(self):
        """查找可能的加密密钥"""
        print("  🔑 查找加密密钥...")
        
        # 常见的密钥模式
        key_patterns = [
            b'uonline', b'game', b'server', b'client',
            b'password', b'secret', b'key123', b'admin'
        ]
        
        try:
            with open('Prmain.exe', 'rb') as f:
                data = f.read()
            
            found_keys = []
            for pattern in key_patterns:
                if pattern in data.lower():
                    pos = data.lower().find(pattern)
                    # 提取可能的密钥上下文
                    start = max(0, pos - 10)
                    end = min(len(data), pos + 30)
                    context = data[start:end]
                    
                    found_keys.append({
                        'pattern': pattern.decode(),
                        'offset': f'0x{pos:08X}',
                        'context': context.hex()
                    })
            
            if found_keys:
                print(f"    ✅ 找到 {len(found_keys)} 个可能的密钥:")
                for key in found_keys:
                    print(f"      {key['pattern']} @ {key['offset']}")
            else:
                print(f"    ⚠️ 未找到明显的密钥模式")
            
            return found_keys
            
        except Exception as e:
            print(f"    ❌ 密钥查找失败: {e}")
            return []

class GameAIAnalyzer:
    """游戏AI行为分析"""
    
    def __init__(self):
        self.monster_ai = {}
        self.npc_behavior = {}
    
    def analyze_monster_ai(self):
        """分析怪物AI算法"""
        print("🤖 分析怪物AI算法...")
        
        # 从怪物PAK文件分析AI模式
        monster_files = [f for f in os.listdir('.') if f.startswith('M2013') and f.endswith('.pak')]
        
        ai_patterns = {}
        
        for monster_file in monster_files[:5]:  # 分析前5个怪物
            try:
                with open(monster_file, 'rb') as f:
                    data = f.read()
                
                # 查找可能的AI参数
                # 通常AI参数是小整数，查找连续的小数值
                ai_params = []
                for i in range(0, min(1000, len(data) - 4), 4):
                    try:
                        value = struct.unpack('<I', data[i:i+4])[0]
                        if 1 <= value <= 1000:  # 合理的AI参数范围
                            ai_params.append((i, value))
                    except:
                        continue
                
                if ai_params:
                    monster_name = monster_file.replace('.pak', '')
                    ai_patterns[monster_name] = ai_params[:10]  # 只保留前10个
                    
                    print(f"  📊 {monster_name} AI参数:")
                    for offset, value in ai_params[:5]:
                        print(f"    偏移0x{offset:04X}: {value}")
            
            except Exception as e:
                continue
        
        # 分析AI行为模式
        self.analyze_ai_behavior_patterns(ai_patterns)
        
        return ai_patterns
    
    def analyze_ai_behavior_patterns(self, ai_patterns):
        """分析AI行为模式"""
        print("  🧠 分析AI行为模式...")
        
        # 统计AI参数分布
        all_values = []
        for monster, params in ai_patterns.items():
            for offset, value in params:
                all_values.append(value)
        
        if all_values:
            avg_value = sum(all_values) / len(all_values)
            max_value = max(all_values)
            min_value = min(all_values)
            
            print(f"    AI参数统计:")
            print(f"      平均值: {avg_value:.1f}")
            print(f"      最大值: {max_value}")
            print(f"      最小值: {min_value}")
            
            # 推测AI行为类型
            if avg_value < 50:
                print(f"    推测: 保守型AI (低攻击性)")
            elif avg_value < 200:
                print(f"    推测: 平衡型AI (中等攻击性)")
            else:
                print(f"    推测: 激进型AI (高攻击性)")

def main():
    print("=" * 70)
    print("🎯 Uonline 深度逆向分析引擎")
    print("=" * 70)
    
    # 协议分析
    protocol_analyzer = GameProtocolAnalyzer()
    protocol_data = protocol_analyzer.analyze_network_protocol()
    
    print()
    
    # 内存结构分析
    memory_analyzer = GameMemoryStructure()
    memory_layout = memory_analyzer.analyze_player_structure()
    
    print()
    
    # 加密分析
    crypto_analyzer = GameEncryptionAnalyzer()
    crypto_data = crypto_analyzer.analyze_encryption()
    
    print()
    
    # AI分析
    ai_analyzer = GameAIAnalyzer()
    ai_data = ai_analyzer.analyze_monster_ai()
    
    print()
    print("=" * 70)
    print("📊 深度分析总结")
    print("=" * 70)
    print(f"🌐 协议线索: {len(protocol_data)} 个")
    print(f"💾 内存结构: {len(memory_layout)} 个偏移")
    print(f"🔐 加密线索: {len(crypto_data)} 个")
    print(f"🤖 AI模式: {len(ai_data)} 个怪物")
    print("=" * 70)

if __name__ == "__main__":
    main()
