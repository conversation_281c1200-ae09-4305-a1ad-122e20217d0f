#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真正的逆向工程 - 分析PE文件结构，提取内存地址，查找API调用
"""

import struct
import os
import re

def analyze_pe_structure(filename):
    """深度分析PE文件结构"""
    print(f"\n🔍 PE结构分析: {filename}")
    
    if not os.path.exists(filename):
        return None
    
    try:
        with open(filename, 'rb') as f:
            # DOS头
            dos_header = f.read(64)
            if dos_header[:2] != b'MZ':
                print("  ❌ 不是有效的PE文件")
                return None
            
            pe_offset = struct.unpack('<I', dos_header[60:64])[0]
            print(f"  PE头偏移: 0x{pe_offset:08X}")
            
            # PE头
            f.seek(pe_offset)
            pe_sig = f.read(4)
            if pe_sig != b'PE\x00\x00':
                print("  ❌ PE签名无效")
                return None
            
            # COFF头
            coff_header = f.read(20)
            machine, sections, timestamp, ptr_to_sym, num_sym, opt_size, characteristics = struct.unpack('<HHIIIHH', coff_header)
            
            print(f"  机器类型: 0x{machine:04X}")
            print(f"  节数量: {sections}")
            print(f"  时间戳: 0x{timestamp:08X}")
            print(f"  可选头大小: {opt_size}")
            print(f"  特征: 0x{characteristics:04X}")
            
            # 可选头
            if opt_size > 0:
                opt_header = f.read(opt_size)
                if len(opt_header) >= 96:
                    magic = struct.unpack('<H', opt_header[0:2])[0]
                    entry_point = struct.unpack('<I', opt_header[16:20])[0]
                    image_base = struct.unpack('<I', opt_header[28:32])[0]
                    
                    print(f"  入口点: 0x{entry_point:08X}")
                    print(f"  镜像基址: 0x{image_base:08X}")
                    print(f"  实际入口: 0x{image_base + entry_point:08X}")
                    
                    # 数据目录
                    if len(opt_header) >= 128:
                        import_rva = struct.unpack('<I', opt_header[80:84])[0]
                        import_size = struct.unpack('<I', opt_header[84:88])[0]
                        
                        if import_rva > 0:
                            print(f"  导入表RVA: 0x{import_rva:08X}")
                            print(f"  导入表大小: {import_size}")
                            
                            return {
                                'image_base': image_base,
                                'entry_point': entry_point,
                                'import_rva': import_rva,
                                'import_size': import_size,
                                'sections': sections
                            }
            
            return None
            
    except Exception as e:
        print(f"  分析失败: {e}")
        return None

def find_api_calls(filename):
    """查找API调用"""
    print(f"\n🎯 API调用分析: {filename}")
    
    if not os.path.exists(filename):
        return []
    
    try:
        with open(filename, 'rb') as f:
            data = f.read()
        
        # 查找常见的Windows API字符串
        api_patterns = [
            b'CreateFileA', b'CreateFileW',
            b'ReadFile', b'WriteFile',
            b'VirtualAlloc', b'VirtualProtect',
            b'LoadLibraryA', b'LoadLibraryW',
            b'GetProcAddress',
            b'socket', b'connect', b'send', b'recv',
            b'CreateThread', b'CreateProcess',
            b'RegOpenKeyA', b'RegOpenKeyW',
            b'FindFirstFileA', b'FindFirstFileW'
        ]
        
        found_apis = []
        for api in api_patterns:
            offset = 0
            while True:
                pos = data.find(api, offset)
                if pos == -1:
                    break
                found_apis.append((pos, api.decode('ascii', errors='ignore')))
                offset = pos + 1
        
        # 按偏移排序
        found_apis.sort()
        
        print(f"  找到 {len(found_apis)} 个API引用:")
        for offset, api in found_apis[:20]:  # 显示前20个
            print(f"    0x{offset:08X}: {api}")
        
        return found_apis
        
    except Exception as e:
        print(f"  分析失败: {e}")
        return []

def find_memory_patterns(filename):
    """查找内存操作模式"""
    print(f"\n🧠 内存模式分析: {filename}")
    
    if not os.path.exists(filename):
        return []
    
    try:
        with open(filename, 'rb') as f:
            data = f.read()
        
        patterns = []
        
        # 查找PUSH指令后跟地址 (68 xx xx xx xx)
        for i in range(len(data) - 4):
            if data[i] == 0x68:  # PUSH immediate
                addr = struct.unpack('<I', data[i+1:i+5])[0]
                if 0x400000 <= addr <= 0x800000:  # 典型代码段地址
                    patterns.append(('PUSH', i, addr))
        
        # 查找MOV指令操作内存 (C7 05 xx xx xx xx)
        for i in range(len(data) - 9):
            if data[i:i+2] == b'\xC7\x05':  # MOV [addr], immediate
                addr = struct.unpack('<I', data[i+2:i+6])[0]
                value = struct.unpack('<I', data[i+6:i+10])[0]
                if 0x400000 <= addr <= 0x800000:
                    patterns.append(('MOV_MEM', i, addr, value))
        
        # 查找CALL指令 (E8 xx xx xx xx)
        for i in range(len(data) - 4):
            if data[i] == 0xE8:  # CALL relative
                rel_addr = struct.unpack('<i', data[i+1:i+5])[0]
                abs_addr = i + 5 + rel_addr
                if 0 < abs_addr < len(data):
                    patterns.append(('CALL', i, abs_addr))
        
        print(f"  找到 {len(patterns)} 个内存操作模式:")
        
        # 按类型分组显示
        push_count = len([p for p in patterns if p[0] == 'PUSH'])
        mov_count = len([p for p in patterns if p[0] == 'MOV_MEM'])
        call_count = len([p for p in patterns if p[0] == 'CALL'])
        
        print(f"    PUSH指令: {push_count}")
        print(f"    MOV内存: {mov_count}")
        print(f"    CALL指令: {call_count}")
        
        # 显示一些具体例子
        for pattern_type in ['PUSH', 'MOV_MEM', 'CALL']:
            examples = [p for p in patterns if p[0] == pattern_type][:5]
            if examples:
                print(f"    {pattern_type} 示例:")
                for ex in examples:
                    if len(ex) == 3:
                        print(f"      0x{ex[1]:08X} -> 0x{ex[2]:08X}")
                    elif len(ex) == 4:
                        print(f"      0x{ex[1]:08X} -> [0x{ex[2]:08X}] = 0x{ex[3]:08X}")
        
        return patterns
        
    except Exception as e:
        print(f"  分析失败: {e}")
        return []

def extract_strings_with_xrefs(filename):
    """提取字符串并查找交叉引用"""
    print(f"\n📝 字符串交叉引用分析: {filename}")
    
    if not os.path.exists(filename):
        return {}
    
    try:
        with open(filename, 'rb') as f:
            data = f.read()
        
        # 提取ASCII字符串
        strings = {}
        current = ""
        start_offset = 0
        
        for i, byte in enumerate(data):
            if 32 <= byte <= 126:
                if not current:
                    start_offset = i
                current += chr(byte)
            else:
                if len(current) >= 6:
                    strings[start_offset] = current
                current = ""
        
        if current and len(current) >= 6:
            strings[start_offset] = current
        
        # 查找字符串的引用
        string_refs = {}
        for str_offset, string in strings.items():
            refs = []
            
            # 查找PUSH指令引用这个字符串
            str_addr_bytes = struct.pack('<I', str_offset + 0x400000)  # 假设基址
            
            offset = 0
            while True:
                # 查找 68 xx xx xx xx (PUSH immediate)
                pos = data.find(b'\x68' + str_addr_bytes, offset)
                if pos == -1:
                    break
                refs.append(pos)
                offset = pos + 1
            
            if refs:
                string_refs[str_offset] = (string, refs)
        
        print(f"  找到 {len(strings)} 个字符串")
        print(f"  其中 {len(string_refs)} 个有交叉引用")
        
        # 显示有引用的字符串
        for str_offset, (string, refs) in list(string_refs.items())[:10]:
            print(f"    0x{str_offset:08X}: \"{string}\" ({len(refs)} 个引用)")
            for ref in refs[:3]:
                print(f"      <- 0x{ref:08X}")
        
        return string_refs
        
    except Exception as e:
        print(f"  分析失败: {e}")
        return {}

def analyze_network_code(filename):
    """分析网络相关代码"""
    print(f"\n🌐 网络代码分析: {filename}")
    
    if not os.path.exists(filename):
        return
    
    try:
        with open(filename, 'rb') as f:
            data = f.read()
        
        # 查找网络相关的常量
        network_patterns = [
            (b'\x01\x00\x00\x00', 'SOCKET_STREAM'),
            (b'\x02\x00\x00\x00', 'AF_INET'),
            (b'\x06\x00\x00\x00', 'IPPROTO_TCP'),
            (b'\xA1\x0F\x00\x00', 'Port 4001'),
            (b'\x50\x00\x00\x00', 'Port 80'),
        ]
        
        found_patterns = []
        for pattern, name in network_patterns:
            offset = 0
            while True:
                pos = data.find(pattern, offset)
                if pos == -1:
                    break
                found_patterns.append((pos, name))
                offset = pos + 1
        
        # 查找IP地址模式
        ip_candidates = []
        for i in range(len(data) - 3):
            ip_bytes = data[i:i+4]
            if (1 <= ip_bytes[0] <= 223 and 
                0 <= ip_bytes[1] <= 255 and 
                0 <= ip_bytes[2] <= 255 and 
                1 <= ip_bytes[3] <= 254):
                ip = f"{ip_bytes[0]}.{ip_bytes[1]}.{ip_bytes[2]}.{ip_bytes[3]}"
                ip_candidates.append((i, ip))
        
        # 去重IP
        unique_ips = {}
        for offset, ip in ip_candidates:
            if ip not in unique_ips:
                unique_ips[ip] = []
            unique_ips[ip].append(offset)
        
        print(f"  网络常量: {len(found_patterns)} 个")
        for offset, name in found_patterns:
            print(f"    0x{offset:08X}: {name}")
        
        print(f"  可能的IP地址: {len(unique_ips)} 个")
        for ip, offsets in list(unique_ips.items())[:10]:
            print(f"    {ip} (出现在 {len(offsets)} 个位置)")
        
    except Exception as e:
        print(f"  分析失败: {e}")

def main():
    """主函数"""
    print("=" * 70)
    print("🎯 真正的逆向工程分析")
    print("=" * 70)
    
    # 分析主要的可执行文件
    target_files = ['Prmain.exe', 'Uonline.dll', 'netmine.dll']
    
    for filename in target_files:
        if os.path.exists(filename):
            print(f"\n{'='*60}")
            print(f"🔍 逆向分析: {filename}")
            print(f"{'='*60}")
            
            # PE结构分析
            pe_info = analyze_pe_structure(filename)
            
            # API调用分析
            find_api_calls(filename)
            
            # 内存模式分析
            find_memory_patterns(filename)
            
            # 字符串交叉引用
            extract_strings_with_xrefs(filename)
            
            # 网络代码分析
            if 'net' in filename.lower():
                analyze_network_code(filename)
    
    print(f"\n" + "=" * 70)
    print("✅ 逆向分析完成")
    print("=" * 70)

if __name__ == "__main__":
    main()
