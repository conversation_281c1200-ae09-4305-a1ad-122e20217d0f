_Hook技能发包代码()

-- 初始化全局变量
g_攻击距离 = g_攻击距离 or 10.0
远程检测玩家距离 = 远程检测玩家距离 or 40
g_血量低于多少回复 = g_血量低于多少回复 or 100
local g_检测到玩家蹲下 = false

-- ?? 双模式攻击系统配置
local 攻击模式 = {
    当前模式 = "正常",  -- "正常" 或 "变态"
    正常模式 = {
        攻击间隔 = 1.2,
        上次攻击时间 = 0,
        描述 = "正常节奏攻击"
    },
    变态模式 = {
        攻击间隔 = 0,  -- 50毫秒超高频
        连续攻击次数 = 0,
        最大连续攻击 = 2,
        上次重置时间 = 0,
        描述 = "变态高频攻击"
    }
}

-- 挂机点坐标
local 挂机点x = 0
local 挂机点y = 0
local 挂机点z = 0

-- 物品白名单配置
local 物品白名单 = {
    "神", "云雀", "宝箱", "血色", "哀", "十", "新", "刀", "石", "宝箱"
}

-- 玩家白名单配置
local WHITELIST = {
    "龙DE传人", "寒冰", "神说", "老司机", "光辉"
}

-- 配置参数
local CONFIG = {
    最大追踪时间 = 50,          
    连续掉血阈值 = 3,           
    血量保护比例 = 0.5,         
    攻击冷却时间 = 0.01,        
    后退距离 = 14.0,           
    检查间隔 = 500,            
    位置记录间隔 = 2,          
    最大卡住时间 = 5,          
    输出等级 = 2,
    BUFF剩余时间 = 30,
    最小战斗距离 = 14.0,       
    理想战斗距离 = 16.0,
    捡物范围 = 4.0,
}

-- ?? 智能攻击系统
function 智能攻击系统(monster)
    local current_time = os.clock()
    local 有玩家 = 检测附近玩家()
    
    -- 根据是否有玩家切换模式
    if 有玩家 and 攻击模式.当前模式 ~= "正常" then
        攻击模式.当前模式 = "正常"
        攻击模式.变态模式.连续攻击次数 = 0  -- 重置变态模式计数
        _Log("?? 切换到正常攻击模式 - 检测到玩家")
    elseif not 有玩家 and 攻击模式.当前模式 ~= "变态" then
        攻击模式.当前模式 = "变态"
        攻击模式.正常模式.上次攻击时间 = current_time  -- 重置正常模式时间
        _Log("?? 切换到变态攻击模式 - 无玩家环境")
    end
    
    -- 执行对应模式的攻击
    if 攻击模式.当前模式 == "正常" then
        return 正常模式攻击(monster, current_time)
    else
        return 变态模式攻击(monster, current_time)
    end
end

function 正常模式攻击(monster, current_time)
    local 正常 = 攻击模式.正常模式
    
    if current_time - 正常.上次攻击时间 >= 正常.攻击间隔 then
        _普攻(monster.Id)  -- 使用你测试有效的_普攻函数
        正常.上次攻击时间 = current_time
        _Log(string.format("?? 正常攻击: %s (间隔: %.1f秒)", monster.Name, 正常.攻击间隔))
        return true
    end
    return false
end

function 变态模式攻击(monster, current_time)
    local 变态 = 攻击模式.变态模式
    
    -- 检查是否需要重置
    if 变态.连续攻击次数 >= 变态.最大连续攻击 then
        if current_time - 变态.上次重置时间 >= 0.5 then  -- 0.5秒重置间隔
            变态.连续攻击次数 = 0
            变态.上次重置时间 = current_time
            _Log("?? 变态模式重置攻击计数")
            return false
        end
        return false
    end
    
    -- 变态高频攻击
    _普攻(monster.Id)
    变态.连续攻击次数 = 变态.连续攻击次数 + 1
    _Log(string.format("?? 变态攻击: %s (第%d次)", monster.Name, 变态.连续攻击次数))
    return true
end

-- 脚本全状态机
g_State = {
    Funs = {},  
    Params = {},  
    FunName = "",
    MonsterObj = nil,
    Run = function()
        return g_State.Funs[g_State.FunName](g_State.Params[g_State.FunName])
    end,
    Create = function()
        while true do
            _Log("-------------------------------------- 当前运行状态：" .. g_State.FunName)
            g_State.FunName = g_State.Run()
            local result = _意外处理(_取人物信息())
            if result then
                g_State.FunName = result
                g_State.MonsterObj = nil
            end
            if g_State.Funs[g_State.FunName] == nil then
                break
            end
            _Sleep(100)
        end
    end
}

-- 检测函数
function 检测附近玩家()
    local role = _取人物信息()
    local players = _取周围玩家信息()
    if players and role then
        for _, player in ipairs(players) do
            local is_whitelisted = false
            for _, whitelist_name in ipairs(WHITELIST) do
                if player.Name == whitelist_name then
                    is_whitelisted = true
                    break
                end
            end
            
            if not is_whitelisted and player.Id ~= role.Id then
                local distance = _取坐标距离(role.x, role.y, player.x, player.y)
                if distance < 远程检测玩家距离 then
                    _Log(string.format("检测到非白名单玩家 %s，距离 %.2f", player.Name, distance))
                    return true
                end
            end
        end
    end
    return false
end

function _取范围内最近怪物()
    local role = _取人物信息()
    local monster = _取周围怪物信息()
    if monster ~= nil and role.Die == false and role.x ~= nil then
        local Len = #monster
        for i = 1,Len do
            monster[i].juli = _取坐标距离(role.x,role.y,monster[i].x,monster[i].y)
        end
        
        table.sort(monster, function(a, b) return a.juli < b.juli end)

        for _, m in ipairs(monster) do
            if not g_指定怪物 or g_指定怪物 == "" then
                if _取坐标距离(挂机点x,挂机点y,m.x,m.y) <= tonumber(g_挂机范围) then
                    _Log(string.format("范围内最近怪物 -> 距离=%.2f, 名称=%s, ID=%08X", m.juli, m.Name, m.Id))
                    return m
                end
            elseif m.Name == g_指定怪物 then
                if _取坐标距离(挂机点x,挂机点y,m.x,m.y) <= tonumber(g_挂机范围) then
                    _Log(string.format("范围内指定怪物 -> 距离=%.2f, 名称=%s, ID=%08X", m.juli, m.Name, m.Id))
                    return m
                end    
            end
        end
    end
    return nil
end

function _取范围内地面物品()
    local role = _取人物信息()
    local items = _取地面物品信息()
    
    if not items or not role or role.Die or not role.x then
        return nil
    end

    for _, item in ipairs(items) do
        local distance = _取坐标距离(role.x, role.y, item.x, item.y)
        
        if _取坐标距离(挂机点x, 挂机点y, item.x, item.y) <= tonumber(g_挂机范围) then
            for _, prefix in ipairs(物品白名单) do
                if string.find(item.Name, prefix) then
                    _Log(string.format("发现物品: %s, 距离: %.2f", item.Name, distance))
                    return item
                end
            end
        end
    end
    return nil
end

function _更新怪物属性(_怪)
    if not _怪 then return nil end
    
    local tb = {}
    local offset = 2632 + 264
    
    tb.Hp = _R4(_怪.Obj + offset)
    if tb.Hp > 0 then
        tb.HpM = _R4(_怪.Obj + offset + 4)
        tb.x = _Rf(_怪.Obj + 8)
        tb.z = _Rf(_怪.Obj + 12)
        tb.y = _Rf(_怪.Obj + 16)
        tb.Obj = _怪.Obj
        tb.Id = _怪.Id
        tb.Name = _怪.Name
        
        _Log(string.format("更新怪物属性 -> ID=%08X, 名称=%s, HP=%d/%d", tb.Id, tb.Name, tb.Hp, tb.HpM))
        return tb
    end
    return nil
end

function _回复血量()
    local role = _取人物信息()
    if role.x and _取坐标距离(role.x,role.y,g_复活到挂机[1].x,g_复活到挂机[1].y) < 10 then
        if _是否蹲下() then
            _Log("在复活点检测到蹲下状态，执行站立")
            _蹲下()
            _Sleep(1000)
        end
        return false
    end
    
    if g_State.MonsterObj == nil and role.x and role.Hp < g_血量低于多少回复 then
        while true do
            role = _取人物信息()
            if role.x and _取坐标距离(role.x,role.y,g_复活到挂机[1].x,g_复活到挂机[1].y) < 10 then
                if _是否蹲下() then
                    _Log("被召唤到复活点，从回血状态站立")
                    _蹲下()
                    _Sleep(1000)
                end
                return false
            end
            
            if not _是否蹲下() then
                _Log("执行回血，切换到蹲下状态")
                _Sleep(2000)
                _蹲下()
            end
            
            if role.Hp == role.HpM then
                if _是否蹲下() then
                    _Log("血量已满，从蹲下切换到站立")
                    _Sleep(2000)
                    _蹲下()
                    _Sleep(5000)
                end
                _Log("回血完成")
                return false
            end
            
            local result = _意外处理(_取人物信息())
            if result then
                if _是否蹲下() then
                    _Log("意外处理，从回血状态切换到站立")
                    _蹲下()
                    _Sleep(1000)
                end
                return result
            end
            _Sleep(500)
        end
    end
    return false
end

-- 状态机函数
g_State.Funs._自动捡物 = function(Param)
    local role = _取人物信息()
    local item = _取范围内地面物品()
    
    if not item or not role.x then
        return "_任务循环"
    end
    
    local distance = _取坐标距离(role.x, role.y, item.x, item.y)
    
    if distance <= CONFIG.捡物范围 then
        _捡物(item.Id)
        _Sleep(100)
        return "_任务循环"
    end
    
    if distance > tonumber(g_挂机范围) + 5 then
        return "_任务循环"
    end
    
    _走路(item.x, item.y, item.z)
    _Sleep(50)
    
    return "_自动捡物"
end

g_State.Funs._追踪怪物 = function(Param)   
    local role = nil
    local monster = nil
    local 追踪开始时间 = os.time()
    local 连续卡住次数 = 0
    local oldPt = {x = 0, y = 0, z = 0}
    local _记录 = true
    local f = 5.0
    local 安全距离 = 8.0
    
    while true do
        if _Exit() == false then 
            _停止走路()
            return "" 
        end

        -- 处理蹲下状态
        if _是否蹲下() then
            if _GetSummonEnd() then
                _Log("被召唤状态检测到蹲下，执行站立")
                _蹲下()
                _Sleep(1000)
                return "_任务循环"
            end
            
            if g_检测到玩家蹲下 then
                if 检测附近玩家() then
                    _Log("检测到玩家，保持蹲下")
                    return "_任务循环"
                else
                    _Log("玩家已离开，站起来继续打怪")
                    _蹲下()
                    _Sleep(1000)
                    g_检测到玩家蹲下 = false
                end
            else
                _Log("检测到意外蹲下状态，尝试站立")
                _蹲下()
                _Sleep(1000)
            end
        end
        
        role = _取人物信息()
        if not role or not role.x then 
            return "_初始化" 
        end

        if role.Hp < g_血量低于多少回复 then
            _Log("血量不足，需要回血")
            g_State.MonsterObj = nil
            return "_任务循环"
        end

        local nearby_item = _取范围内地面物品()
        if nearby_item then
            local item_distance = _取坐标距离(role.x, role.y, nearby_item.x, nearby_item.y)
            if item_distance <= CONFIG.捡物范围 then
                _捡物(nearby_item.Id)
                _Sleep(100)
            end
        end

        local nearby_monsters = _取周围怪物信息()
        if nearby_monsters then
            for _, m in ipairs(nearby_monsters) do
                if m.Id ~= Param.Id then
                    local dist = _取坐标距离(role.x, role.y, m.x, m.y)
                    if dist < 安全距离 then
                        _Log(string.format("发现近距离怪物: %s, 距离: %.2f, 优先处理", m.Name, dist))
                        g_State.Params._追踪怪物 = m
                        g_State.MonsterObj = m
                        Param = m
                        break
                    end
                end
            end
        end

        if 检测附近玩家() then
            _Log("[警告] 检测到附近有玩家,继续打完当前怪物")
            g_检测到玩家蹲下 = true
        end

        monster = _更新怪物属性(Param)
        if not monster or monster.Hp <= 0 then
            if g_检测到玩家蹲下 then
                _Log("怪物已死亡，检测到玩家，执行蹲下")
                if not _是否蹲下() then
                    _蹲下()
                end
                while 检测附近玩家() do
                    _Sleep(1000)
                end
                _Log("玩家已离开，站起来继续打怪")
                if _是否蹲下() then
                    _蹲下()
                end
                g_检测到玩家蹲下 = false
            end
            g_State.MonsterObj = nil
            return "_任务循环"
        end

        -- ?? 核心改动：使用智能攻击系统
        local 当前距离 = _取坐标距离(role.x, role.y, monster.x, monster.y)
        if 当前距离 <= g_攻击距离 and not _是否蹲下() then
            -- 使用智能攻击系统替代原来的_释放技能
            local 攻击成功 = 智能攻击系统(monster)
            if 攻击成功 then
                追踪开始时间 = os.time()
            end
            
            -- 保持原有的后退逻辑
            local dx = role.x - monster.x
            local dy = role.y - monster.y
            local dist = math.sqrt(dx * dx + dy * dy)
            
            if dist > 0 then
                dx = dx / dist
                dy = dy / dist
                
                local target_x = monster.x + dx * g_攻击距离
                local target_y = monster.y + dy * g_攻击距离
                
                local is_safe = true
                if nearby_monsters then
                    for _, m in ipairs(nearby_monsters) do
                        if m.Id ~= monster.Id then
                            local back_dist = _取坐标距离(target_x, target_y, m.x, m.y)
                            if back_dist < 安全距离 then
                                is_safe = false
                                break
                            end
                        end
                    end
                end
                
                if is_safe then
                    _走路(target_x, target_y, role.z)
                end
            end
        elseif not _是否蹲下() then
            if _记录 then
                oldPt.x, oldPt.y, oldPt.z = monster.x, monster.y, monster.z
                _记录 = false
            elseif _取坐标距离(oldPt.x, oldPt.y, monster.x, monster.y) > f then
                _记录 = true
                _走路(role.x, role.y, role.z)
                _Sleep(10)
            end
            _走路(monster.x, monster.y, monster.z)
        end
        
        if os.time() - 追踪开始时间 > CONFIG.最大追踪时间 then
            g_State.MonsterObj = nil
            return "_任务循环"
        end
        
        -- ?? 根据攻击模式调整循环延迟
        if 攻击模式.当前模式 == "变态" then
            _Sleep(攻击模式.变态模式.攻击间隔 * 1000)  -- 变态模式用更短延迟
        else
            _Sleep(25)  -- 正常模式保持原延迟
        end
    end
end

g_State.Funs._任务循环 = function()
    local role = _取人物信息()
    
    if _GetSummonEnd() then
        if _是否蹲下() then
            _Log("被召唤检测到蹲下状态，立即站立")
            _蹲下()
            _Sleep(1000)
        end
        g_检测到玩家蹲下 = false
        _Log("_任务循环 -> 正在被召唤")
        return "_任务循环"
    end

    if g_检测到玩家蹲下 then
        if 检测附近玩家() then
            if not _是否蹲下() then
                _Log("检测到玩家，保持蹲下")
                _蹲下()
            end
            _Sleep(1000)
            return "_任务循环"
        else
            _Log("玩家已离开，站起来继续打怪")
            if _是否蹲下() then
                _蹲下()
            end
            g_检测到玩家蹲下 = false
        end
    end

    if role.x ~= nil and _取坐标距离(role.x,role.y,g_复活到挂机[1].x,g_复活到挂机[1].y) < 10 then
        if g_挂机模式 == "单人" then
            _Log("_任务循环 -> 人物在复活点,开始寻路到挂机点")
            return _到挂机点()
        end
    end

    local item = _取范围内地面物品()
    if item then
        local distance = _取坐标距离(role.x, role.y, item.x, item.y)
        if distance <= CONFIG.捡物范围 * 2 then
            return "_自动捡物"
        end
    end

    local result = _回复血量()
    if result then
        return result
    end

    if role.x ~= nil and g_State.MonsterObj == nil then
        local 距离挂机点 = _取坐标距离(挂机点x,挂机点y,role.x,role.y)
        if 距离挂机点 > tonumber(g_挂机范围) then
            _Log(string.format("超出挂机范围(%.1f)，返回挂机点", 距离挂机点))
            _走路(挂机点x, 挂机点y, 挂机点z)
            _Sleep(500)
            return "_任务循环"
        end
    end

    if role.x ~= nil and _取坐标距离(挂机点x,挂机点y,role.x,role.y) <= tonumber(g_挂机范围) + 10 then
        if g_State.MonsterObj == nil then
            if item then
                return "_自动捡物"
            end
            
            local _怪 = _取范围内最近怪物()
            if _怪 ~= nil then 
                g_State.Params._追踪怪物 = _怪
                g_State.MonsterObj = _怪
                return "_追踪怪物"
            else
                _Log("_任务循环 -> 挂机范围内没有怪物")
                _Sleep(500)
            end
        else
            _Log("继续追踪当前怪物")
            g_State.Params._追踪怪物 = g_State.MonsterObj
            return "_追踪怪物"
        end
    end
    
    return "_任务循环"
end

g_State.Funs._初始化 = function()
    if _读寻路坐标() then
        挂机点x,挂机点y,挂机点z = g_复活到挂机[#g_复活到挂机].x,g_复活到挂机[#g_复活到挂机].y,g_复活到挂机[#g_复活到挂机].z
        if g_挂机模式 == "单人" then
            return _到挂机点()
        end
        return "_任务循环"
    end
end

g_State.Funs._到挂机点 = _到挂机点

-- 启动脚本
g_State.FunName = "_初始化"
g_State.Create()