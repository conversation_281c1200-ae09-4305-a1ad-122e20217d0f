#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Uonline 游戏数据分析 - 发现有趣的游戏机制
基于Message.ctf文件的真实数据分析
"""

import re
import os

def analyze_hp_values():
    """分析HP数值"""
    print("🔍 分析HP数值...")
    
    try:
        with open("Uonline/Message.ctf", 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # 提取所有HP数值
        hp_patterns = [
            r'HP\+(\d+)',
            r'HP增加\s*(\d+)',
            r'HP.*?(\d+)'
        ]
        
        all_hp_values = []
        for pattern in hp_patterns:
            matches = re.findall(pattern, content)
            all_hp_values.extend([int(x) for x in matches])
        
        # 去重并排序
        unique_hp = sorted(list(set(all_hp_values)), reverse=True)
        
        print(f"  📊 发现 {len(unique_hp)} 种不同的HP数值")
        print(f"  🎯 最高HP值: {unique_hp[0] if unique_hp else 0}")
        print(f"  🎯 前10个最高HP值: {unique_hp[:10]}")
        
        # 分析HP分布
        hp_ranges = {
            "超高HP (10000+)": [x for x in unique_hp if x >= 10000],
            "高HP (1000-9999)": [x for x in unique_hp if 1000 <= x < 10000],
            "中HP (100-999)": [x for x in unique_hp if 100 <= x < 1000],
            "低HP (1-99)": [x for x in unique_hp if 1 <= x < 100]
        }
        
        for range_name, values in hp_ranges.items():
            if values:
                print(f"  📈 {range_name}: {len(values)} 种")
        
        return unique_hp
        
    except Exception as e:
        print(f"❌ 分析HP失败: {e}")
        return []

def analyze_equipment_stats():
    """分析装备属性"""
    print("🔍 分析装备属性...")
    
    try:
        with open("Uonline/Message.ctf", 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # 分析各种属性
        stats = {
            "攻击力": re.findall(r'攻击力.*?(\d+)', content),
            "防御力": re.findall(r'防御力.*?(\d+)', content),
            "力量": re.findall(r'力量.*?(\d+)', content),
            "敏捷": re.findall(r'敏捷.*?(\d+)', content),
            "智力": re.findall(r'智力.*?(\d+)', content),
            "体力": re.findall(r'体力.*?(\d+)', content)
        }
        
        for stat_name, values in stats.items():
            if values:
                int_values = [int(x) for x in values]
                max_val = max(int_values) if int_values else 0
                print(f"  🎯 {stat_name}: 最高 {max_val}, 共 {len(set(int_values))} 种数值")
        
        return stats
        
    except Exception as e:
        print(f"❌ 分析装备属性失败: {e}")
        return {}

def analyze_synthesis_formulas():
    """分析合成公式"""
    print("🔍 分析合成公式...")
    
    try:
        with open("Uonline/Message.ctf", 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # 查找合成公式
        synthesis_patterns = [
            r'合成公式[：:](.*?)(?=\n|$)',
            r'合成.*?[：:](.*?)(?=\n|$)',
            r'制作.*?[：:](.*?)(?=\n|$)'
        ]
        
        formulas = []
        for pattern in synthesis_patterns:
            matches = re.findall(pattern, content, re.MULTILINE)
            formulas.extend(matches)
        
        # 去重
        unique_formulas = list(set(formulas))
        
        print(f"  📊 发现 {len(unique_formulas)} 个合成公式")
        
        # 显示一些有趣的公式
        for i, formula in enumerate(unique_formulas[:10]):
            if formula.strip():
                print(f"    {i+1}. {formula.strip()}")
        
        return unique_formulas
        
    except Exception as e:
        print(f"❌ 分析合成公式失败: {e}")
        return []

def analyze_success_rates():
    """分析成功率"""
    print("🔍 分析成功率...")
    
    try:
        with open("Uonline/Message.ctf", 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # 查找成功率
        success_patterns = [
            r'成功率[：:]?\s*(\d+)%',
            r'成功率[：:]?\s*(\d+)',
            r'(\d+)%.*?成功'
        ]
        
        success_rates = []
        for pattern in success_patterns:
            matches = re.findall(pattern, content)
            success_rates.extend([int(x) for x in matches])
        
        # 去重并排序
        unique_rates = sorted(list(set(success_rates)))
        
        print(f"  📊 发现 {len(unique_rates)} 种成功率")
        print(f"  🎯 成功率范围: {min(unique_rates) if unique_rates else 0}% - {max(unique_rates) if unique_rates else 0}%")
        print(f"  📈 所有成功率: {unique_rates}")
        
        return unique_rates
        
    except Exception as e:
        print(f"❌ 分析成功率失败: {e}")
        return []

def analyze_game_currency():
    """分析游戏货币"""
    print("🔍 分析游戏货币...")
    
    try:
        with open("Uonline/Message.ctf", 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # 查找货币相关
        currency_patterns = [
            r'(\d+).*?游戏币',
            r'游戏币.*?(\d+)',
            r'(\d+).*?金币',
            r'金币.*?(\d+)',
            r'(\d+)W',  # 万的简写
            r'(\d+)万'
        ]
        
        currency_values = []
        for pattern in currency_patterns:
            matches = re.findall(pattern, content)
            currency_values.extend([int(x) for x in matches])
        
        # 去重并排序
        unique_currency = sorted(list(set(currency_values)), reverse=True)
        
        print(f"  📊 发现 {len(unique_currency)} 种货币数值")
        if unique_currency:
            print(f"  💰 最高金额: {unique_currency[0]:,}")
            print(f"  💰 前10个最高金额: {[f'{x:,}' for x in unique_currency[:10]]}")
        
        return unique_currency
        
    except Exception as e:
        print(f"❌ 分析游戏货币失败: {e}")
        return []

def analyze_item_restrictions():
    """分析物品限制"""
    print("🔍 分析物品限制...")
    
    try:
        with open("Uonline/Message.ctf", 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # 查找各种限制
        restrictions = {
            "24小时限制": len(re.findall(r'24小时', content)),
            "转让限制": len(re.findall(r'转让', content)),
            "交易限制": len(re.findall(r'交易', content)),
            "禁止": len(re.findall(r'禁止', content)),
            "限制": len(re.findall(r'限制', content)),
            "绑定": len(re.findall(r'绑定', content))
        }
        
        print(f"  📊 物品限制统计:")
        for restriction, count in restrictions.items():
            if count > 0:
                print(f"    - {restriction}: {count} 次")
        
        return restrictions
        
    except Exception as e:
        print(f"❌ 分析物品限制失败: {e}")
        return {}

def find_cheat_opportunities():
    """寻找作弊机会"""
    print("🔍 寻找可能的作弊机会...")
    
    opportunities = []
    
    # 基于之前的分析结果
    print("  💡 潜在的修改目标:")
    print("    1. 🎯 HP数值修改 - 将低HP改为高HP")
    print("    2. 💰 货币数值修改 - 修改游戏币数量")
    print("    3. 📈 成功率修改 - 将低成功率改为100%")
    print("    4. ⏰ 时间限制修改 - 绕过24小时限制")
    print("    5. 🔧 合成公式修改 - 简化合成材料")
    print("    6. 📦 物品属性修改 - 增强装备属性")
    
    return opportunities

def main():
    print("=" * 70)
    print("🎮 Uonline 游戏数据深度分析")
    print("🔍 发现有趣的游戏机制和修改机会")
    print("=" * 70)
    
    # 1. HP数值分析
    print("\n📊 第一部分: HP数值分析")
    print("-" * 50)
    hp_values = analyze_hp_values()
    
    # 2. 装备属性分析
    print("\n⚔️ 第二部分: 装备属性分析")
    print("-" * 50)
    equipment_stats = analyze_equipment_stats()
    
    # 3. 合成公式分析
    print("\n🔬 第三部分: 合成公式分析")
    print("-" * 50)
    formulas = analyze_synthesis_formulas()
    
    # 4. 成功率分析
    print("\n🎲 第四部分: 成功率分析")
    print("-" * 50)
    success_rates = analyze_success_rates()
    
    # 5. 游戏货币分析
    print("\n💰 第五部分: 游戏货币分析")
    print("-" * 50)
    currency = analyze_game_currency()
    
    # 6. 物品限制分析
    print("\n🔒 第六部分: 物品限制分析")
    print("-" * 50)
    restrictions = analyze_item_restrictions()
    
    # 7. 作弊机会分析
    print("\n🎯 第七部分: 作弊机会分析")
    print("-" * 50)
    opportunities = find_cheat_opportunities()
    
    # 总结
    print("\n" + "=" * 70)
    print("📊 发现总结")
    print("=" * 70)
    
    print(f"\n✅ 数据统计:")
    print(f"  - HP数值种类: {len(hp_values)}")
    print(f"  - 合成公式: {len(formulas)}")
    print(f"  - 成功率种类: {len(success_rates)}")
    print(f"  - 货币数值: {len(currency)}")
    
    print(f"\n🎯 最有价值的发现:")
    if hp_values:
        print(f"  🔥 最高HP值: {max(hp_values):,}")
    if currency:
        print(f"  💰 最高金额: {max(currency):,}")
    if success_rates:
        print(f"  📈 成功率范围: {min(success_rates)}% - {max(success_rates)}%")
    
    print(f"\n💡 修改建议:")
    print(f"  1. 🎮 修改Message.ctf中的数值")
    print(f"  2. 🔧 使用十六进制编辑器")
    print(f"  3. 🎯 重点修改HP、金币、成功率")
    print(f"  4. ⚠️ 备份原始文件")
    
    print(f"\n🚀 下一步行动:")
    print(f"  1. 创建数值修改工具")
    print(f"  2. 分析PAK文件结构")
    print(f"  3. 研究网络协议")
    print(f"  4. 测试修改效果")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
