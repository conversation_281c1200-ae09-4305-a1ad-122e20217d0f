#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动修改系统时间工具
使用Windows API直接修改时间
"""

import ctypes
import ctypes.wintypes
import sys
import time
from datetime import datetime, timedelta
import subprocess

# Windows API 结构体
class SYSTEMTIME(ctypes.Structure):
    _fields_ = [
        ('wYear', ctypes.wintypes.WORD),
        ('wMonth', ctypes.wintypes.WORD),
        ('wDayOfWeek', ctypes.wintypes.WORD),
        ('wDay', ctypes.wintypes.WORD),
        ('wHour', ctypes.wintypes.WORD),
        ('wMinute', ctypes.wintypes.WORD),
        ('wSecond', ctypes.wintypes.WORD),
        ('wMilliseconds', ctypes.wintypes.WORD),
    ]

def is_admin():
    """检查是否有管理员权限"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def run_as_admin():
    """以管理员身份重新运行"""
    try:
        ctypes.windll.shell32.ShellExecuteW(
            None, "runas", sys.executable, __file__, None, 1
        )
    except:
        print("❌ 无法获取管理员权限")
        return False
    return True

def get_system_time():
    """获取当前系统时间"""
    st = SYSTEMTIME()
    ctypes.windll.kernel32.GetSystemTime(ctypes.byref(st))
    return datetime(st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute, st.wSecond)

def set_system_time(target_datetime):
    """设置系统时间"""
    try:
        st = SYSTEMTIME()
        st.wYear = target_datetime.year
        st.wMonth = target_datetime.month
        st.wDay = target_datetime.day
        st.wHour = target_datetime.hour
        st.wMinute = target_datetime.minute
        st.wSecond = target_datetime.second
        st.wMilliseconds = 0
        
        # 设置系统时间
        result = ctypes.windll.kernel32.SetSystemTime(ctypes.byref(st))
        
        if result:
            print(f"✅ 系统时间已修改为: {target_datetime.strftime('%Y-%m-%d %H:%M:%S')}")
            return True
        else:
            print("❌ 修改系统时间失败")
            return False
            
    except Exception as e:
        print(f"❌ 修改时间出错: {e}")
        return False

def disable_time_sync():
    """禁用Windows时间同步"""
    try:
        # 停止Windows时间服务
        subprocess.run('net stop w32time', shell=True, capture_output=True)
        print("🔧 已禁用自动时间同步")
        return True
    except:
        print("⚠️ 禁用时间同步失败")
        return False

def enable_time_sync():
    """启用Windows时间同步"""
    try:
        # 启动Windows时间服务并同步
        subprocess.run('net start w32time', shell=True, capture_output=True)
        subprocess.run('w32tm /resync', shell=True, capture_output=True)
        print("🔄 已恢复自动时间同步")
        return True
    except:
        print("⚠️ 恢复时间同步失败")
        return False

def check_game_process():
    """检查游戏是否在运行"""
    try:
        result = subprocess.run('tasklist /FI "IMAGENAME eq Prmain.exe"', 
                              shell=True, capture_output=True, text=True)
        return "Prmain.exe" in result.stdout
    except:
        return False

def kill_game_process():
    """强制关闭游戏进程"""
    try:
        if check_game_process():
            print("🎮 检测到游戏正在运行，正在强制关闭...")
            subprocess.run('taskkill /F /IM Prmain.exe', shell=True, capture_output=True)
            time.sleep(2)
            
            if not check_game_process():
                print("✅ 游戏进程已关闭")
                return True
            else:
                print("❌ 无法关闭游戏进程，请手动关闭")
                return False
        else:
            print("✅ 游戏未运行")
            return True
    except Exception as e:
        print(f"❌ 关闭游戏失败: {e}")
        return False

def main():
    print("=" * 60)
    print("🚀 自动时间修改工具 - 24小时限制绕过")
    print("=" * 60)
    print()
    
    # 检查管理员权限
    if not is_admin():
        print("🔐 需要管理员权限来修改系统时间")
        print("🔄 正在请求管理员权限...")
        if run_as_admin():
            return
        else:
            print("❌ 无法获取管理员权限，程序退出")
            input("按回车键退出...")
            return
    
    print("✅ 已获得管理员权限")
    print()
    
    # 获取当前时间
    current_time = datetime.now()
    target_time = current_time + timedelta(hours=25)
    
    print(f"📅 当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 目标时间: {target_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    print("选择操作:")
    print("1. 🕐 自动修改时间到25小时后")
    print("2. 🔄 恢复正确时间")
    print("3. 🎯 完整测试流程")
    print("4. 🚪 退出")
    print()
    
    choice = input("请选择 (1-4): ").strip()
    
    if choice == "1":
        print("\n🕐 开始自动修改时间...")
        
        # 关闭游戏
        if not kill_game_process():
            print("⚠️ 请手动关闭游戏后重试")
            input("按回车键继续...")
            return
        
        # 禁用时间同步
        disable_time_sync()
        
        # 修改时间
        if set_system_time(target_time):
            print("🎉 时间修改成功!")
            print()
            print("📋 现在可以:")
            print("   1. 启动游戏")
            print("   2. 登录角色")
            print("   3. 测试24小时限制物品")
            print("   4. 记录测试结果")
            print()
            print("⚠️ 测试完成后请选择选项2恢复时间")
        else:
            print("❌ 时间修改失败")
    
    elif choice == "2":
        print("\n🔄 恢复正确时间...")
        
        # 启用时间同步
        enable_time_sync()
        
        # 等待同步
        print("⏳ 等待时间同步...")
        time.sleep(5)
        
        current = datetime.now()
        print(f"✅ 当前时间: {current.strftime('%Y-%m-%d %H:%M:%S')}")
        print("✅ 时间已恢复正常")
    
    elif choice == "3":
        print("\n🎯 开始完整测试流程...")
        print()
        
        # 步骤1: 关闭游戏
        print("📋 步骤1: 关闭游戏进程")
        if not kill_game_process():
            print("❌ 无法自动关闭游戏，请手动关闭")
            input("关闭游戏后按回车继续...")
        
        # 步骤2: 禁用时间同步
        print("\n📋 步骤2: 禁用自动时间同步")
        disable_time_sync()
        
        # 步骤3: 修改时间
        print("\n📋 步骤3: 修改系统时间")
        if set_system_time(target_time):
            print("✅ 时间修改成功!")
            print()
            print("🎮 现在请进行测试:")
            print("   1. 启动游戏")
            print("   2. 登录角色")
            print("   3. 找到有'24小时限制'的物品")
            print("   4. 尝试使用该物品")
            print("   5. 记录结果:")
            print("      ✅ 可以使用 = 绕过成功 (客户端验证)")
            print("      ❌ 仍然限制 = 绕过失败 (服务器验证)")
            print()
            
            input("测试完成后按回车恢复时间...")
            
            # 步骤4: 恢复时间
            print("\n📋 步骤4: 恢复系统时间")
            enable_time_sync()
            time.sleep(5)
            
            final_time = datetime.now()
            print(f"✅ 时间已恢复: {final_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print()
            print("📊 请报告测试结果:")
            print("🟢 如果绕过成功 = 我的逆向分析正确，timeGetTime用于客户端验证")
            print("🔴 如果绕过失败 = 主要是服务器验证，需要其他方法")
        else:
            print("❌ 时间修改失败，测试中止")
    
    elif choice == "4":
        print("\n👋 程序退出")
        return
    
    else:
        print("❌ 无效选择")
    
    print()
    input("按回车键退出...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n🛑 用户中断")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
        input("按回车键退出...")
