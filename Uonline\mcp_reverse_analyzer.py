#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCP增强逆向分析工具
利用MCP进行深度二进制分析
"""

import asyncio
import json
import sys
from pathlib import Path
import subprocess

# MCP相关导入
try:
    from mcp import ClientSession, StdioServerParameters
    from mcp.client.stdio import stdio_client
    MCP_AVAILABLE = True
    print("✅ MCP模块加载成功")
except ImportError as e:
    MCP_AVAILABLE = False
    print(f"❌ MCP模块加载失败: {e}")

class MCPReverseAnalyzer:
    def __init__(self):
        self.session = None
        self.available_tools = []
    
    async def connect_to_ida_mcp(self):
        """连接到IDA Pro MCP服务器"""
        try:
            # IDA Pro MCP服务器参数
            server_params = StdioServerParameters(
                command="ida-pro-mcp",
                args=["--stdio"]
            )
            
            async with stdio_client(server_params) as (read, write):
                async with ClientSession(read, write) as session:
                    self.session = session
                    
                    # 获取可用工具
                    tools_result = await session.list_tools()
                    self.available_tools = [tool.name for tool in tools_result.tools]
                    
                    print("🔗 成功连接到IDA Pro MCP服务器")
                    print(f"📋 可用工具: {', '.join(self.available_tools)}")
                    
                    return True
        except Exception as e:
            print(f"❌ 连接IDA Pro MCP失败: {e}")
            return False
    
    async def analyze_pe_with_ida(self, filename):
        """使用IDA Pro分析PE文件"""
        if not self.session:
            print("❌ 未连接到MCP服务器")
            return None
        
        try:
            # 调用IDA Pro分析工具
            result = await self.session.call_tool(
                "analyze_binary",
                arguments={
                    "file_path": str(Path(filename).absolute()),
                    "analysis_type": "full",
                    "output_format": "json"
                }
            )
            
            return result.content
        except Exception as e:
            print(f"❌ IDA Pro分析失败: {e}")
            return None
    
    async def find_time_validation_functions(self, filename):
        """查找时间验证相关函数"""
        if not self.session:
            print("❌ 未连接到MCP服务器")
            return None
        
        try:
            # 搜索时间相关函数
            result = await self.session.call_tool(
                "search_functions",
                arguments={
                    "file_path": str(Path(filename).absolute()),
                    "keywords": ["time", "hour", "limit", "restrict", "valid", "check"],
                    "api_calls": ["timeGetTime", "GetSystemTime", "GetLocalTime"]
                }
            )
            
            return result.content
        except Exception as e:
            print(f"❌ 函数搜索失败: {e}")
            return None
    
    async def analyze_network_communication(self, filename):
        """分析网络通信模式"""
        if not self.session:
            print("❌ 未连接到MCP服务器")
            return None
        
        try:
            result = await self.session.call_tool(
                "analyze_network",
                arguments={
                    "file_path": str(Path(filename).absolute()),
                    "search_patterns": ["socket", "send", "recv", "connect", "WSA"]
                }
            )
            
            return result.content
        except Exception as e:
            print(f"❌ 网络分析失败: {e}")
            return None

def check_mcp_servers():
    """检查可用的MCP服务器"""
    print("🔍 检查MCP服务器...")
    
    # 检查IDA Pro MCP
    try:
        result = subprocess.run(
            ["ida-pro-mcp", "--help"],
            capture_output=True,
            text=True,
            timeout=10
        )
        if result.returncode == 0:
            print("✅ IDA Pro MCP服务器可用")
            return True
    except:
        pass
    
    # 检查其他可能的MCP服务器
    servers = ["ghidra-mcp", "radare2-mcp", "x64dbg-mcp"]
    available_servers = []
    
    for server in servers:
        try:
            result = subprocess.run(
                [server, "--version"],
                capture_output=True,
                text=True,
                timeout=5
            )
            if result.returncode == 0:
                available_servers.append(server)
                print(f"✅ {server} 可用")
        except:
            print(f"❌ {server} 不可用")
    
    return len(available_servers) > 0

async def main():
    print("=" * 60)
    print("🔬 MCP增强逆向分析工具")
    print("=" * 60)
    print()
    
    if not MCP_AVAILABLE:
        print("❌ MCP不可用，回退到基础分析模式")
        print("📋 请确保已正确安装MCP:")
        print("   pip install mcp anthropic-mcp")
        return
    
    # 检查MCP服务器
    if not check_mcp_servers():
        print("⚠️ 未找到可用的MCP逆向工具服务器")
        print("📋 可能需要配置以下工具的MCP集成:")
        print("   - IDA Pro MCP插件")
        print("   - Ghidra MCP脚本")
        print("   - Radare2 MCP接口")
        print()
        print("🔄 尝试基础MCP功能...")
    
    # 创建分析器
    analyzer = MCPReverseAnalyzer()
    
    # 分析目标文件
    target_files = ["Prmain.exe", "Uonline.dll"]
    
    for filename in target_files:
        if Path(filename).exists():
            print(f"\n🎯 分析 {filename}...")
            print("-" * 40)
            
            # 尝试连接IDA Pro MCP
            if await analyzer.connect_to_ida_mcp():
                print("🚀 使用IDA Pro进行深度分析...")
                
                # PE结构分析
                pe_analysis = await analyzer.analyze_pe_with_ida(filename)
                if pe_analysis:
                    print("✅ PE结构分析完成")
                    print(json.dumps(pe_analysis, indent=2)[:500] + "...")
                
                # 时间验证函数分析
                time_functions = await analyzer.find_time_validation_functions(filename)
                if time_functions:
                    print("✅ 时间验证函数分析完成")
                    print(json.dumps(time_functions, indent=2)[:500] + "...")
                
                # 网络通信分析
                network_analysis = await analyzer.analyze_network_communication(filename)
                if network_analysis:
                    print("✅ 网络通信分析完成")
                    print(json.dumps(network_analysis, indent=2)[:500] + "...")
            
            else:
                print("⚠️ IDA Pro MCP不可用，使用基础分析...")
                # 这里可以添加基础分析代码
                print("📋 建议手动使用IDA Pro分析以下内容:")
                print("   1. 搜索字符串: 'timeGetTime', '24小时', 'limit'")
                print("   2. 分析导入表中的时间相关API")
                print("   3. 查找网络通信函数")
                print("   4. 分析数据段中的时间常量")
        
        else:
            print(f"❌ 文件不存在: {filename}")
    
    print("\n" + "="*60)
    print("🎯 MCP分析完成")
    
    if MCP_AVAILABLE:
        print("💡 如果需要更深入的分析，请确保:")
        print("   1. IDA Pro已安装并配置MCP插件")
        print("   2. 相关逆向工具已连接到MCP")
        print("   3. 具有足够的系统权限")
    
    print("="*60)

def run_basic_analysis():
    """运行基础分析（无MCP）"""
    print("🔧 运行基础分析模式...")
    
    target_files = ["Prmain.exe", "Uonline.dll"]
    
    for filename in target_files:
        if Path(filename).exists():
            print(f"\n📁 基础分析 {filename}:")
            
            # 文件大小
            size = Path(filename).stat().st_size
            print(f"   文件大小: {size:,} 字节")
            
            # 简单字符串搜索
            try:
                with open(filename, 'rb') as f:
                    data = f.read()
                
                # 搜索关键字符串
                keywords = [b'timeGetTime', b'24', b'hour', b'limit']
                for keyword in keywords:
                    count = data.count(keyword)
                    if count > 0:
                        print(f"   发现 '{keyword.decode('ascii', errors='ignore')}': {count}次")
            
            except Exception as e:
                print(f"   ❌ 分析失败: {e}")

if __name__ == "__main__":
    try:
        if MCP_AVAILABLE:
            asyncio.run(main())
        else:
            run_basic_analysis()
        
        input("\n按回车键退出...")
    
    except KeyboardInterrupt:
        print("\n🛑 用户中断")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
        input("按回车键退出...")
