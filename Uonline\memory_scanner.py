#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实际内存扫描器 - 找到游戏中的具体数值和地址
"""

import struct
import os
import re

def find_server_ip_in_binary():
    """在二进制文件中找到服务器IP的确切位置"""
    print("🔍 查找服务器IP ************** 的二进制位置...")
    
    # IP转换为字节
    ip_bytes = struct.pack('BBBB', 121, 40, 205, 138)
    ip_bytes_reversed = struct.pack('BBBB', 138, 205, 40, 121)  # 小端序
    
    files = ['Prmain.exe', 'Uonline.dll']
    
    for filename in files:
        if os.path.exists(filename):
            with open(filename, 'rb') as f:
                data = f.read()
            
            # 查找正序IP
            pos = data.find(ip_bytes)
            if pos != -1:
                print(f"  {filename}: 找到IP (正序) 在偏移 0x{pos:08X}")
                # 显示周围的数据
                start = max(0, pos - 16)
                end = min(len(data), pos + 20)
                context = data[start:end]
                print(f"    上下文: {context.hex()}")
            
            # 查找反序IP
            pos = data.find(ip_bytes_reversed)
            if pos != -1:
                print(f"  {filename}: 找到IP (小端序) 在偏移 0x{pos:08X}")
                start = max(0, pos - 16)
                end = min(len(data), pos + 20)
                context = data[start:end]
                print(f"    上下文: {context.hex()}")

def find_port_4001():
    """查找端口4001的位置"""
    print("\n🔌 查找端口4001的位置...")
    
    port_big = struct.pack('>H', 4001)    # 大端序 0x0FA1
    port_little = struct.pack('<H', 4001) # 小端序 0xA10F
    
    files = ['Prmain.exe', 'Uonline.dll']
    
    for filename in files:
        if os.path.exists(filename):
            with open(filename, 'rb') as f:
                data = f.read()
            
            # 查找大端序端口
            offset = 0
            count = 0
            while True:
                pos = data.find(port_big, offset)
                if pos == -1:
                    break
                print(f"  {filename}: 端口4001 (大端) 在 0x{pos:08X}")
                offset = pos + 1
                count += 1
                if count > 10:  # 限制输出
                    break
            
            # 查找小端序端口
            offset = 0
            count = 0
            while True:
                pos = data.find(port_little, offset)
                if pos == -1:
                    break
                print(f"  {filename}: 端口4001 (小端) 在 0x{pos:08X}")
                offset = pos + 1
                count += 1
                if count > 10:
                    break

def find_numeric_constants():
    """查找可能的游戏数值常量"""
    print("\n🎯 查找游戏数值常量...")
    
    # 常见的游戏数值
    values = [
        (1000, "1000 (可能是金币/经验)"),
        (100, "100 (可能是HP/MP)"),
        (50, "50 (可能是等级上限)"),
        (9999, "9999 (可能是最大值)"),
        (255, "255 (可能是属性上限)"),
    ]
    
    files = ['Prmain.exe']
    
    for filename in files:
        if os.path.exists(filename):
            with open(filename, 'rb') as f:
                data = f.read()
            
            print(f"\n  分析 {filename}:")
            
            for value, desc in values:
                # 32位小端序
                value_bytes = struct.pack('<I', value)
                count = 0
                offset = 0
                
                while count < 5:  # 只显示前5个
                    pos = data.find(value_bytes, offset)
                    if pos == -1:
                        break
                    print(f"    0x{pos:08X}: {desc}")
                    offset = pos + 1
                    count += 1

def find_string_addresses():
    """查找字符串的内存地址引用"""
    print("\n📝 查找字符串地址引用...")
    
    target_strings = [
        b"**************",
        b"4001",
        b"www.t2uu.com",
        b"Uonline",
        b"netmine"
    ]
    
    files = ['Prmain.exe']
    
    for filename in files:
        if os.path.exists(filename):
            with open(filename, 'rb') as f:
                data = f.read()
            
            print(f"\n  分析 {filename}:")
            
            for target in target_strings:
                pos = data.find(target)
                if pos != -1:
                    print(f"    字符串 '{target.decode('ascii', errors='ignore')}' 在 0x{pos:08X}")
                    
                    # 查找指向这个字符串的指针 (假设基址0x400000)
                    string_addr = 0x400000 + pos
                    addr_bytes = struct.pack('<I', string_addr)
                    
                    ref_pos = data.find(addr_bytes)
                    if ref_pos != -1:
                        print(f"      -> 被引用在 0x{ref_pos:08X} (指针值: 0x{string_addr:08X})")

def analyze_pe_imports():
    """分析PE导入表，找到实际的API调用"""
    print("\n📚 分析PE导入表...")
    
    filename = 'Prmain.exe'
    if not os.path.exists(filename):
        return
    
    try:
        with open(filename, 'rb') as f:
            # 读取DOS头
            f.seek(0x3C)
            pe_offset = struct.unpack('<I', f.read(4))[0]
            
            # 跳到PE头
            f.seek(pe_offset + 4)  # 跳过PE签名
            
            # 读取COFF头
            coff_header = f.read(20)
            sections_count = struct.unpack('<H', coff_header[2:4])[0]
            opt_header_size = struct.unpack('<H', coff_header[16:18])[0]
            
            # 跳过可选头到节表
            f.seek(pe_offset + 24 + opt_header_size)
            
            print(f"  PE文件有 {sections_count} 个节:")
            
            for i in range(sections_count):
                section_header = f.read(40)
                if len(section_header) < 40:
                    break
                
                name = section_header[:8].rstrip(b'\x00').decode('ascii', errors='ignore')
                virtual_size = struct.unpack('<I', section_header[8:12])[0]
                virtual_addr = struct.unpack('<I', section_header[12:16])[0]
                raw_size = struct.unpack('<I', section_header[16:20])[0]
                raw_addr = struct.unpack('<I', section_header[20:24])[0]
                
                print(f"    {name}: VA=0x{virtual_addr:08X}, Size=0x{virtual_size:08X}")
                
                if name == '.rdata' or name == '.idata':
                    print(f"      -> 可能包含导入表")
                    
    except Exception as e:
        print(f"  分析失败: {e}")

def find_function_calls():
    """查找函数调用模式"""
    print("\n🎯 查找函数调用模式...")
    
    filename = 'Prmain.exe'
    if not os.path.exists(filename):
        return
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    # 查找CALL指令 (E8 xx xx xx xx)
    call_count = 0
    for i in range(len(data) - 4):
        if data[i] == 0xE8:  # CALL relative
            rel_addr = struct.unpack('<i', data[i+1:i+5])[0]
            target_addr = i + 5 + rel_addr
            
            if 0 <= target_addr < len(data):
                call_count += 1
                if call_count <= 10:  # 只显示前10个
                    print(f"    CALL 0x{i:08X} -> 0x{target_addr:08X}")
    
    print(f"  总共找到 {call_count} 个CALL指令")

def main():
    print("=" * 60)
    print("🎯 Uonline 实际内存分析")
    print("=" * 60)
    
    # 查找服务器IP
    find_server_ip_in_binary()
    
    # 查找端口
    find_port_4001()
    
    # 查找数值常量
    find_numeric_constants()
    
    # 查找字符串地址
    find_string_addresses()
    
    # 分析PE结构
    analyze_pe_imports()
    
    # 查找函数调用
    find_function_calls()
    
    print(f"\n" + "=" * 60)
    print("✅ 实际分析完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
