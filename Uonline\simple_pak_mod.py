#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import shutil

def main():
    print("简单PAK修改器")
    
    pak_file = "item0001.pak"
    
    if not os.path.exists(pak_file):
        print(f"文件不存在: {pak_file}")
        return
        
    # 备份
    backup = pak_file + ".backup_simple"
    shutil.copy2(pak_file, backup)
    print(f"备份创建: {backup}")
    
    # 读取文件
    with open(pak_file, 'rb') as f:
        data = f.read()
        
    print(f"文件大小: {len(data)} 字节")
    
    # 查找HP+
    hp_count = data.count(b'HP+')
    print(f"找到 {hp_count} 个HP+")
    
    # 简单修改：将所有HP+100替换为HP+999
    new_data = data.replace(b'HP+100', b'HP+999')
    
    changes = len(data) - len(new_data.replace(b'HP+999', b'HP+100'))
    print(f"进行了 {changes} 处修改")
    
    # 写入
    with open(pak_file, 'wb') as f:
        f.write(new_data)
        
    print("修改完成")

if __name__ == "__main__":
    main()
