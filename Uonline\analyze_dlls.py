#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析Uonline目录中的DLL文件和其他关键文件
"""

import os
import struct

def analyze_file_strings(filename, max_strings=20):
    """分析文件中的字符串"""
    if not os.path.exists(filename):
        return []
    
    try:
        with open(filename, 'rb') as f:
            data = f.read()
        
        strings = []
        current = ""
        
        for byte in data:
            if 32 <= byte <= 126:  # 可打印ASCII
                current += chr(byte)
            else:
                if len(current) >= 4:
                    strings.append(current)
                current = ""
        
        if len(current) >= 4:
            strings.append(current)
        
        # 过滤有意义的字符串
        meaningful = []
        for s in strings:
            if len(s) >= 6 and not s.isdigit():
                meaningful.append(s)
        
        return meaningful[:max_strings]
        
    except Exception as e:
        print(f"分析 {filename} 失败: {e}")
        return []

def analyze_version_dat():
    """分析version.dat文件"""
    print("🔍 分析 version.dat...")
    
    if not os.path.exists('version.dat'):
        print("❌ version.dat 不存在")
        return
    
    try:
        with open('version.dat', 'rb') as f:
            data = f.read()
        
        print(f"文件大小: {len(data)} 字节")
        
        # 尝试解析为文本
        try:
            text = data.decode('utf-8', errors='ignore')
            if text.strip():
                print(f"版本信息: {text.strip()}")
        except:
            pass
        
        # 显示十六进制
        if len(data) <= 100:
            hex_data = ' '.join(f'{b:02X}' for b in data)
            print(f"十六进制: {hex_data}")
        
    except Exception as e:
        print(f"分析失败: {e}")

def analyze_crash_log():
    """分析crash.log文件"""
    print("\n🔍 分析 crash.log...")
    
    if not os.path.exists('crash.log'):
        print("❌ crash.log 不存在")
        return
    
    try:
        with open('crash.log', 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        lines = content.split('\n')
        print(f"崩溃日志 ({len(lines)} 行):")
        
        for i, line in enumerate(lines[:20]):
            if line.strip():
                print(f"  {i+1}: {line.strip()}")
        
    except Exception as e:
        print(f"分析失败: {e}")

def analyze_key_dlls():
    """分析关键DLL文件"""
    print("\n🔍 分析关键DLL文件...")
    
    dlls = ['Uonline.dll', 'netmine.dll', 'GrpLib.dll', 'soundlib.dll', 'white.dll']
    
    for dll in dlls:
        if os.path.exists(dll):
            print(f"\n📚 {dll}:")
            size = os.path.getsize(dll)
            print(f"  大小: {size:,} 字节")
            
            strings = analyze_file_strings(dll, 10)
            if strings:
                print("  关键字符串:")
                for s in strings:
                    print(f"    {s}")
        else:
            print(f"\n❌ {dll} 不存在")

def analyze_urls():
    """分析URL文件"""
    print("\n🔍 分析URL文件...")
    
    url_files = ['天之游侠官方网站.url', '天之游侠帐号注册.url', '天之游侠帐户充值.url']
    
    for url_file in url_files:
        if os.path.exists(url_file):
            try:
                with open(url_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                print(f"\n🌐 {url_file}:")
                lines = content.split('\n')
                for line in lines:
                    if 'URL=' in line or 'http' in line.lower():
                        print(f"  {line.strip()}")
                        
            except Exception as e:
                print(f"读取 {url_file} 失败: {e}")

def analyze_backup():
    """分析备份目录"""
    print("\n🔍 分析备份目录...")
    
    backup_dir = 'backup_20250725_081900'
    if os.path.exists(backup_dir):
        print(f"📁 发现备份目录: {backup_dir}")
        
        # 分析备份的connection.ini
        backup_conn = os.path.join(backup_dir, 'connection.ini')
        if os.path.exists(backup_conn):
            try:
                with open(backup_conn, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                print("备份的连接配置:")
                for line in content.split('\n'):
                    if line.strip():
                        print(f"  {line.strip()}")
            except Exception as e:
                print(f"读取备份配置失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 Uonline 关键文件分析")
    print("=" * 60)
    
    # 分析版本文件
    analyze_version_dat()
    
    # 分析崩溃日志
    analyze_crash_log()
    
    # 分析DLL文件
    analyze_key_dlls()
    
    # 分析URL文件
    analyze_urls()
    
    # 分析备份
    analyze_backup()
    
    print(f"\n" + "=" * 60)
    print("✅ 关键文件分析完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
