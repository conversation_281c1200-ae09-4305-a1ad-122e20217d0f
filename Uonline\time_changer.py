#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天之游侠 24小时限制绕过工具
自动修改系统时间进行测试
"""

import os
import sys
import time
import subprocess
from datetime import datetime, timedelta
import ctypes

def is_admin():
    """检查是否有管理员权限"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def run_as_admin():
    """以管理员身份重新运行"""
    if is_admin():
        return True
    else:
        print("🔐 需要管理员权限来修改系统时间...")
        print("🔄 正在请求管理员权限...")
        try:
            ctypes.windll.shell32.ShellExecuteW(
                None, "runas", sys.executable, " ".join(sys.argv), None, 1
            )
            return False
        except:
            print("❌ 无法获取管理员权限")
            return False

def get_current_time():
    """获取当前系统时间"""
    return datetime.now()

def set_system_time(target_time):
    """设置系统时间"""
    try:
        # 格式化时间字符串
        date_str = target_time.strftime("%m-%d-%Y")
        time_str = target_time.strftime("%H:%M:%S")
        
        # 设置日期
        date_cmd = f'date {date_str}'
        result1 = subprocess.run(date_cmd, shell=True, capture_output=True, text=True)
        
        # 设置时间
        time_cmd = f'time {time_str}'
        result2 = subprocess.run(time_cmd, shell=True, capture_output=True, text=True)
        
        if result1.returncode == 0 and result2.returncode == 0:
            print(f"✅ 系统时间已设置为: {target_time.strftime('%Y-%m-%d %H:%M:%S')}")
            return True
        else:
            print(f"❌ 设置时间失败")
            print(f"Date命令结果: {result1.stderr}")
            print(f"Time命令结果: {result2.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 设置时间出错: {e}")
        return False

def disable_time_sync():
    """禁用自动时间同步"""
    try:
        cmd = 'w32tm /unregister'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        print("🔧 已禁用自动时间同步")
        return True
    except Exception as e:
        print(f"⚠️ 禁用时间同步失败: {e}")
        return False

def enable_time_sync():
    """启用自动时间同步"""
    try:
        cmd1 = 'w32tm /register'
        cmd2 = 'w32tm /resync'
        subprocess.run(cmd1, shell=True, capture_output=True, text=True)
        subprocess.run(cmd2, shell=True, capture_output=True, text=True)
        print("🔄 已恢复自动时间同步")
        return True
    except Exception as e:
        print(f"⚠️ 恢复时间同步失败: {e}")
        return False

def check_game_process():
    """检查游戏是否在运行"""
    try:
        result = subprocess.run('tasklist /FI "IMAGENAME eq Prmain.exe"', 
                              shell=True, capture_output=True, text=True)
        if "Prmain.exe" in result.stdout:
            return True
        return False
    except:
        return False

def kill_game_process():
    """结束游戏进程"""
    try:
        if check_game_process():
            print("🎮 检测到游戏正在运行，正在关闭...")
            subprocess.run('taskkill /F /IM Prmain.exe', shell=True, capture_output=True)
            time.sleep(2)
            if not check_game_process():
                print("✅ 游戏进程已关闭")
                return True
            else:
                print("❌ 无法关闭游戏进程")
                return False
        return True
    except Exception as e:
        print(f"❌ 关闭游戏进程失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🎮 天之游侠 24小时限制绕过测试工具")
    print("=" * 60)
    print()
    
    # 检查管理员权限
    if not is_admin():
        print("❌ 需要管理员权限来修改系统时间")
        if not run_as_admin():
            input("按回车键退出...")
            return
        else:
            return
    
    print("✅ 已获得管理员权限")
    print()
    
    # 记录原始时间
    original_time = get_current_time()
    print(f"📅 当前系统时间: {original_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    print("选择操作:")
    print("1. 🕐 设置时间到25小时后 (测试绕过)")
    print("2. 🔄 恢复原始时间")
    print("3. 🎯 自动测试流程")
    print("4. 🚪 退出")
    print()
    
    choice = input("请选择 (1-4): ").strip()
    
    if choice == "1":
        print("\n🕐 设置时间到25小时后...")
        
        # 检查并关闭游戏
        if not kill_game_process():
            print("⚠️ 请手动关闭游戏后重试")
            input("按回车键继续...")
            return
        
        # 禁用时间同步
        disable_time_sync()
        
        # 计算目标时间
        target_time = original_time + timedelta(hours=25)
        
        if set_system_time(target_time):
            print("✅ 时间修改成功!")
            print("📋 现在可以:")
            print("   1. 启动游戏")
            print("   2. 登录角色")
            print("   3. 测试24小时限制物品")
            print("   4. 测试完成后选择选项2恢复时间")
        else:
            print("❌ 时间修改失败")
    
    elif choice == "2":
        print("\n🔄 恢复原始时间...")
        
        # 恢复时间同步
        enable_time_sync()
        
        # 等待同步
        print("⏳ 等待时间同步...")
        time.sleep(3)
        
        current = get_current_time()
        print(f"✅ 当前时间: {current.strftime('%Y-%m-%d %H:%M:%S')}")
        print("✅ 时间已恢复")
    
    elif choice == "3":
        print("\n🎯 自动测试流程...")
        print("📋 测试步骤:")
        print("1. 关闭游戏")
        print("2. 修改时间到25小时后")
        print("3. 等待用户测试游戏")
        print("4. 恢复时间")
        print()
        
        confirm = input("确认开始自动测试? (y/N): ").strip().lower()
        if confirm == 'y':
            # 步骤1: 关闭游戏
            print("\n🎮 步骤1: 关闭游戏...")
            if not kill_game_process():
                print("⚠️ 请手动关闭游戏")
                input("关闭游戏后按回车继续...")
            
            # 步骤2: 修改时间
            print("\n🕐 步骤2: 修改时间...")
            disable_time_sync()
            target_time = original_time + timedelta(hours=25)
            
            if set_system_time(target_time):
                print("✅ 时间修改成功!")
                print(f"📅 新时间: {target_time.strftime('%Y-%m-%d %H:%M:%S')}")
                print()
                print("🎮 现在请:")
                print("   1. 启动游戏")
                print("   2. 登录角色") 
                print("   3. 测试有24小时限制的物品")
                print("   4. 记录测试结果")
                print()
                
                input("测试完成后按回车恢复时间...")
                
                # 步骤3: 恢复时间
                print("\n🔄 步骤3: 恢复时间...")
                enable_time_sync()
                time.sleep(3)
                
                current = get_current_time()
                print(f"✅ 时间已恢复: {current.strftime('%Y-%m-%d %H:%M:%S')}")
                print()
                print("📊 请报告测试结果:")
                print("✅ 如果限制被绕过 = 客户端验证，方法有效")
                print("❌ 如果限制仍存在 = 服务器验证，需要其他方法")
            else:
                print("❌ 时间修改失败")
    
    elif choice == "4":
        print("\n👋 退出程序")
        return
    
    else:
        print("❌ 无效选择")
    
    print()
    input("按回车键退出...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n🛑 用户中断")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
        input("按回车键退出...")
