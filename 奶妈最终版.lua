_Hook技能发包代码()

-- 初始化全局变量
g_攻击距离 = g_攻击距离 or 17.0
远程检测玩家距离 = 远程检测玩家距离 or 30
g_血量低于多少回复 = g_血量低于多少回复 or 2000
local g_检测到玩家蹲下 = false

-- 挂机点坐标
local 挂机点x = 0
local 挂机点y = 0
local 挂机点z = 0

-- 物品白名单配置
local 物品白名单 = {
    "枪",
    "圣经",
    "宝箱",
    "血色",
    "剑",
 "刀",  "石","宝箱", -- 可以继续添加更多物品前缀
}

-- 玩家白名单配置
local WHITELIST = {
    "龙DE传人",
    "寒冰",
    "神说",
    "老司机",
    "光辉",
}

-- 配置参数
local CONFIG = {
    最大追踪时间 = 50,
    连续掉血阈值 = 3,
    血量保护比例 = 0.5,
    攻击冷却时间 = 0.01,
    后退距离 = 14.0,
    检查间隔 = 500,
    位置记录间隔 = 2,
    最大卡住时间 = 5,
    输出等级 = 2,
    BUFF剩余时间 = 30,
    最小战斗距离 = 14.0,
    理想战斗距离 = 16.0,
    捡物范围 = 4.0,
}

-- 【新增】卡住检测系统 - 3分钟停留检测
local 卡住检测 = {
    上次位置 = {x = 0, y = 0},
    开始时间 = 0,
    检测半径 = 2.0,  -- 2米范围内算同一位置
    最大停留时间 = 180  -- 3分钟 = 180秒
}

-- 【新增】卡住检测函数
local function 检测长时间停留(role)
    local 当前时间 = os.time()
    local 当前距离 = _取坐标距离(role.x, role.y, 卡住检测.上次位置.x, 卡住检测.上次位置.y)

    -- 如果移动距离超过检测半径，说明有移动，重置计时器
    if 当前距离 > 卡住检测.检测半径 then
        卡住检测.上次位置.x = role.x
        卡住检测.上次位置.y = role.y
        卡住检测.开始时间 = 当前时间
        return false
    end

    -- 如果是第一次记录位置
    if 卡住检测.开始时间 == 0 then
        卡住检测.上次位置.x = role.x
        卡住检测.上次位置.y = role.y
        卡住检测.开始时间 = 当前时间
        return false
    end

    -- 检查是否超过最大停留时间
    local 停留时间 = 当前时间 - 卡住检测.开始时间
    if 停留时间 >= 卡住检测.最大停留时间 then
        _Log(string.format("检测到长时间停留: %.0f秒，需要换目标或换方向", 停留时间))
        -- 重置计时器
        卡住检测.开始时间 = 当前时间
        return true
    end

    return false
end

-- 【新增】多方向后退尝试函数
local function 尝试多方向后退(role, monster, nearby_monsters, 安全距离)
    -- 原来的后退方向
    local dx = role.x - monster.x
    local dy = role.y - monster.y
    local dist = math.sqrt(dx * dx + dy * dy)

    if dist > 0 then
        dx = dx / dist
        dy = dy / dist

        -- 尝试3个方向：正后、左后、右后
        local 方向列表 = {
            {dx = dx, dy = dy, 名称 = "正后方"},
            {dx = dx - 0.5, dy = dy, 名称 = "左后方"},
            {dx = dx + 0.5, dy = dy, 名称 = "右后方"}
        }

        for _, 方向 in ipairs(方向列表) do
            local target_x = monster.x + 方向.dx * g_攻击距离
            local target_y = monster.y + 方向.dy * g_攻击距离

            -- 检查这个方向是否安全
            local is_safe = true
            if nearby_monsters then
                for _, m in ipairs(nearby_monsters) do
                    if m.Id ~= monster.Id then
                        local back_dist = _取坐标距离(target_x, target_y, m.x, m.y)
                        if back_dist < 安全距离 then
                            is_safe = false
                            break
                        end
                    end
                end
            end

            if is_safe then
                _Log(string.format("多方向后退: 选择%s", 方向.名称))
                return target_x, target_y, role.z
            end
        end
    end

    return nil
end

-- 脚本全状态机
g_State = {
    Funs = {},
    Params = {},
    FunName = "",
    MonsterObj = nil,
    Run = function()
        return g_State.Funs[g_State.FunName](g_State.Params[g_State.FunName])
    end,
    Create = function()
        while true do
            _Log("-------------------------------------- 当前运行状态：" .. g_State.FunName)
            g_State.FunName = g_State.Run()
            local result = _意外处理(_取人物信息())
            if result then
                g_State.FunName = result
                g_State.MonsterObj = nil
            end
            if g_State.Funs[g_State.FunName] == nil then
                break
            end
            _Sleep(100)
        end
    end
}
-- 检测函数
function 检测附近玩家()
    local role = _取人物信息()
    local players = _取周围玩家信息()
    if players and role then
        for _, player in ipairs(players) do
            local is_whitelisted = false
            for _, whitelist_name in ipairs(WHITELIST) do
                if player.Name == whitelist_name then
                    is_whitelisted = true
                    break
                end
            end

            if not is_whitelisted and player.Id ~= role.Id then
                local distance = _取坐标距离(role.x, role.y, player.x, player.y)
                if distance < 远程检测玩家距离 then
                    _Log(string.format("检测到非白名单玩家 %s，距离 %.2f", player.Name, distance))
                    return true
                end
            end
        end
    end
    return false
end

function _取范围内最近怪物()
    local role = _取人物信息()
    local monster = _取周围怪物信息()
    if monster ~= nil and role.Die == false and role.x ~= nil then
        local Len = #monster
        for i = 1,Len do
            monster[i].juli = _取坐标距离(role.x,role.y,monster[i].x,monster[i].y)
        end

        table.sort(monster, function(a, b) return a.juli < b.juli end)

        for _, m in ipairs(monster) do
            if not g_指定怪物 or g_指定怪物 == "" then
                if _取坐标距离(挂机点x,挂机点y,m.x,m.y) <= tonumber(g_挂机范围) then
                    _Log(string.format("范围内最近怪物 -> 距离=%.2f, 名称=%s, ID=%08X", m.juli, m.Name, m.Id))
                    return m
                end
            elseif m.Name == g_指定怪物 then
                if _取坐标距离(挂机点x,挂机点y,m.x,m.y) <= tonumber(g_挂机范围) then
                    _Log(string.format("范围内指定怪物 -> 距离=%.2f, 名称=%s, ID=%08X", m.juli, m.Name, m.Id))
                    return m
                end
            end
        end
    end
    return nil
end

function _取范围内地面物品()
    local role = _取人物信息()
    local items = _取地面物品信息()

    -- 确保角色信息和物品信息都存在
    if not items or not role or role.Die or not role.x then
        return nil
    end

    -- 遍历所有物品
    for _, item in ipairs(items) do
        -- 计算物品与角色的距离
        local distance = _取坐标距离(role.x, role.y, item.x, item.y)

        -- 检查物品是否在挂机范围内
        if _取坐标距离(挂机点x, 挂机点y, item.x, item.y) <= tonumber(g_挂机范围) then
            -- 检查是否匹配白名单中的任意前缀
            for _, prefix in ipairs(物品白名单) do
                if string.find(item.Name, prefix) then
                    _Log(string.format("发现物品: %s, 距离: %.2f", item.Name, distance))
                    return item
                end
            end
        end
    end
    return nil
end

function _更新怪物属性(_怪)
    if not _怪 then return nil end

    local tb = {}
    local offset = 2632 + 264

    tb.Hp = _R4(_怪.Obj + offset)
    if tb.Hp > 0 then
        tb.HpM = _R4(_怪.Obj + offset + 4)
        tb.x = _Rf(_怪.Obj + 8)
        tb.z = _Rf(_怪.Obj + 12)
        tb.y = _Rf(_怪.Obj + 16)
        tb.Obj = _怪.Obj
        tb.Id = _怪.Id
        tb.Name = _怪.Name

        _Log(string.format("更新怪物属性 -> ID=%08X, 名称=%s, HP=%d/%d", tb.Id, tb.Name, tb.Hp, tb.HpM))
        return tb
    end
    return nil
end

function _回复血量()
    local role = _取人物信息()
    if role.x and _取坐标距离(role.x,role.y,g_复活到挂机[1].x,g_复活到挂机[1].y) < 10 then
        if _是否蹲下() then
            _Log("在复活点检测到蹲下状态，执行站立")
            _蹲下()
            _Sleep(1000)
        end
        return false
    end

    if g_State.MonsterObj == nil and role.x and role.Hp < g_血量低于多少回复 then
        while true do
            role = _取人物信息()
            if role.x and _取坐标距离(role.x,role.y,g_复活到挂机[1].x,g_复活到挂机[1].y) < 10 then
                if _是否蹲下() then
                    _Log("被召唤到复活点，从回血状态站立")
                    _蹲下()
                    _Sleep(1000)
                end
                return false
            end

            if not _是否蹲下() then
                _Log("执行回血，切换到蹲下状态")
                _Sleep(2000)
                _蹲下()
            end

            if role.Hp == role.HpM then
                if _是否蹲下() then
                    _Log("血量已满，从蹲下切换到站立")
                    _Sleep(2000)
                    _蹲下()
                    _Sleep(5000)
                end
                _Log("回血完成")
                return false
            end

            local result = _意外处理(_取人物信息())
            if result then
                if _是否蹲下() then
                    _Log("意外处理，从回血状态切换到站立")
                    _蹲下()
                    _Sleep(1000)
                end
                return result
            end
            _Sleep(500)
        end
    end
    return false
end
-- 状态机函数
g_State.Funs._自动捡物 = function(Param)
    local role = _取人物信息()
    local item = _取范围内地面物品()

    if not item or not role.x then
        return "_任务循环"
    end

    local distance = _取坐标距离(role.x, role.y, item.x, item.y)

    -- 快速捡取
    if distance <= CONFIG.捡物范围 then
        _捡物(item.Id)
        _Sleep(100)  -- 减少延迟提高速度
        return "_任务循环"
    end

    -- 超出范围放弃
    if distance > tonumber(g_挂机范围) + 5 then
        return "_任务循环"
    end

    -- 快速移动到物品位置
    _走路(item.x, item.y, item.z)
    _Sleep(50)

    return "_自动捡物"
end

g_State.Funs._追踪怪物 = function(Param)
    local role = nil
    local monster = nil
    local 上次攻击时间 = 0
    local 追踪开始时间 = os.time()
    local 连续卡住次数 = 0
    local oldPt = {x = 0, y = 0, z = 0}
    local _记录 = true
    local f = 5.0
    local 安全距离 = 8.0

    while true do
        if _Exit() == false then
            _停止走路()
            return ""
        end

        -- 处理蹲下状态
        if _是否蹲下() then
            if _GetSummonEnd() then
                _Log("被召唤状态检测到蹲下，执行站立")
                _蹲下()
                _Sleep(1000)
                return "_任务循环"
            end

            if g_检测到玩家蹲下 then
                if 检测附近玩家() then
                    _Log("检测到玩家，保持蹲下")
                    return "_任务循环"
                else
                    _Log("玩家已离开，站起来继续打怪")
                    _蹲下()
                    _Sleep(1000)
                    g_检测到玩家蹲下 = false
                end
            else
                _Log("检测到意外蹲下状态，尝试站立")
                _蹲下()
                _Sleep(1000)
            end
        end

        -- 获取角色信息
        role = _取人物信息()
        if not role or not role.x then
            return "_初始化"
        end

        -- 【新增】长时间停留检测
        if 检测长时间停留(role) then
            _Log("长时间停留，放弃当前怪物")
            g_State.MonsterObj = nil
            return "_任务循环"
        end

        -- 检查血量
        if role.Hp < g_血量低于多少回复 then
            _Log("血量不足，需要回血")
            g_State.MonsterObj = nil
            return "_任务循环"
        end

        -- 检查周围物品（在打怪过程中也要捡物品）
        local nearby_item = _取范围内地面物品()
        if nearby_item then
            local item_distance = _取坐标距离(role.x, role.y, nearby_item.x, nearby_item.y)
            if item_distance <= CONFIG.捡物范围 then
                _捡物(nearby_item.Id)
                _Sleep(100)
            end
        end

        -- 检查周围怪物
        local nearby_monsters = _取周围怪物信息()
        if nearby_monsters then
            for _, m in ipairs(nearby_monsters) do
                if m.Id ~= Param.Id then
                    local dist = _取坐标距离(role.x, role.y, m.x, m.y)
                    if dist < 安全距离 then
                        _Log(string.format("发现近距离怪物: %s, 距离: %.2f, 优先处理", m.Name, dist))
                        g_State.Params._追踪怪物 = m
                        g_State.MonsterObj = m
                        Param = m
                        break
                    end
                end
            end
        end

        -- 检查玩家
        if 检测附近玩家() then
            _Log("[警告] 检测到附近有玩家,继续打完当前怪物")
            g_检测到玩家蹲下 = true
        end

        -- 更新怪物信息
        monster = _更新怪物属性(Param)
        if not monster or monster.Hp <= 0 then
            if g_检测到玩家蹲下 then
                _Log("怪物已死亡，检测到玩家，执行蹲下")
                if not _是否蹲下() then
                    _蹲下()
                end
                while 检测附近玩家() do
                    _Sleep(1000)
                end
                _Log("玩家已离开，站起来继续打怪")
                if _是否蹲下() then
                    _蹲下()
                end
                g_检测到玩家蹲下 = false
            end
            g_State.MonsterObj = nil
            return "_任务循环"
        end

        -- 战斗逻辑 - 完全按照你的原版
        local 当前距离 = _取坐标距离(role.x, role.y, monster.x, monster.y)
        if 当前距离 <= g_攻击距离 and not _是否蹲下() then
            local current_time = os.time()

            -- 处理攻击逻辑
            if current_time - 上次攻击时间 >= CONFIG.攻击冷却时间 then
                _释放技能(monster)
                上次攻击时间 = current_time
                追踪开始时间 = current_time
            end

            -- 处理后退逻辑
            local dx = role.x - monster.x
            local dy = role.y - monster.y
            local dist = math.sqrt(dx * dx + dy * dy)

            if dist > 0 then
                dx = dx / dist
                dy = dy / dist

                local target_x = monster.x + dx * g_攻击距离
                local target_y = monster.y + dy * g_攻击距离

                -- 检查后退位置安全性
                local is_safe = true
                if nearby_monsters then
                    for _, m in ipairs(nearby_monsters) do
                        if m.Id ~= monster.Id then
                            local back_dist = _取坐标距离(target_x, target_y, m.x, m.y)
                            if back_dist < 安全距离 then
                                is_safe = false
                                break
                            end
                        end
                    end
                end

                -- 【修改】安全的话就后退，不安全则尝试其他方向
                if is_safe then
                    _走路(target_x, target_y, role.z)
                else
                    -- 【新增】原方向不安全，尝试其他方向
                    local alt_x, alt_y, alt_z = 尝试多方向后退(role, monster, nearby_monsters, 安全距离)
                    if alt_x then
                        _走路(alt_x, alt_y, alt_z)
                    end
                end
            end
        elseif not _是否蹲下() then
            if _记录 then
                oldPt.x, oldPt.y, oldPt.z = monster.x, monster.y, monster.z
                _记录 = false
            elseif _取坐标距离(oldPt.x, oldPt.y, monster.x, monster.y) > f then
                _记录 = true
                _走路(role.x, role.y, role.z)
                _Sleep(10)
            end
            _走路(monster.x, monster.y, monster.z)
        end

        if os.time() - 追踪开始时间 > CONFIG.最大追踪时间 then
            g_State.MonsterObj = nil
            return "_任务循环"
        end

        _Sleep(50)
    end
end
g_State.Funs._任务循环 = function()
    local role = _取人物信息()

    -- 检查是否正在被召唤
    if _GetSummonEnd() then
        if _是否蹲下() then
            _Log("被召唤检测到蹲下状态，立即站立")
            _蹲下()
            _Sleep(1000)
        end
        g_检测到玩家蹲下 = false
        _Log("_任务循环 -> 正在被召唤")
        return "_任务循环"
    end

    -- 检查是否因为玩家而蹲下
    if g_检测到玩家蹲下 then
        if 检测附近玩家() then
            if not _是否蹲下() then
                _Log("检测到玩家，保持蹲下")
                _蹲下()
            end
            _Sleep(1000)
            return "_任务循环"
        else
            _Log("玩家已离开，站起来继续打怪")
            if _是否蹲下() then
                _蹲下()
            end
            g_检测到玩家蹲下 = false
        end
    end

    -- 检查是否在复活点
    if role.x ~= nil and _取坐标距离(role.x,role.y,g_复活到挂机[1].x,g_复活到挂机[1].y) < 10 then
        if g_挂机模式 == "单人" then
            _Log("_任务循环 -> 人物在复活点,开始寻路到挂机点")
            return _到挂机点()
        end
    end

    -- 优先检查附近物品
    local item = _取范围内地面物品()
    if item then
        local distance = _取坐标距离(role.x, role.y, item.x, item.y)
        if distance <= CONFIG.捡物范围 * 2 then  -- 如果物品较近，优先捡取
            return "_自动捡物"
        end
    end

    -- 检查是否需要回血
    local result = _回复血量()
    if result then
        return result
    end

    -- 检查是否需要返回挂机范围（只在没有目标怪物时检查）
    if role.x ~= nil and g_State.MonsterObj == nil then
        local 距离挂机点 = _取坐标距离(挂机点x,挂机点y,role.x,role.y)
        if 距离挂机点 > tonumber(g_挂机范围) then
            _Log(string.format("超出挂机范围(%.1f)，返回挂机点", 距离挂机点))
            _走路(挂机点x, 挂机点y, 挂机点z)
            _Sleep(500)
            return "_任务循环"
        end
    end

    -- 处理打怪和捡物逻辑
    if role.x ~= nil and _取坐标距离(挂机点x,挂机点y,role.x,role.y) <= tonumber(g_挂机范围) + 10 then
        -- 在没有怪物或打完怪物后检查物品
        if g_State.MonsterObj == nil then
            if item then
                return "_自动捡物"
            end

            local _怪 = _取范围内最近怪物()
            if _怪 ~= nil then
                g_State.Params._追踪怪物 = _怪
                g_State.MonsterObj = _怪
                return "_追踪怪物"
            else
                _Log("_任务循环 -> 挂机范围内没有怪物")
                _Sleep(500)
            end
        else
            _Log("继续追踪当前怪物")
            g_State.Params._追踪怪物 = g_State.MonsterObj
            return "_追踪怪物"
        end
    end

    return "_任务循环"
end
g_State.Funs._初始化 = function()
    if _读寻路坐标() then
        挂机点x,挂机点y,挂机点z = g_复活到挂机[#g_复活到挂机].x,g_复活到挂机[#g_复活到挂机].y,g_复活到挂机[#g_复活到挂机].z
        if g_挂机模式 == "单人" then
            return _到挂机点()
        end
        return "_任务循环"
    end
end

-- 确保到挂机点函数存在
g_State.Funs._到挂机点 = _到挂机点

-- 启动脚本
g_State.FunName = "_初始化"
g_State.Create()