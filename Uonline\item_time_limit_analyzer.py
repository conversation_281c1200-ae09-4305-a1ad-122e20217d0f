#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天之游侠 24小时物品限制系统分析工具
基于真实游戏文件数据的逆向分析
"""

import re
import struct
import os
from typing import List, Dict, Tuple

class UonlineItemAnalyzer:
    def __init__(self, game_dir: str = "D:/1/Uonline"):
        self.game_dir = game_dir
        self.message_file = os.path.join(game_dir, "Message.ctf")
        self.main_exe = os.path.join(game_dir, "Prmain.exe")
        self.game_dll = os.path.join(game_dir, "Uonline.dll")
        
        # 从真实数据中提取的24小时限制物品模式
        self.time_limit_patterns = [
            r"24Сʱֻ��ת��һ��",  # 24小时只能转移一次
            r"24Сʱ֮��ֻ��ת��һ��",  # 24小时之内只能转移一次
            r"������Ʒ\(24Сʱ",  # 临时物品(24小时
        ]
        
        # 真实存在的HP值范围
        self.real_hp_values = []
        self.time_limited_items = []
        
    def analyze_message_file(self) -> Dict:
        """分析Message.ctf文件中的24小时限制物品"""
        results = {
            "total_time_limited_items": 0,
            "hp_items_with_limits": [],
            "highest_hp_limited": 0,
            "item_categories": {},
            "time_limit_mechanics": []
        }
        
        if not os.path.exists(self.message_file):
            return {"error": "Message.ctf文件不存在"}
            
        try:
            with open(self.message_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                
            # 查找所有24小时限制物品
            for pattern in self.time_limit_patterns:
                matches = re.finditer(pattern, content)
                for match in matches:
                    results["total_time_limited_items"] += 1
                    
                    # 获取上下文，查找HP值
                    start = max(0, match.start() - 200)
                    end = min(len(content), match.end() + 200)
                    context = content[start:end]
                    
                    # 查找HP值
                    hp_matches = re.findall(r'HP\+(\d+)', context)
                    for hp_val in hp_matches:
                        hp_int = int(hp_val)
                        if hp_int > 0:
                            results["hp_items_with_limits"].append({
                                "hp_value": hp_int,
                                "context": context[:100] + "..."
                            })
                            if hp_int > results["highest_hp_limited"]:
                                results["highest_hp_limited"] = hp_int
                                
            # 统计物品类别
            category_patterns = {
                "临时物品": r"������Ʒ",
                "合成材料": r"�ϳɹ�ʽ",
                "装备": r"װ��",
                "药水": r"ҩˮ|HP\+|MP\+"
            }
            
            for category, pattern in category_patterns.items():
                matches = len(re.findall(pattern, content))
                results["item_categories"][category] = matches
                
        except Exception as e:
            results["error"] = f"分析失败: {str(e)}"
            
        return results
    
    def find_time_limit_memory_addresses(self) -> List[Dict]:
        """在可执行文件中查找时间限制相关的内存地址"""
        addresses = []
        
        # 分析主程序
        if os.path.exists(self.main_exe):
            try:
                with open(self.main_exe, 'rb') as f:
                    data = f.read()
                    
                # 查找可能的时间检查函数
                # 24小时 = 86400秒 = 0x15180
                time_constants = [
                    b'\x80\x51\x01\x00',  # 86400 little endian
                    b'\x00\x00\x51\x80',  # 86400 big endian
                    b'\x18\x00\x00\x00',  # 24 hours
                ]
                
                for i, constant in enumerate(time_constants):
                    offset = 0
                    while True:
                        pos = data.find(constant, offset)
                        if pos == -1:
                            break
                        addresses.append({
                            "type": f"时间常量_{i}",
                            "offset": hex(pos),
                            "value": constant.hex(),
                            "description": "可能的24小时时间检查"
                        })
                        offset = pos + 1
                        
            except Exception as e:
                addresses.append({"error": f"分析主程序失败: {str(e)}"})
                
        return addresses
    
    def generate_bypass_methods(self) -> Dict:
        """生成绕过24小时限制的方法"""
        methods = {
            "内存修改": {
                "description": "通过修改内存中的时间检查来绕过限制",
                "tools_needed": ["Cheat Engine", "Process Hacker"],
                "steps": [
                    "1. 启动游戏并附加到进程",
                    "2. 搜索时间相关的内存值",
                    "3. 查找24小时限制检查函数",
                    "4. 修改时间比较逻辑",
                    "5. 冻结相关内存地址"
                ],
                "risk_level": "中等",
                "detection_risk": "可能被反作弊检测"
            },
            "文件修改": {
                "description": "修改游戏配置文件或数据文件",
                "tools_needed": ["十六进制编辑器"],
                "steps": [
                    "1. 备份原始文件",
                    "2. 在Message.ctf中查找时间限制文本",
                    "3. 修改或删除限制条件",
                    "4. 重新启动游戏测试"
                ],
                "risk_level": "低",
                "detection_risk": "较低，但可能导致游戏崩溃"
            },
            "系统时间操作": {
                "description": "通过修改系统时间来重置物品冷却",
                "tools_needed": ["系统时间修改工具"],
                "steps": [
                    "1. 记录当前系统时间",
                    "2. 将系统时间向前调整25小时",
                    "3. 登录游戏使用物品",
                    "4. 恢复系统时间"
                ],
                "risk_level": "低",
                "detection_risk": "很低，但可能影响其他程序"
            }
        }
        
        return methods
    
    def create_memory_trainer(self) -> str:
        """创建内存修改工具的代码"""
        trainer_code = '''
// 天之游侠 24小时限制绕过工具
// 使用方法：编译后运行，选择对应功能

#include <windows.h>
#include <tlhelp32.h>
#include <iostream>

class UonlineTrainer {
private:
    HANDLE hProcess;
    DWORD processId;
    
public:
    bool AttachToGame() {
        PROCESSENTRY32 pe32;
        HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        
        pe32.dwSize = sizeof(PROCESSENTRY32);
        
        if (Process32First(hSnapshot, &pe32)) {
            do {
                if (strcmp(pe32.szExeFile, "Prmain.exe") == 0) {
                    processId = pe32.th32ProcessID;
                    hProcess = OpenProcess(PROCESS_ALL_ACCESS, FALSE, processId);
                    CloseHandle(hSnapshot);
                    return hProcess != NULL;
                }
            } while (Process32Next(hSnapshot, &pe32));
        }
        
        CloseHandle(hSnapshot);
        return false;
    }
    
    bool BypassTimeLimit() {
        // 查找并修改时间检查函数
        DWORD timeCheck = 0x86400; // 24小时秒数
        DWORD newValue = 0x1;      // 修改为1秒
        
        // 这里需要根据实际内存地址进行修改
        // 示例地址，需要通过调试确定
        DWORD baseAddr = 0x400000;
        
        return WriteProcessMemory(hProcess, (LPVOID)baseAddr, 
                                &newValue, sizeof(DWORD), NULL);
    }
};
'''
        return trainer_code

def main():
    print("=== 天之游侠 24小时物品限制分析工具 ===")
    print("基于真实游戏文件的逆向分析\n")
    
    analyzer = UonlineItemAnalyzer()
    
    # 分析Message.ctf文件
    print("1. 分析物品限制系统...")
    results = analyzer.analyze_message_file()
    
    if "error" not in results:
        print(f"发现 {results['total_time_limited_items']} 个时间限制物品")
        print(f"最高HP限制物品: HP+{results['highest_hp_limited']}")
        print(f"HP限制物品数量: {len(results['hp_items_with_limits'])}")
        
        print("\n物品类别统计:")
        for category, count in results["item_categories"].items():
            print(f"  {category}: {count} 个")
    else:
        print(f"分析失败: {results['error']}")
    
    # 查找内存地址
    print("\n2. 查找时间限制相关内存地址...")
    addresses = analyzer.find_time_limit_memory_addresses()
    print(f"发现 {len(addresses)} 个可能的时间检查地址")
    
    # 生成绕过方法
    print("\n3. 生成绕过方法...")
    methods = analyzer.generate_bypass_methods()
    
    print("\n可用的绕过方法:")
    for method_name, details in methods.items():
        print(f"\n{method_name}:")
        print(f"  风险等级: {details['risk_level']}")
        print(f"  检测风险: {details['detection_risk']}")
        print(f"  描述: {details['description']}")

if __name__ == "__main__":
    main()
