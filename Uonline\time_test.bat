@echo off
chcp 65001 >nul
echo.
echo ==========================================
echo 🧪 24小时限制验证测试
echo ==========================================
echo.
echo 📋 测试步骤:
echo 1. 准备一个有24小时限制的物品
echo 2. 记录当前系统时间
echo 3. 修改系统时间到25小时后
echo 4. 重启游戏测试限制是否解除
echo 5. 恢复系统时间
echo.
echo ⚠️  注意: 这个测试会暂时修改系统时间
echo.

:MENU
echo 选择操作:
echo [1] 显示当前时间
echo [2] 设置时间到25小时后
echo [3] 恢复当前时间 
echo [4] 退出
echo.
set /p choice="请选择 (1-4): "

if "%choice%"=="1" goto SHOW_TIME
if "%choice%"=="2" goto SET_FUTURE_TIME
if "%choice%"=="3" goto RESTORE_TIME
if "%choice%"=="4" goto EXIT
goto MENU

:SHOW_TIME
echo.
echo 📅 当前系统时间:
date /t
time /t
echo.
pause
goto MENU

:SET_FUTURE_TIME
echo.
echo ⏰ 正在设置时间到25小时后...
echo.
echo 📝 请记录当前时间用于恢复:
date /t
time /t
echo.
echo ⚠️  即将修改系统时间，按任意键继续...
pause >nul

REM 获取当前时间并计算25小时后
for /f "tokens=1-3 delims=/" %%a in ('date /t') do (
    set current_date=%%a/%%b/%%c
)

REM 这里需要手动计算，因为批处理的日期计算比较复杂
echo.
echo 🔧 请手动设置系统时间:
echo 1. 右键点击任务栏时间
echo 2. 选择"调整日期/时间"
echo 3. 关闭"自动设置时间"
echo 4. 点击"更改"按钮
echo 5. 将时间设置为当前时间+25小时
echo.
echo 💡 例如: 如果现在是 2024/1/15 10:00
echo         设置为: 2024/1/16 11:00
echo.
echo 设置完成后按任意键继续...
pause >nul

echo.
echo ✅ 时间修改完成!
echo 📋 现在可以:
echo 1. 启动游戏
echo 2. 登录角色
echo 3. 检查24小时限制物品是否可用
echo 4. 测试完成后选择选项3恢复时间
echo.
pause
goto MENU

:RESTORE_TIME
echo.
echo 🔄 恢复系统时间...
echo.
echo 🔧 请手动恢复系统时间:
echo 1. 右键点击任务栏时间
echo 2. 选择"调整日期/时间"  
echo 3. 开启"自动设置时间"
echo 4. 系统会自动同步正确时间
echo.
echo 恢复完成后按任意键继续...
pause >nul

echo.
echo ✅ 时间恢复完成!
echo 📅 当前时间:
date /t
time /t
echo.
pause
goto MENU

:EXIT
echo.
echo 👋 测试完成!
echo.
echo 📊 测试结果分析:
echo ✅ 如果修改时间后限制解除 = 客户端验证
echo ❌ 如果修改时间后限制仍存在 = 服务器验证
echo.
pause
exit

