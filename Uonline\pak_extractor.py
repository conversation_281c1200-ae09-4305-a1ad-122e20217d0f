#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PAK文件提取器 - 尝试解包游戏资源文件
"""

import os
import struct
import zlib
from pathlib import Path

class PAKExtractor:
    def __init__(self, pak_file):
        self.pak_file = pak_file
        self.data = None
        
    def load(self):
        """加载PAK文件"""
        try:
            with open(self.pak_file, 'rb') as f:
                self.data = f.read()
            return True
        except Exception as e:
            print(f"加载失败: {e}")
            return False
            
    def analyze_header(self):
        """分析PAK文件头"""
        if not self.data or len(self.data) < 16:
            return None
            
        # 尝试不同的头部格式
        header_formats = [
            '<4sI',      # 签名 + 版本
            '<4sII',     # 签名 + 文件数 + 偏移
            '<8s',       # 8字节签名
            '<IIII',     # 4个整数
        ]
        
        results = []
        for fmt in header_formats:
            try:
                size = struct.calcsize(fmt)
                if len(self.data) >= size:
                    values = struct.unpack(fmt, self.data[:size])
                    results.append((fmt, values))
            except:
                continue
                
        return results
        
    def find_file_entries(self):
        """查找文件条目"""
        if not self.data:
            return []
            
        entries = []
        
        # 查找可能的文件名
        for i in range(0, len(self.data) - 100, 4):
            try:
                # 查找以.开头的文件扩展名
                if self.data[i:i+4] == b'.bmp' or self.data[i:i+4] == b'.txt':
                    # 向前查找文件名开始
                    start = i
                    while start > 0 and self.data[start-1] != 0:
                        start -= 1
                    filename = self.data[start:i+4].decode('ascii', errors='ignore')
                    if len(filename) > 0 and len(filename) < 100:
                        entries.append((start, filename))
            except:
                continue
                
        return entries
        
    def extract_text_data(self):
        """提取文本数据"""
        if not self.data:
            return []
            
        text_data = []
        
        # 查找ASCII文本
        current_text = b""
        for byte in self.data:
            if 32 <= byte <= 126:  # 可打印ASCII
                current_text += bytes([byte])
            else:
                if len(current_text) > 10:  # 至少10个字符
                    try:
                        text = current_text.decode('ascii')
                        if 'HP' in text or 'MP' in text or '+' in text:
                            text_data.append(text)
                    except:
                        pass
                current_text = b""
                
        return text_data
        
    def search_hp_data(self):
        """搜索HP相关数据"""
        if not self.data:
            return []
            
        hp_data = []
        
        # 搜索HP+模式
        i = 0
        while i < len(self.data) - 10:
            if self.data[i:i+3] == b'HP+':
                # 提取数字
                j = i + 3
                num_str = b""
                while j < len(self.data) and self.data[j:j+1].isdigit():
                    num_str += self.data[j:j+1]
                    j += 1
                if num_str:
                    try:
                        value = int(num_str)
                        hp_data.append((i, value))
                    except:
                        pass
            i += 1
            
        return hp_data

def main():
    """主函数"""
    pak_files = ['item0001.pak', 'item0002.pak', 'item0003.pak', 'etc.pak']
    
    for pak_name in pak_files:
        if not os.path.exists(pak_name):
            continue
            
        print(f"\n=== 分析 {pak_name} ===")
        
        extractor = PAKExtractor(pak_name)
        if not extractor.load():
            continue
            
        # 分析头部
        headers = extractor.analyze_header()
        print(f"可能的头部格式:")
        for fmt, values in headers:
            print(f"  {fmt}: {values}")
            
        # 查找文件条目
        entries = extractor.find_file_entries()
        print(f"找到 {len(entries)} 个可能的文件条目")
        for offset, filename in entries[:5]:  # 只显示前5个
            print(f"  {offset:08x}: {filename}")
            
        # 搜索HP数据
        hp_data = extractor.search_hp_data()
        print(f"找到 {len(hp_data)} 个HP+条目")
        for offset, value in hp_data[:10]:  # 只显示前10个
            print(f"  {offset:08x}: HP+{value}")
            
        # 提取文本数据
        text_data = extractor.extract_text_data()
        hp_texts = [t for t in text_data if 'HP' in t]
        print(f"找到 {len(hp_texts)} 个包含HP的文本")
        for text in hp_texts[:5]:  # 只显示前5个
            print(f"  {text[:50]}...")

if __name__ == "__main__":
    main()
