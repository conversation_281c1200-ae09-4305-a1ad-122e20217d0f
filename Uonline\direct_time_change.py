#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接修改系统时间 - 使用PowerShell命令
"""

import subprocess
import sys
from datetime import datetime, timedelta
import time

def run_powershell_as_admin(command):
    """以管理员身份运行PowerShell命令"""
    try:
        # 构建PowerShell命令
        ps_command = f'Start-Process powershell -ArgumentList "-Command {command}" -Verb RunAs -Wait'
        
        result = subprocess.run(
            ['powershell', '-Command', ps_command],
            capture_output=True,
            text=True,
            shell=True
        )
        
        return result.returncode == 0
    except Exception as e:
        print(f"❌ PowerShell命令执行失败: {e}")
        return False

def set_time_with_powershell(target_datetime):
    """使用PowerShell设置系统时间"""
    try:
        # 格式化时间
        date_str = target_datetime.strftime("%m/%d/%Y")
        time_str = target_datetime.strftime("%H:%M:%S")
        
        # PowerShell命令设置日期和时间
        date_command = f'Set-Date -Date "{date_str} {time_str}"'
        
        print(f"🔧 正在设置时间为: {target_datetime.strftime('%Y-%m-%d %H:%M:%S')}")
        print("⚠️ 可能会弹出UAC权限确认窗口，请点击'是'")
        
        success = run_powershell_as_admin(date_command)
        
        if success:
            print("✅ 时间设置成功!")
            return True
        else:
            print("❌ 时间设置失败")
            return False
            
    except Exception as e:
        print(f"❌ 设置时间出错: {e}")
        return False

def sync_time():
    """同步网络时间"""
    try:
        print("🔄 正在同步网络时间...")
        
        # 启动时间服务并同步
        sync_command = 'w32tm /resync'
        
        success = run_powershell_as_admin(sync_command)
        
        if success:
            print("✅ 时间同步成功!")
            return True
        else:
            print("❌ 时间同步失败")
            return False
            
    except Exception as e:
        print(f"❌ 同步时间出错: {e}")
        return False

def check_and_kill_game():
    """检查并关闭游戏"""
    try:
        # 检查游戏进程
        result = subprocess.run(
            'tasklist /FI "IMAGENAME eq Prmain.exe"',
            shell=True, capture_output=True, text=True
        )
        
        if "Prmain.exe" in result.stdout:
            print("🎮 检测到游戏正在运行，正在关闭...")
            
            # 强制关闭游戏
            kill_result = subprocess.run(
                'taskkill /F /IM Prmain.exe',
                shell=True, capture_output=True, text=True
            )
            
            if kill_result.returncode == 0:
                print("✅ 游戏已关闭")
                time.sleep(2)
                return True
            else:
                print("❌ 无法关闭游戏，请手动关闭")
                return False
        else:
            print("✅ 游戏未运行")
            return True
            
    except Exception as e:
        print(f"❌ 检查游戏进程失败: {e}")
        return False

def main():
    print("=" * 60)
    print("⚡ 直接时间修改工具 - 24小时限制绕过测试")
    print("=" * 60)
    print()
    
    # 获取当前时间
    current_time = datetime.now()
    target_time = current_time + timedelta(hours=25)
    
    print(f"📅 当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 目标时间: {target_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    print("选择操作:")
    print("1. ⚡ 直接修改时间到25小时后")
    print("2. 🔄 恢复网络同步时间")
    print("3. 🎯 完整自动测试")
    print("4. 🚪 退出")
    print()
    
    choice = input("请选择 (1-4): ").strip()
    
    if choice == "1":
        print("\n⚡ 开始修改系统时间...")
        print()
        
        # 关闭游戏
        if not check_and_kill_game():
            print("⚠️ 请手动关闭游戏后重试")
            input("按回车键继续...")
            return
        
        # 修改时间
        if set_time_with_powershell(target_time):
            print()
            print("🎉 时间修改完成!")
            print("📋 现在可以:")
            print("   1. 启动游戏")
            print("   2. 登录角色")
            print("   3. 测试24小时限制物品")
            print("   4. 记录测试结果")
            print()
            print("⚠️ 测试完成后请选择选项2恢复时间")
        else:
            print("❌ 时间修改失败")
    
    elif choice == "2":
        print("\n🔄 恢复网络同步时间...")
        print()
        
        if sync_time():
            print()
            print("✅ 时间已恢复正常")
            current = datetime.now()
            print(f"📅 当前时间: {current.strftime('%Y-%m-%d %H:%M:%S')}")
        else:
            print("❌ 时间恢复失败")
    
    elif choice == "3":
        print("\n🎯 开始完整自动测试...")
        print()
        
        # 步骤1: 关闭游戏
        print("📋 步骤1: 关闭游戏")
        if not check_and_kill_game():
            print("❌ 请手动关闭游戏")
            input("关闭游戏后按回车继续...")
        
        # 步骤2: 修改时间
        print("\n📋 步骤2: 修改系统时间")
        if set_time_with_powershell(target_time):
            print()
            print("🎮 现在请测试:")
            print("   1. 启动游戏")
            print("   2. 登录角色")
            print("   3. 找到有'24小时限制'的物品")
            print("   4. 尝试使用该物品")
            print("   5. 记录结果:")
            print("      ✅ 可以使用 = 绕过成功 (客户端验证)")
            print("      ❌ 仍然限制 = 绕过失败 (服务器验证)")
            print()
            
            input("测试完成后按回车恢复时间...")
            
            # 步骤3: 恢复时间
            print("\n📋 步骤3: 恢复系统时间")
            if sync_time():
                print()
                final_time = datetime.now()
                print(f"✅ 时间已恢复: {final_time.strftime('%Y-%m-%d %H:%M:%S')}")
                print()
                print("📊 测试结果分析:")
                print("🟢 如果绕过成功 = 我的逆向分析正确")
                print("   - timeGetTime API用于客户端时间验证")
                print("   - 可以用时间修改方法绕过24小时限制")
                print()
                print("🔴 如果绕过失败 = 主要是服务器验证")
                print("   - 服务器记录物品使用时间戳")
                print("   - 需要更复杂的绕过方法")
            else:
                print("❌ 时间恢复失败")
        else:
            print("❌ 时间修改失败，测试中止")
    
    elif choice == "4":
        print("\n👋 程序退出")
        return
    
    else:
        print("❌ 无效选择")
    
    print()
    input("按回车键退出...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n🛑 用户中断")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
        input("按回车键退出...")
