#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PAK文件数据分析器 - 寻找真正的游戏数据源
"""

import os
import struct
import re
from pathlib import Path

def analyze_pak_file(pak_path):
    """分析PAK文件内容"""
    try:
        with open(pak_path, 'rb') as f:
            data = f.read()
            
        # 查找HP相关数据
        hp_patterns = [
            rb'HP\+\d+',
            rb'\xc9\xfa\xc3\xfc',  # 生命 (中文编码)
            rb'\xd1\xaa\xc1\xbf',  # 血量 (中文编码)
            rb'24\xd0\xa1\xca\xb1', # 24小时 (中文编码)
        ]
        
        results = []
        for pattern in hp_patterns:
            matches = re.findall(pattern, data)
            if matches:
                results.append(f"Found {len(matches)} matches for pattern: {pattern}")
                
        # 查找数值数据
        numeric_data = []
        for i in range(0, len(data) - 4, 4):
            try:
                value = struct.unpack('<I', data[i:i+4])[0]
                if 1000 <= value <= 999999:  # HP范围
                    numeric_data.append((i, value))
            except:
                continue
                
        if numeric_data:
            results.append(f"Found {len(numeric_data)} potential HP values")
            
        return results
        
    except Exception as e:
        return [f"Error: {e}"]

def main():
    """主函数"""
    pak_dir = Path(".")
    pak_files = list(pak_dir.glob("*.pak"))
    
    print(f"分析 {len(pak_files)} 个PAK文件...")
    
    important_paks = []
    
    for pak_file in pak_files:
        print(f"\n分析: {pak_file.name}")
        results = analyze_pak_file(pak_file)
        
        if any("Found" in r and "matches" in r for r in results):
            important_paks.append(pak_file.name)
            print("*** 重要文件 ***")
            
        for result in results:
            print(f"  {result}")
            
    print(f"\n重要的PAK文件:")
    for pak in important_paks:
        print(f"  {pak}")
        
    # 分析item相关的PAK文件
    item_paks = [f for f in pak_files if "item" in f.name.lower() or "eqp" in f.name.lower()]
    print(f"\n装备/物品相关PAK文件:")
    for pak in item_paks:
        print(f"  {pak.name}")

if __name__ == "__main__":
    main()
