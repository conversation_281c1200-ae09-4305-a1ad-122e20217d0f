import os
import glob

# 分析PAK文件
pak_files = glob.glob("*.pak")
print(f"发现 {len(pak_files)} 个PAK文件")

# 分类分析
monster_files = []
map_files = []
equipment_files = []
npc_files = []

for pak in pak_files:
    name = pak.lower()
    if any(keyword in name for keyword in ['monster', 'zombie', 'spider', 'wolf', 'angel', 'demon', 'boss', 'dragon', 'beast']):
        monster_files.append(pak)
    elif 'map-' in name:
        map_files.append(pak)
    elif any(keyword in name for keyword in ['eqp', 'item', 'weapon']):
        equipment_files.append(pak)
    elif any(keyword in name for keyword in ['npc', 'ivan', 'temo', 'keeper', 'master']):
        npc_files.append(pak)

print(f"\n怪物文件 ({len(monster_files)} 个):")
for f in monster_files[:10]:
    print(f"  {f}")

print(f"\n地图文件 ({len(map_files)} 个):")
for f in map_files[:10]:
    map_name = f.replace('map-', '').replace('.pak', '')
    print(f"  {map_name}")

print(f"\n装备文件 ({len(equipment_files)} 个):")
for f in equipment_files[:10]:
    print(f"  {f}")

print(f"\nNPC文件 ({len(npc_files)} 个):")
for f in npc_files[:10]:
    print(f"  {f}")

# 分析重要文件大小
important_files = ['Message.ctf', 'Common.pak', 'Effect.pak', 'Env.pak', 'prmain.exe']
print(f"\n重要文件大小:")
for filename in important_files:
    if os.path.exists(filename):
        size = os.path.getsize(filename)
        print(f"  {filename}: {size:,} 字节")
