#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实PAK文件修改器
基于深度分析结果，修改真正的游戏数据
"""

import os
import struct
import shutil
from datetime import datetime

class RealPAKModifier:
    def __init__(self, pak_file):
        self.pak_file = pak_file
        self.data = None
        self.modifications = 0
        
    def load_pak(self):
        """加载PAK文件"""
        try:
            with open(self.pak_file, 'rb') as f:
                self.data = bytearray(f.read())
            print(f"加载PAK文件: {self.pak_file} ({len(self.data):,} 字节)")
            return True
        except Exception as e:
            print(f"加载失败: {e}")
            return False
            
    def backup_pak(self):
        """备份PAK文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = f"{self.pak_file}.backup_{timestamp}"
        shutil.copy2(self.pak_file, backup_path)
        print(f"备份创建: {backup_path}")
        return backup_path
        
    def enhance_hp_values(self):
        """增强HP值"""
        print(f"\n=== 增强HP值 ===")
        
        # 基于分析结果的真实HP值
        if "item0001" in self.pak_file:
            target_values = [256, 32768, 4608, 2304, 128, 2816, 768, 4096]
        elif "eqp0001" in self.pak_file:
            target_values = [101, 25966, 256, 63, 191, 1087, 831, 62]
        else:
            target_values = [256, 1024, 2048, 4096, 8192]
            
        for target_value in target_values:
            enhanced_value = min(target_value * 10, 999999)  # 增强10倍，最大999999
            
            # 查找并替换32位小端序值
            target_bytes = struct.pack('<I', target_value)
            enhanced_bytes = struct.pack('<I', enhanced_value)
            
            count = 0
            i = 0
            while i < len(self.data) - 4:
                if self.data[i:i+4] == target_bytes:
                    self.data[i:i+4] = enhanced_bytes
                    count += 1
                    self.modifications += 1
                i += 1
                
            if count > 0:
                print(f"  HP {target_value} -> {enhanced_value}: 修改了 {count} 处")
                
    def remove_time_restrictions(self):
        """移除时间限制"""
        print(f"\n=== 移除时间限制 ===")
        
        # 基于分析结果的时间限制模式
        time_patterns = [
            (b'\x18\x00\x00\x00', b'\x01\x00\x00\x00'),  # 24小时 -> 1小时 (32位)
            (b'\x18\x00', b'\x01\x00'),                   # 24小时 -> 1小时 (16位)
            (b'\x18', b'\x01'),                           # 24 -> 1 (8位)
        ]
        
        for old_pattern, new_pattern in time_patterns:
            count = 0
            i = 0
            while i < len(self.data) - len(old_pattern):
                if self.data[i:i+len(old_pattern)] == old_pattern:
                    # 检查上下文，确保这是时间限制而不是其他数据
                    context_ok = True
                    
                    # 简单的上下文检查：前后不应该是连续的相同值
                    if i > 0 and i < len(self.data) - len(old_pattern) - 1:
                        if (self.data[i-1] == 0x18 or 
                            self.data[i+len(old_pattern)] == 0x18):
                            context_ok = False
                            
                    if context_ok:
                        self.data[i:i+len(old_pattern)] = new_pattern
                        count += 1
                        self.modifications += 1
                        
                i += 1
                
            if count > 0:
                print(f"  模式 {old_pattern.hex()} -> {new_pattern.hex()}: 修改了 {count} 处")
                
    def enhance_rare_values(self):
        """增强稀有数值"""
        print(f"\n=== 增强稀有数值 ===")
        
        # 基于分析结果的稀有值
        if "eqp0001" in self.pak_file:
            rare_values = [25966, 1087, 2367, 4927, 7487, 8767]
        elif "item0001" in self.pak_file:
            rare_values = [32768, 4608, 6912, 8960, 7168]
        else:
            rare_values = [21060, 33664, 16717, 37713, 19794]
            
        for rare_value in rare_values:
            enhanced_value = min(rare_value * 5, 999999)  # 增强5倍
            
            target_bytes = struct.pack('<I', rare_value)
            enhanced_bytes = struct.pack('<I', enhanced_value)
            
            count = 0
            i = 0
            while i < len(self.data) - 4:
                if self.data[i:i+4] == target_bytes:
                    self.data[i:i+4] = enhanced_bytes
                    count += 1
                    self.modifications += 1
                i += 1
                
            if count > 0:
                print(f"  稀有值 {rare_value} -> {enhanced_value}: 修改了 {count} 处")
                
    def add_cheat_items(self):
        """添加作弊物品"""
        print(f"\n=== 添加作弊物品 ===")
        
        # 在PAK文件末尾添加超级物品数据
        cheat_data = bytearray()
        
        # 超级HP物品 (999999 HP)
        super_hp = struct.pack('<I', 999999)
        cheat_data.extend(super_hp)
        
        # 超级金币 (999999999)
        super_gold = struct.pack('<I', 999999999)
        cheat_data.extend(super_gold)
        
        # 无限耐久度 (65535)
        infinite_durability = struct.pack('<H', 65535)
        cheat_data.extend(infinite_durability)
        
        # 添加到文件末尾
        self.data.extend(cheat_data)
        self.modifications += len(cheat_data) // 4
        
        print(f"  添加了 {len(cheat_data)} 字节的作弊数据")
        
    def save_modified_pak(self):
        """保存修改后的PAK文件"""
        try:
            with open(self.pak_file, 'wb') as f:
                f.write(self.data)
            print(f"\n修改完成: {self.modifications} 处变更")
            print(f"新文件大小: {len(self.data):,} 字节")
            return True
        except Exception as e:
            print(f"保存失败: {e}")
            return False
            
    def verify_modifications(self):
        """验证修改"""
        print(f"\n=== 验证修改 ===")
        
        # 检查一些关键值是否被修改
        enhanced_hp_found = 0
        reduced_time_found = 0
        
        # 检查增强的HP值
        for i in range(0, len(self.data) - 4, 4):
            value = struct.unpack('<I', self.data[i:i+4])[0]
            if 50000 <= value <= 999999:  # 增强后的HP范围
                enhanced_hp_found += 1
                
        # 检查减少的时间限制
        one_hour_pattern = b'\x01\x00\x00\x00'
        reduced_time_found = self.data.count(one_hour_pattern)
        
        print(f"  发现增强HP值: {enhanced_hp_found} 个")
        print(f"  发现1小时限制: {reduced_time_found} 个")
        
        return enhanced_hp_found > 0 or reduced_time_found > 0

def main():
    """主函数"""
    print("真实PAK文件修改器")
    print("基于深度分析结果修改真正的游戏数据")
    print("=" * 60)
    
    # 要修改的关键PAK文件
    target_paks = [
        'item0001.pak',  # 17MB, 3164个时间限制
        'eqp0001.pak',   # 44MB, 470个时间限制
        'item0002.pak',  # 16MB, 1521个时间限制
        'eqp0002.pak',   # 41MB, 1354个时间限制
    ]
    
    success_count = 0
    
    for pak_name in target_paks:
        if not os.path.exists(pak_name):
            print(f"\n跳过不存在的文件: {pak_name}")
            continue
            
        print(f"\n{'='*60}")
        print(f"处理文件: {pak_name}")
        print(f"{'='*60}")
        
        modifier = RealPAKModifier(pak_name)
        
        # 执行修改流程
        if not modifier.load_pak():
            continue
            
        backup_path = modifier.backup_pak()
        
        # 执行各种修改
        modifier.enhance_hp_values()
        modifier.remove_time_restrictions()
        modifier.enhance_rare_values()
        modifier.add_cheat_items()
        
        # 保存并验证
        if modifier.save_modified_pak():
            if modifier.verify_modifications():
                print(f"✅ {pak_name} 修改成功！")
                success_count += 1
            else:
                print(f"⚠️ {pak_name} 修改可能未生效")
        else:
            print(f"❌ {pak_name} 修改失败")
            # 恢复备份
            if os.path.exists(backup_path):
                shutil.copy2(backup_path, pak_name)
                print(f"已恢复备份: {backup_path}")
                
    print(f"\n{'='*60}")
    print(f"修改完成: {success_count}/{len(target_paks)} 个文件成功修改")
    print(f"{'='*60}")
    
    if success_count > 0:
        print("\n🎉 重要提示:")
        print("1. 已创建备份文件，如有问题可以恢复")
        print("2. 重启游戏以加载修改后的数据")
        print("3. 如果游戏崩溃，请恢复备份文件")
        print("4. 这次修改的是真正的游戏数据，应该会有效果！")
    else:
        print("\n❌ 没有文件被成功修改")

if __name__ == "__main__":
    main()
