#!/usr/bin/env python3
"""
二进制分析MCP服务器
为Augment提供PE文件分析功能
"""

import asyncio
import json
import struct
import re
from pathlib import Path
from mcp.server import Server
from mcp.server.stdio import stdio_server
from mcp.types import Tool, TextContent

app = Server("binary-analyzer")

@app.list_tools()
async def list_tools():
    return [
        Tool(
            name="analyze_pe_structure",
            description="分析PE文件结构",
            inputSchema={
                "type": "object",
                "properties": {
                    "file_path": {"type": "string", "description": "PE文件路径"}
                },
                "required": ["file_path"]
            }
        ),
        Tool(
            name="find_api_calls",
            description="查找API调用",
            inputSchema={
                "type": "object", 
                "properties": {
                    "file_path": {"type": "string", "description": "文件路径"},
                    "api_names": {"type": "array", "items": {"type": "string"}, "description": "要搜索的API名称"}
                },
                "required": ["file_path"]
            }
        ),
        Tool(
            name="find_time_constants",
            description="查找时间相关常量",
            inputSchema={
                "type": "object",
                "properties": {
                    "file_path": {"type": "string", "description": "文件路径"}
                },
                "required": ["file_path"]
            }
        ),
        Tool(
            name="extract_strings",
            description="提取字符串",
            inputSchema={
                "type": "object",
                "properties": {
                    "file_path": {"type": "string", "description": "文件路径"},
                    "min_length": {"type": "integer", "description": "最小字符串长度", "default": 4}
                },
                "required": ["file_path"]
            }
        )
    ]

@app.call_tool()
async def call_tool(name: str, arguments: dict):
    if name == "analyze_pe_structure":
        return await analyze_pe_structure(arguments["file_path"])
    elif name == "find_api_calls":
        api_names = arguments.get("api_names", ["timeGetTime", "GetSystemTime", "send", "recv"])
        return await find_api_calls(arguments["file_path"], api_names)
    elif name == "find_time_constants":
        return await find_time_constants(arguments["file_path"])
    elif name == "extract_strings":
        min_length = arguments.get("min_length", 4)
        return await extract_strings(arguments["file_path"], min_length)
    else:
        raise ValueError(f"Unknown tool: {name}")

async def analyze_pe_structure(file_path: str):
    """分析PE文件结构"""
    try:
        with open(file_path, 'rb') as f:
            data = f.read()
        
        if data[:2] != b'MZ':
            return [TextContent(type="text", text="❌ 不是有效的PE文件")]
        
        # 获取PE头偏移
        pe_offset = struct.unpack('<I', data[0x3C:0x40])[0]
        
        if data[pe_offset:pe_offset+4] != b'PE\x00\x00':
            return [TextContent(type="text", text="❌ 无效的PE签名")]
        
        # 分析COFF头
        coff_header = data[pe_offset+4:pe_offset+24]
        machine, num_sections, timestamp = struct.unpack('<HHI', coff_header[:8])
        
        result = {
            "file_type": "PE",
            "machine_type": f"0x{machine:04X}",
            "sections_count": num_sections,
            "timestamp": timestamp,
            "sections": []
        }
        
        # 分析节表
        optional_header_size = struct.unpack('<H', coff_header[16:18])[0]
        section_table_offset = pe_offset + 24 + optional_header_size
        
        for i in range(num_sections):
            section_offset = section_table_offset + i * 40
            section_data = data[section_offset:section_offset+40]
            
            name = section_data[:8].rstrip(b'\x00').decode('ascii', errors='ignore')
            virtual_size = struct.unpack('<I', section_data[8:12])[0]
            virtual_address = struct.unpack('<I', section_data[12:16])[0]
            
            result["sections"].append({
                "name": name,
                "virtual_address": f"0x{virtual_address:08X}",
                "virtual_size": f"0x{virtual_size:08X}"
            })
        
        return [TextContent(type="text", text=json.dumps(result, indent=2))]
        
    except Exception as e:
        return [TextContent(type="text", text=f"❌ PE分析失败: {e}")]

async def find_api_calls(file_path: str, api_names: list):
    """查找API调用"""
    try:
        with open(file_path, 'rb') as f:
            data = f.read()
        
        results = {}
        for api in api_names:
            api_bytes = api.encode('ascii')
            matches = list(re.finditer(re.escape(api_bytes), data))
            if matches:
                results[api] = {
                    "count": len(matches),
                    "offsets": [f"0x{m.start():08X}" for m in matches[:5]]
                }
        
        return [TextContent(type="text", text=json.dumps(results, indent=2))]
        
    except Exception as e:
        return [TextContent(type="text", text=f"❌ API搜索失败: {e}")]

async def find_time_constants(file_path: str):
    """查找时间常量"""
    try:
        with open(file_path, 'rb') as f:
            data = f.read()
        
        time_constants = {
            86400: "24小时(秒)",
            86400000: "24小时(毫秒)",
            3600: "1小时(秒)", 
            3600000: "1小时(毫秒)",
            24: "24小时"
        }
        
        results = {}
        for constant, desc in time_constants.items():
            little_endian = struct.pack('<I', constant)
            matches = list(re.finditer(re.escape(little_endian), data))
            if matches:
                results[desc] = {
                    "value": constant,
                    "count": len(matches),
                    "offsets": [f"0x{m.start():08X}" for m in matches[:3]]
                }
        
        return [TextContent(type="text", text=json.dumps(results, indent=2))]
        
    except Exception as e:
        return [TextContent(type="text", text=f"❌ 时间常量搜索失败: {e}")]

async def extract_strings(file_path: str, min_length: int):
    """提取字符串"""
    try:
        with open(file_path, 'rb') as f:
            data = f.read()
        
        # ASCII字符串
        ascii_pattern = rb'[!-~]{' + str(min_length).encode() + rb',}'
        ascii_strings = re.findall(ascii_pattern, data)
        
        # 过滤有趣的字符串
        keywords = ['time', 'hour', '小时', 'limit', 'restrict', 'server', 'valid']
        interesting = []
        
        for s in ascii_strings:
            s_str = s.decode('ascii', errors='ignore')
            if any(keyword in s_str.lower() for keyword in keywords):
                interesting.append(s_str)
        
        result = {
            "total_strings": len(ascii_strings),
            "interesting_strings": interesting[:20]
        }
        
        return [TextContent(type="text", text=json.dumps(result, indent=2))]
        
    except Exception as e:
        return [TextContent(type="text", text=f"❌ 字符串提取失败: {e}")]

async def main():
    async with stdio_server() as (read_stream, write_stream):
        await app.run(read_stream, write_stream, app.create_initialization_options())

if __name__ == "__main__":
    asyncio.run(main())
