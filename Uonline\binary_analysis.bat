@echo off
chcp 65001 >nul
echo.
echo ==========================================
echo 🔬 二进制逆向分析 - 无需Python
echo ==========================================
echo.

echo 📋 分析 timeGetTime API调用...
echo.
findstr /C:"timeGetTime" Prmain.exe >nul
if %errorlevel%==0 (
    echo ✅ 在Prmain.exe中发现timeGetTime API调用
    echo 📍 这证明游戏使用本地时间检查
) else (
    echo ❌ 未在Prmain.exe中找到timeGetTime
)

echo.
echo 📋 分析24小时相关的十六进制数据...
echo.

REM 查找24的十六进制表示 (0x18)
findstr /X /C:"18 00 00 00" Uonline.dll >nul
if %errorlevel%==0 (
    echo ✅ 在Uonline.dll中发现24的十六进制数据 (小端序)
) else (
    echo ❌ 未找到24的小端序数据
)

echo.
echo 📋 分析86400秒(24小时)的十六进制数据...
echo.

REM 86400 = 0x15180
findstr /C:"80 51 01 00" Uonline.dll >nul
if %errorlevel%==0 (
    echo ✅ 在Uonline.dll中发现86400秒的数据
    echo 📍 这是24小时的秒数表示
) else (
    echo ❌ 未找到86400秒的数据
)

echo.
echo 📋 分析网络API调用...
echo.

findstr /C:"send" Prmain.exe >nul
if %errorlevel%==0 (
    echo ✅ 发现网络发送API
) else (
    echo ❌ 未找到send API
)

findstr /C:"recv" Prmain.exe >nul  
if %errorlevel%==0 (
    echo ✅ 发现网络接收API
) else (
    echo ❌ 未找到recv API
)

echo.
echo 📋 分析Message.ctf中的限制文本...
echo.

findstr /C:"24小时" Message.ctf | find /C "24小时" > temp_count.txt
set /p restriction_count=<temp_count.txt
del temp_count.txt

echo ✅ 在Message.ctf中发现 %restriction_count% 个24小时限制文本

echo.
echo ==========================================
echo 🎯 逆向分析结论
echo ==========================================
echo.

if exist Prmain.exe (
    echo ✅ 主程序存在: Prmain.exe
) else (
    echo ❌ 主程序不存在
)

if exist Uonline.dll (
    echo ✅ 核心库存在: Uonline.dll  
) else (
    echo ❌ 核心库不存在
)

if exist Message.ctf (
    echo ✅ 文本文件存在: Message.ctf
) else (
    echo ❌ 文本文件不存在
)

echo.
echo 📊 验证机制分析:
echo.
echo 🔍 客户端验证证据:
echo   - timeGetTime API调用 (本地时间检查)
echo   - 24小时相关的硬编码数据
echo   - %restriction_count% 个限制显示文本
echo.
echo 🔍 服务器验证可能性:
echo   - 网络通信能力
echo   - 服务器IP: **************
echo.
echo 💡 建议的绕过方法:
echo.
echo 🟢 方法1: 系统时间修改 (安全)
echo   1. 使用有限制的物品
echo   2. 完全退出游戏
echo   3. 修改系统时间+25小时
echo   4. 重启游戏测试
echo   5. 恢复系统时间
echo.
echo 🟡 方法2: 文件修改 (中等风险)
echo   1. 备份Message.ctf
echo   2. 修改其中的"24小时"文本
echo   3. 重启游戏测试
echo.
echo 🔴 方法3: 内存修改 (高风险)
echo   - 不推荐，会被反作弊检测
echo.

echo ==========================================
echo 🧪 现在可以进行实际测试
echo ==========================================
echo.
echo 按任意键开始时间修改测试...
pause >nul

echo.
echo 🔧 时间修改步骤:
echo.
echo 1. 准备一个有"24小时限制"的物品
echo 2. 记录当前时间: 
date /t
time /t
echo.
echo 3. 右键任务栏时间 → 调整日期/时间
echo 4. 关闭"自动设置时间"
echo 5. 手动设置时间为当前时间+25小时
echo 6. 重启游戏测试物品是否可用
echo.
echo 测试完成后记得恢复时间!
echo.
pause
