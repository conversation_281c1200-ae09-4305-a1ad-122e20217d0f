#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import struct
import re
import os

def quick_pe_check(filename):
    """快速PE检查"""
    try:
        with open(filename, 'rb') as f:
            data = f.read(1024)  # 只读前1KB
        
        if data[:2] == b'MZ':
            print(f"✅ {filename} 是PE文件")
            return True
        else:
            print(f"❌ {filename} 不是PE文件")
            return False
    except:
        print(f"❌ 无法读取 {filename}")
        return False

def find_timegettime(filename):
    """查找timeGetTime"""
    try:
        with open(filename, 'rb') as f:
            data = f.read()
        
        pattern = b'timeGetTime'
        matches = list(re.finditer(re.escape(pattern), data))
        
        print(f"🕐 {filename} 中的timeGetTime: {len(matches)}次")
        for i, match in enumerate(matches[:3]):
            offset = match.start()
            print(f"  位置{i+1}: 0x{offset:08X}")
        
        return len(matches) > 0
    except:
        print(f"❌ 分析 {filename} 失败")
        return False

def find_24_hour_constants(filename):
    """查找24小时常量"""
    try:
        with open(filename, 'rb') as f:
            data = f.read()
        
        # 86400秒 = 24小时
        const_86400 = struct.pack('<I', 86400)
        matches_86400 = list(re.finditer(re.escape(const_86400), data))
        
        # 24
        const_24 = struct.pack('<I', 24)
        matches_24 = list(re.finditer(re.escape(const_24), data))
        
        print(f"🔢 {filename} 中的常量:")
        print(f"  86400(24小时秒): {len(matches_86400)}次")
        print(f"  24: {len(matches_24)}次")
        
        return len(matches_86400) > 0 or len(matches_24) > 0
    except:
        print(f"❌ 常量分析 {filename} 失败")
        return False

def main():
    print("🔍 快速二进制分析")
    print("="*30)
    
    files = ['Prmain.exe', 'Uonline.dll']
    
    for filename in files:
        if os.path.exists(filename):
            print(f"\n📁 分析 {filename}:")
            quick_pe_check(filename)
            find_timegettime(filename)
            find_24_hour_constants(filename)
        else:
            print(f"❌ {filename} 不存在")
    
    print("\n✅ 分析完成")

if __name__ == "__main__":
    main()
    input("按回车退出...")
