import numpy as np
import matplotlib.pyplot as plt
import os

# 设置工作目录
os.chdir(r"C:\Users\<USER>\Desktop\打三个")

def read_path(file_path):
    coordinates = []
    with open(file_path, 'r') as file:
        for line in file:
            x, y, z = map(float, line.strip().split(','))
            coordinates.append((x, y))  # 只使用 x 和 y 坐标
    return coordinates

def create_map(coordinates, resolution=0.5):
    x_coords, y_coords = zip(*coordinates)
    x_min, x_max = min(x_coords), max(x_coords)
    y_min, y_max = min(y_coords), max(y_coords)
    
    width = int((x_max - x_min) / resolution) + 1
    height = int((y_max - y_min) / resolution) + 1
    
    game_map = np.zeros((height, width), dtype=np.uint8)
    
    for x, y in coordinates:
        map_x = int((x - x_min) / resolution)
        map_y = int((y - y_min) / resolution)
        game_map[height - 1 - map_y, map_x] = 255
    
    return game_map, (x_min, y_min)

def visualize_map(game_map):
    plt.figure(figsize=(10, 10))
    plt.imshow(game_map, cmap='gray', interpolation='nearest')
    plt.title('游戏路径地图')
    plt.axis('off')
    plt.show()

# 主程序
path_file = "path.txt"
coordinates = read_path(path_file)
game_map, origin = create_map(coordinates)
visualize_map(game_map)

print(f"地图原点坐标: {origin}")
print("地图已显示")