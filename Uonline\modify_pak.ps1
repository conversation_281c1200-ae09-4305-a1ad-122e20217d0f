# PAK文件修改脚本
Write-Host "开始修改PAK文件..."

$pakFile = "item0001.pak"

if (-not (Test-Path $pakFile)) {
    Write-Host "文件不存在: $pakFile"
    exit
}

# 备份文件
$backup = "$pakFile.backup_ps1"
Copy-Item $pakFile $backup
Write-Host "备份创建: $backup"

# 读取文件为字节数组
$bytes = [System.IO.File]::ReadAllBytes($pakFile)
Write-Host "文件大小: $($bytes.Length) 字节"

# 查找HP+模式
$hpPattern = [System.Text.Encoding]::ASCII.GetBytes("HP+")
$count = 0
for ($i = 0; $i -lt ($bytes.Length - 3); $i++) {
    if ($bytes[$i] -eq $hpPattern[0] -and 
        $bytes[$i+1] -eq $hpPattern[1] -and 
        $bytes[$i+2] -eq $hpPattern[2]) {
        $count++
    }
}
Write-Host "找到 $count 个HP+模式"

# 简单替换：HP+100 -> HP+999
$oldPattern = [System.Text.Encoding]::ASCII.GetBytes("HP+100")
$newPattern = [System.Text.Encoding]::ASCII.GetBytes("HP+999")

$modifications = 0
for ($i = 0; $i -lt ($bytes.Length - 6); $i++) {
    $match = $true
    for ($j = 0; $j -lt $oldPattern.Length; $j++) {
        if ($bytes[$i + $j] -ne $oldPattern[$j]) {
            $match = $false
            break
        }
    }
    if ($match) {
        for ($j = 0; $j -lt $newPattern.Length; $j++) {
            $bytes[$i + $j] = $newPattern[$j]
        }
        $modifications++
        Write-Host "修改位置 $i : HP+100 -> HP+999"
    }
}

Write-Host "进行了 $modifications 处修改"

# 写入修改后的文件
[System.IO.File]::WriteAllBytes($pakFile, $bytes)
Write-Host "修改完成！"
